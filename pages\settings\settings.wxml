<!--settings.wxml-->
<view class="container {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}">
  <view class="settings-header">
    <view class="header-title">通用设置</view>
  </view>

  <view class="settings-list">

    <view class="settings-section">

      <view class="settings-item" bindtap="navigateToCompleteInfo">
        <view class="item-label">完善信息</view>
        <view class="item-arrow">></view>
      </view>
      
    </view>

    <view class="settings-section">
      <view class="settings-item">
        <view class="item-label">消息通知</view>
        <switch checked="{{notificationEnabled}}" bindchange="toggleNotification" color="#ff8c00"></switch>
      </view>
      <view class="settings-item">
        <view class="item-label">声音提醒</view>
        <switch checked="{{soundEnabled}}" bindchange="toggleSound" color="#ff8c00"></switch>
      </view>
      <view class="settings-item">
        <view class="item-label">暗黑模式</view>
        <switch checked="{{darkModeEnabled}}" bindchange="toggleDarkMode" color="#ff8c00"></switch>
      </view>
    </view>

    <view class="settings-section">
      <view class="settings-item">
        <view class="item-label">清除缓存</view>
        <view class="item-value" bindtap="clearCache">{{cacheSize}}</view>
      </view>
      <view class="settings-item" bindtap="checkUpdate">
        <view class="item-label">检查更新</view>
        <view class="item-arrow">></view>
      </view>
    </view>

    <view class="settings-section">
      <view class="settings-item" bindtap="navigateToPage" data-url="/pages/about/about">
        <view class="item-label">关于我们</view>
        <view class="item-arrow">></view>
      </view>
      <view class="settings-item" bindtap="showPrivacyPolicy">
        <view class="item-label">隐私政策</view>
        <view class="item-arrow">></view>
      </view>
      <view class="settings-item" bindtap="showUserAgreement">
        <view class="item-label">用户协议</view>
        <view class="item-arrow">></view>
      </view>
    </view>
  </view>
</view>
