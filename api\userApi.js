const REQUEST = require('../utils/request.js')

//用户端实名认证 /users-api/v1/auth/real-name
function submitRealNameAuth(authData) {
  // authData 包含以下字段:
  // {
  //     "residentName": "string",
  //     "certificateType": "string",
  //     "idCardNumber": "string",
  //     "phone": "16030446442",
  //     "codeKey": "string",
  //     "code": "string",
  //     "photo": "string"
  // }
  return REQUEST.request('/users-api/v1/member/real-name', 'POST', authData, true);
}
//完善个人信息
function supplementUserInfo(params) {

  return REQUEST.request('/users-api/v1/member/supplement', 'POST', params, true);
}


function getUserInfo()
{
  return REQUEST.request('/users-api/v1/member/info', 'POST', {}, true);
}


//注销用户
function logout()
{
  return REQUEST.request('/users-api/v1/member/logout', 'GET', {}, true);
}


module.exports = {
  submitRealNameAuth,
  supplementUserInfo,
  getUserInfo,
  logout
}


