<!--添加居民页面-->
<view class="container">
  <view class="form-container">
    <!-- 基本信息 -->
    <view class="form-section">
      <view class="section-title">基本信息</view>

      <!-- 姓名 -->
      <view class="form-item {{errors.name ? 'error' : ''}}">
        <view class="form-label">姓名</view>
        <input class="form-input" type="text" value="{{formData.name}}" data-field="name" bindinput="onInput" placeholder="请输入姓名" />
        <view class="error-message" wx:if="{{errors.name}}">{{errors.name}}</view>
      </view>

      <!-- 手机号 -->
      <view class="form-item {{errors.phone ? 'error' : ''}}">
        <view class="form-label">手机号</view>
        <input class="form-input" type="number" value="{{formData.phone}}" data-field="phone" bindinput="onInput" placeholder="请输入手机号" maxlength="11" />
        <view class="error-message" wx:if="{{errors.phone}}">{{errors.phone}}</view>
      </view>

      <!-- 身份证号 -->
      <view class="form-item {{errors.idNumber ? 'error' : ''}}">
        <view class="form-label">身份证号</view>
        <input class="form-input" type="idcard" value="{{formData.idNumber}}" data-field="idNumber" bindinput="onInput" placeholder="请输入身份证号" maxlength="18" />
        <view class="error-message" wx:if="{{errors.idNumber}}">{{errors.idNumber}}</view>
      </view>

      <!-- 性别 -->
      <view class="form-item">
        <view class="form-label">性别</view>
        <radio-group class="radio-group" bindchange="onGenderChange">
          <label class="radio"><radio value="男" checked="{{formData.gender === '男'}}"/>男</label>
          <label class="radio"><radio value="女" checked="{{formData.gender === '女'}}"/>女</label>
        </radio-group>
      </view>

      <!-- 出生日期 -->
      <view class="form-item {{errors.birthDate ? 'error' : ''}}">
        <view class="form-label">出生日期</view>
        <picker mode="date" value="{{formData.birthDate}}" bindchange="onBirthDateChange">
          <view class="picker-value">{{formData.birthDate || '请选择出生日期'}}</view>
        </picker>
        <view class="error-message" wx:if="{{errors.birthDate}}">{{errors.birthDate}}</view>
      </view>

      <!-- 居民类型 -->
      <view class="form-item">
        <view class="form-label">居民类型</view>
        <picker mode="selector" range="{{typeOptions}}" range-key="name" bindchange="onTypeChange">
          <view class="picker-value">
            <block wx:for="{{typeOptions}}" wx:key="id">
              <block wx:if="{{item.id === formData.type}}">{{item.name}}</block>
            </block>
          </view>
        </picker>
      </view>
    </view>

    <!-- 联系信息 -->
    <view class="form-section">
      <view class="section-title">联系信息</view>

      <!-- 邮箱 -->
      <view class="form-item {{errors.email ? 'error' : ''}}">
        <view class="form-label">邮箱</view>
        <input class="form-input" type="text" value="{{formData.email}}" data-field="email" bindinput="onInput" placeholder="请输入邮箱（选填）" />
        <view class="error-message" wx:if="{{errors.email}}">{{errors.email}}</view>
      </view>

      <!-- 紧急联系人 -->
      <view class="form-item">
        <view class="form-label">紧急联系人</view>
        <input class="form-input" type="text" value="{{formData.emergencyContact}}" data-field="emergencyContact" bindinput="onInput" placeholder="请输入紧急联系人（选填）" />
      </view>

      <!-- 紧急联系人电话 -->
      <view class="form-item {{errors.emergencyPhone ? 'error' : ''}}">
        <view class="form-label">紧急联系电话</view>
        <input class="form-input" type="number" value="{{formData.emergencyPhone}}" data-field="emergencyPhone" bindinput="onInput" placeholder="请输入紧急联系电话（选填）" maxlength="11" />
        <view class="error-message" wx:if="{{errors.emergencyPhone}}">{{errors.emergencyPhone}}</view>
      </view>
    </view>

    <!-- 房屋信息 -->
    <view class="form-section">
      <view class="section-title">房屋信息</view>

      <!-- 关联房屋 -->
      <view class="form-item {{errors.house ? 'error' : ''}}">
        <view class="form-label">关联房屋</view>
        <view class="house-selector" bindtap="showHouseSelector">
          <text class="house-value">{{formData.house || '请选择关联房屋'}}</text>
          <view class="selector-arrow"></view>
        </view>
        <view class="error-message" wx:if="{{errors.house}}">{{errors.house}}</view>
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-actions">
    <button class="btn-cancel" bindtap="cancelAdd">取消</button>
    <button class="btn-submit" bindtap="submitForm" disabled="{{submitting}}">确认</button>
  </view>

  <!-- 房屋选择器弹窗 -->
  <view class="modal {{showHouseSelector ? 'show' : ''}}" catchtouchmove="preventTouchMove">
    <view class="modal-mask" bindtap="hideHouseSelector"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">选择房屋</text>
      </view>
      <view class="modal-body">
        <scroll-view scroll-y="true" class="house-list">
          <view class="house-item" wx:for="{{houses}}" wx:key="id" bindtap="selectHouse" data-id="{{item.id}}" data-address="{{item.address}}">
            <text class="house-address">{{item.address}}</text>
            <view class="item-selected" wx:if="{{formData.houseId === item.id}}"></view>
          </view>
        </scroll-view>
      </view>
      <view class="modal-footer">
        <button class="btn-cancel" bindtap="hideHouseSelector">取消</button>
      </view>
    </view>
  </view>
</view>