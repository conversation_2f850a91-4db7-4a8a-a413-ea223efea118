<!--messages.wxml-->
<view class="container page-bottom-safe-area">

  <!-- 消息分类标签 -->
  <view class="message-tabs-wrapper">
    <!-- 加载状态 -->
    <view class="tabs-loading" wx:if="{{typesLoading}}">
      <view class="loading-text">正在加载通知类型...</view>
    </view>

    <!-- 标签容器 -->
    <scroll-view scroll-x class="message-tabs-container" wx:if="{{!typesLoading}}" enable-flex="{{true}}" show-scrollbar="{{false}}">
      <view class="message-tabs">
        <view class="message-tab {{currentTab === item.nameEn ? 'active' : ''}}"
              wx:for="{{noticeTypes}}"
              wx:key="id"
              bindtap="switchTab"
              data-tab="{{item.nameEn}}">
          {{item.nameCn}}
          <view class="tab-badge {{item.nameEn === 'emergency_notice' ? 'emergency' : ''}}"
          wx:if="{{unreadCounts[item.nameEn]> 0}}">
          {{unreadCounts[item.nameEn]}}
          </view>

        </view>
      </view>
    </scroll-view>

    <!-- 滚动提示图标 -->
    <!-- <view class="scroll-hint" wx:if="{{showScrollHint && !typesLoading}}" bindtap="onScrollHintTap">
      <text class="scroll-hint-icon">›</text>
    </view> -->
  </view>

  <!-- 消息内容区 -->
  <view class="message-content list-container">
    <!-- 消息加载状态 -->
    <view class="messages-loading" wx:if="{{messagesLoading}}">
      <view class="loading-text">正在加载消息...</view>
    </view>

    <!-- 动态消息面板 -->
    <view class="message-panel active" wx:if="{{!messagesLoading && currentTab}}">
      <!-- 有消息时显示列表 -->
      <view wx:if="{{messages[currentTab] && messages[currentTab].length > 0}}">
        <view class="message-item {{item.type === 'emergency_notice' ? 'emergency' : ''}}"
              wx:for="{{messages[currentTab]}}"
              wx:key="id"
              bindtap="showMessageDetail"
              data-message="{{item}}">
          <view class="message-icon">
            <icon class="iconfont {{item.type === 'property_notice' ? 'icon-property' : item.type === 'customer_message' ? 'icon-service' : item.type === 'community_notice' ? 'icon-community' : item.type === 'emergency_notice' ? 'icon-emergency' : 'icon-property'}}"></icon>
          </view>
          <view class="message-info">
            <view class="message-title">{{item.title}}</view>
            <view class="message-time">{{item.time}}</view>
          </view>
          <!-- 未读状态红点指示器 - 只在未读时显示 -->
          <view class="message-status unread" wx:if="{{!item.read}}"></view>
        </view>
      </view>

      <!-- 无消息时显示空状态 -->
      <view class="empty-messages" wx:else>
        <view class="empty-icon">📭</view>
        <view class="empty-text">暂无消息</view>
      </view>
    </view>
  </view>
</view>

<!-- 消息详情弹窗 -->
<view class="message-detail-modal {{showMessageModal ? 'show' : ''}}" bindtap="closeMessageModal">
  <view class="message-detail-content" catchtap="stopPropagation">
    <view class="message-detail-header">
      <view class="message-detail-icon">
        <icon class="iconfont {{currentMessageIcon}}"></icon>
      </view>
      <view class="message-detail-title-container">
        <view class="message-detail-title">{{currentMessage.title}}</view>
        <view class="message-detail-time">{{currentMessage.time}}</view>
      </view>
    </view>
    <view class="message-detail-body">
      <view class="message-detail-text">{{currentMessage.content}}</view>
    </view>
    <view class="message-detail-footer">
      <button class="message-detail-close-btn" bindtap="closeMessageModal">我知道了</button>
    </view>
  </view>
</view>
