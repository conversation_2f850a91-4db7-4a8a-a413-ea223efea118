// pages/property/staff/staff-stats.js
const util = require('../../../../utils/util.js')

Page({
  data: {
    darkMode: false,
    isLoading: true,
    
    // 统计数据
    totalStaff: 0,
    activeStaff: 0,
    inactiveStaff: 0,
    
    // 部门分布
    departmentData: [],
    
    // 性别比例
    genderData: [],
    
    // 年龄分布
    ageData: []
  },

  onLoad: function() {
    // 检查暗黑模式
    const app = getApp()
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      })
    }
    
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '员工统计'
    });
    
    // 加载统计数据
    this.loadStatsData();
  },
  
  // 加载统计数据
  loadStatsData: function() {
    this.setData({
      isLoading: true
    });
    
    // 模拟从服务器获取统计数据
    setTimeout(() => {
      // 模拟数据
      const statsData = {
        totalStaff: 28,
        activeStaff: 25,
        inactiveStaff: 3,
        
        departmentData: [
          { name: '物业管理部', value: 5, percentage: '17.9%' },
          { name: '客服部', value: 4, percentage: '14.3%' },
          { name: '工程部', value: 6, percentage: '21.4%' },
          { name: '保安部', value: 7, percentage: '25.0%' },
          { name: '保洁部', value: 3, percentage: '10.7%' },
          { name: '绿化部', value: 2, percentage: '7.1%' },
          { name: '财务部', value: 1, percentage: '3.6%' }
        ],
        
        genderData: [
          { name: '男', value: 16, percentage: '57.1%' },
          { name: '女', value: 12, percentage: '42.9%' }
        ],
        
        ageData: [
          { name: '20岁以下', value: 1, percentage: '3.6%' },
          { name: '20-30岁', value: 8, percentage: '28.6%' },
          { name: '30-40岁', value: 12, percentage: '42.9%' },
          { name: '40-50岁', value: 5, percentage: '17.9%' },
          { name: '50岁以上', value: 2, percentage: '7.1%' }
        ]
      };
      
      this.setData({
        totalStaff: statsData.totalStaff,
        activeStaff: statsData.activeStaff,
        inactiveStaff: statsData.inactiveStaff,
        departmentData: statsData.departmentData,
        genderData: statsData.genderData,
        ageData: statsData.ageData,
        isLoading: false
      });
    }, 1000);
  },
  
  // 返回员工列表
  goBack: function() {
    wx.navigateBack();
  },
  
  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadStatsData();
    wx.stopPullDownRefresh();
  }
})
