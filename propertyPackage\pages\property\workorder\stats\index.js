// pages/property/workorder/stats/index.js
const workOrderManager = require('@/utils/workorder-manager');

Page({
  data: {
    darkMode: false,
    isLoading: true,
    activeTab: 'overview', // 当前活动标签页：overview, type, efficiency, trend
    timeRange: 'week', // 时间范围：week, month, quarter, year

    // 统计数据
    statistics: {
      statusCounts: {
        total: 0,
        pending: 0,
        processing: 0,
        completed: 0,
        cancelled: 0
      },
      typeDistribution: {},
      trend: {
        labels: [],
        data: []
      },
      timeEfficiency: {
        avgResponseTime: '0h',
        avgProcessingTime: '0h',
        onTimeRate: '0%'
      }
    }
  },

  onLoad: function() {
    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }

    // 获取状态栏高度并设置CSS变量
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight;

    // 设置状态栏高度CSS变量
    wx.nextTick(() => {
      this.setData({
        ['--status-bar-height']: statusBarHeight + 'px'
      });
    });

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '工单统计分析'
    });

    // 加载统计数据
    this.loadStatistics();
  },

  onPullDownRefresh: function() {
    // 下拉刷新
    this.loadStatistics().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载统计数据
  loadStatistics: function() {
    this.setData({ isLoading: true });

    return workOrderManager.getOrderStatistics(this.data.timeRange)
      .then(statistics => {
        this.setData({
          statistics: statistics,
          isLoading: false
        });
      })
      .catch(error => {
        console.error('加载统计数据失败', error);
        this.setData({ isLoading: false });

        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },

  // 切换标签页
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ activeTab: tab });
  },

  // 切换时间范围
  switchTimeRange: function(e) {
    const range = e.currentTarget.dataset.range;

    if (range !== this.data.timeRange) {
      this.setData({
        timeRange: range,
        isLoading: true
      });

      // 重新加载统计数据
      this.loadStatistics();
    }
  },

  // 导出统计数据
  exportStatistics: function() {
    wx.showToast({
      title: '导出功能开发中',
      icon: 'none'
    });
  },

  // 计算完成率
  calculateCompletionRate: function() {
    const { completed, total } = this.data.statistics.statusCounts;
    if (total === 0) return 0;
    return Math.round((completed / total) * 100);
  },

  // 计算处理中率
  calculateProcessingRate: function() {
    const { processing, total } = this.data.statistics.statusCounts;
    if (total === 0) return 0;
    return Math.round((processing / total) * 100);
  },

  // 计算待处理率
  calculatePendingRate: function() {
    const { pending, total } = this.data.statistics.statusCounts;
    if (total === 0) return 0;
    return Math.round((pending / total) * 100);
  },

  // 计算取消率
  calculateCancelRate: function() {
    const { cancelled, total } = this.data.statistics.statusCounts;
    if (total === 0) return 0;
    return Math.round((cancelled / total) * 100);
  },

  // 获取类型分布数据
  getTypeDistributionData: function() {
    return Object.entries(this.data.statistics.typeDistribution).map(([type, data]) => ({
      type,
      name: data.name,
      percentage: data.percentage
    }));
  },

  // 导航返回
  navigateBack: function() {
    wx.navigateBack();
  }
});
