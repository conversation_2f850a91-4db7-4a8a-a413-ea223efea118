// 我的房屋页面逻辑
const app = getApp()
const houseApi = require('@/api/houseApi.js')
const util = require('@/utils/util.js')

Page({
  data: {
    houses: [],
    loading: true,

    // 新增房屋弹窗相关数据
    showAddHouseModal: false,
    currentStep: 'building', // 'building' | 'room'
    searchKeyword: '',
    searchPlaceholder: '搜索楼栋',

    // 楼栋和房间数据
    buildings: [],
    rooms: [],
    filteredBuildings: [],
    filteredRooms: [],

    // 选择状态
    selectedBuildingId: null,
    selectedBuildingName: '',
    selectedRoomId: null,
    selectedRoomName: '',
    selectedResidentType: '',

    // 字典数据
    residentTypeOptions: [],

    // 提交状态
    canSubmit: false,
    isSubmitting: false
  },

  onLoad: function (options) {
    // 获取URL参数
    this.fromPage = options.from || '';
  },

  onShow: function () {
    // 加载房屋列表
    this.loadHouses();
    // 加载字典数据
    this.loadResidentTypeDict();
  },


  // 加载房屋列表
  loadHouses: function () {
    this.setData({ loading: true })

    // 首先尝试从API获取房屋数据
    houseApi.getHouseList({
      pageNum: 1,
      pageSize: 100
    }).then(res => {
      console.log('房屋列表数据：', res)

      if (res.code === 0 && res.data && res.data.list) {

         
        var houseList = res.data.list.map(item => ({
          id: item.id,
          roomId: item.roomId,
          buildingNumber: item.buildingNumber,
          unitNumber: item.unitNumber || '',
          floorNumber: item.floorNumber || '',
          roomNumber: item.roomNumber || '',
          fullAddress: this.formatHouseAddress(item),
          area: item.area || 0,
          residentType: item.residentType || item.role || 'owner', // 优先使用residentType，兼容旧的role字段
          roleText: this.getRoleText(item.residentType || item.role || 'owner'),
          isDefault: item.isDefault || false,
          status: item.status , // 新增审核状态字段
          isVerified: item.isVerified || false, // 保留兼容性
          createTime: item.createTime,
          updateTime: item.updateTime
        }))

        // 按默认房屋优先排序
        houseList.sort((a, b) => {
          if (a.isDefault && !b.isDefault) return -1;
          if (!a.isDefault && b.isDefault) return 1;
          return 0;
        });

        // 处理车辆数据
        houseList = houseList.map(house => {
           
          return houseApi.formatHouseData(house);
        });

         
        this.setData({
          houses: houseList,
          loading: false
        })

        // 同时保存到本地存储作为备份
        wx.setStorageSync('my_houses', houseList)
      } else {
        console.error('获取房屋列表失败：', res)
        // 如果API失败，尝试从本地存储获取
        this.loadHousesFromLocal()
      }
    }).catch(err => {
      console.error('获取房屋列表异常：', err)
      // 如果API异常，尝试从本地存储获取
      this.loadHousesFromLocal()
    })
  },

  // 从本地存储加载房屋列表（备用方案）
  loadHousesFromLocal: function () {
    let houses = wx.getStorageSync('my_houses') || [];

    // 按默认房屋优先排序
    houses.sort((a, b) => {
      if (a.isDefault && !b.isDefault) return -1;
      if (!a.isDefault && b.isDefault) return 1;
      return 0;
    });

    // 添加角色文本和状态字段
    houses = houses.map(house => {
      return {
        ...house,
        residentType: house.residentType || house.role || 'owner', // 兼容旧字段
        roleText: this.getRoleText(house.residentType || house.role || 'owner'),
        status: house.status || '待审核' // 确保有状态字段
      };
    });

    this.setData({
      houses: houses,
      loading: false
    });
  },

  // 格式化房屋地址
  formatHouseAddress: function (houseItem) {
    const parts = []
    if (houseItem.buildingNumber) parts.push(houseItem.buildingNumber)
    if (houseItem.unitNumber) parts.push(houseItem.unitNumber)
    if (houseItem.floorNumber) parts.push(houseItem.floorNumber)
    if (houseItem.roomNumber) parts.push(houseItem.roomNumber)
    return parts.join('')
  },

  // 获取角色文本
  getRoleText: function (role) {
    switch (role) {
      case 'owner': return '业主';
      case 'tenant': return '租户';
      case 'family': return '家庭成员';
      default: return '未知';
    }
  },

  // 返回上一页
  goBack: function () {
    wx.navigateBack({
      fail: function() {
        // 如果返回失败，则跳转到个人中心页面
        wx.switchTab({
          url: '/pages/profile/profile'
        });
      }
    });
  },

  // 跳转到房屋详情页
  goToHouseDetail: function (e) {
    const id = e.currentTarget.dataset.id;
       
    wx.navigateTo({
      url: `./detail/detail?id=${id}`
    });
  },

  // 跳转到添加房屋页面
  goToAddHouse: function () {
    let url = '/profilePackage/pages/profile/house/add/add?mode=add';

    // 如果有来源页面，则传递参数
    if (this.fromPage) {
      url += `&from=${this.fromPage}`;
    }

    wx.navigateTo({
      url: url
    });
  },

  // 编辑房屋
  editHouse: function (e) {
    const id = e.currentTarget.dataset.id;

    // 跳转到编辑页面
    let url = `/profilePackage/pages/profile/house/add/add?mode=edit&id=${id}`;

    // 如果有来源页面，则传递参数
    if (this.fromPage) {
      url += `&from=${this.fromPage}`;
    }

    wx.navigateTo({
      url: url
    });
  },

  // ==================== 新增房屋弹窗相关方法 ====================

  // 显示新增房屋弹窗
  showAddHouseModal: function () {
    this.resetModalData();
    this.setData({
      showAddHouseModal: true
    });
    this.loadBuildings();
  },

  // 隐藏新增房屋弹窗
  hideAddHouseModal: function () {
    this.setData({
      showAddHouseModal: false
    });
    this.resetModalData();
  },

  // 重置弹窗数据
  resetModalData: function () {
    this.setData({
      currentStep: 'building',
      searchKeyword: '',
      searchPlaceholder: '搜索楼栋',
      selectedBuildingId: null,
      selectedBuildingName: '',
      selectedRoomId: null,
      selectedRoomName: '',
      selectedResidentType: '',
      buildings: [],
      rooms: [],
      filteredBuildings: [],
      filteredRooms: [],
      canSubmit: false,
      isSubmitting: false
    });
  },

  // 防止弹窗滚动穿透
  preventTouchMove: function () {
    return false;
  },

  // 加载住户身份字典
  loadResidentTypeDict: function () {
    try {
      const residentTypeDict = util.getDictByNameEn('resident_type');
      if (residentTypeDict && residentTypeDict.length > 0 && residentTypeDict[0].children) {
        this.setData({
          residentTypeOptions: residentTypeDict[0].children
        });
      } else {
        // 使用默认数据
        this.setData({
          residentTypeOptions: [
            { nameCn: '业主', nameEn: 'owner' },
            { nameCn: '租户', nameEn: 'tenant' },
            { nameCn: '家庭成员', nameEn: 'family' }
          ]
        });
      }
    } catch (error) {
      console.error('加载住户身份字典失败:', error);
      // 使用默认数据
      this.setData({
        residentTypeOptions: [
          { nameCn: '业主', nameEn: 'owner' },
          { nameCn: '租户', nameEn: 'tenant' },
          { nameCn: '家庭成员', nameEn: 'family' }
        ]
      });
    }
  },

  // 加载楼栋列表
  loadBuildings: function () {
    const communityId = wx.getStorageSync('selectedCommunity').id;
    const params = {
      pageNum: 1,
      pageSize: 500,
      communityId: communityId
    };

    houseApi.getBuildingsByCommunity(params)
      .then(res => {
        console.log('楼栋列表数据：', res);
        if (res.code === 0 && res.data && res.data.list) {
          this.setData({
            buildings: res.data.list,
            filteredBuildings: res.data.list
          });
        } else {
          wx.showToast({
            title: '获取楼栋列表失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('获取楼栋列表异常：', err);
        wx.showToast({
          title: '网络异常，请重试',
          icon: 'none'
        });
      });
  },

  // 加载房间列表
  loadRooms: function (buildingId) {
    houseApi.getRoomsByBuilding(buildingId)
      .then(res => {
        console.log('房间列表数据：', res);
        if (res.code === 0 && res.data && res.data.list) {
          this.setData({
            rooms: res.data.list,
            filteredRooms: res.data.list,
            currentStep: 'room',
            searchKeyword: '',
            searchPlaceholder: '搜索房间'
          });
        } else {
          wx.showToast({
            title: '获取房间列表失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('获取房间列表异常：', err);
        wx.showToast({
          title: '网络异常，请重试',
          icon: 'none'
        });
      });
  },

  // 搜索输入处理
  onSearchInput: function (e) {
    const keyword = e.detail.value.trim();
    this.setData({
      searchKeyword: keyword
    });

    if (this.data.currentStep === 'building') {
      this.filterBuildings(keyword);
    } else if (this.data.currentStep === 'room') {
      this.filterRooms(keyword);
    }
  },

  // 筛选楼栋
  filterBuildings: function (keyword) {
    const buildings = this.data.buildings;
    if (!keyword) {
      this.setData({
        filteredBuildings: buildings
      });
      return;
    }

    const filtered = buildings.filter(building =>
      building.buildingNumber && building.buildingNumber.includes(keyword)
    );

    this.setData({
      filteredBuildings: filtered
    });
  },

  // 筛选房间
  filterRooms: function (keyword) {
    const rooms = this.data.rooms;
    if (!keyword) {
      this.setData({
        filteredRooms: rooms
      });
      return;
    }

    const filtered = rooms.filter(room =>
      room.roomNumber && room.roomNumber.includes(keyword)
    );

    this.setData({
      filteredRooms: filtered
    });
  },

  // 选择楼栋
  selectBuilding: function (e) {
    const buildingId = e.currentTarget.dataset.id;
    const buildingName = e.currentTarget.dataset.name;

    this.setData({
      selectedBuildingId: buildingId,
      selectedBuildingName: buildingName,
      selectedRoomId: null,
      selectedRoomName: ''
    });

    this.checkCanSubmit();
    this.loadRooms(buildingId);
  },

  // 选择房间
  selectRoom: function (e) {
    const roomId = e.currentTarget.dataset.id;
    const roomName = e.currentTarget.dataset.name;

    this.setData({
      selectedRoomId: roomId,
      selectedRoomName: roomName
    });

    this.checkCanSubmit();
  },

  // 选择住户身份
  selectResidentType: function (e) {
    const residentType = e.currentTarget.dataset.type;

    this.setData({
      selectedResidentType: residentType
    });

    this.checkCanSubmit();
  },

  // 检查是否可以提交
  checkCanSubmit: function () {
    const canSubmit = this.data.selectedBuildingId &&
                     this.data.selectedRoomId &&
                     this.data.selectedResidentType;

    this.setData({
      canSubmit: canSubmit
    });
  },

  // 提交新增房屋
  submitAddHouse: function () {
    if (!this.data.canSubmit || this.data.isSubmitting) {
      return;
    }

    this.setData({
      isSubmitting: true
    });

    const roomData = {
      buildingId: this.data.selectedBuildingId,
      roomId: this.data.selectedRoomId,
      residentType: this.data.selectedResidentType
    };

    console.log('提交房屋数据：', roomData);

    houseApi.addHouse(roomData)
      .then(res => {
        console.log('新增房屋成功：', res);

        if (res.code === 0) {
          wx.showToast({
            title: '添加成功',
            icon: 'success'
          });

          this.hideAddHouseModal();
          this.loadHouses(); // 重新加载房屋列表
        } else {
          wx.showToast({
            title: res.errorMessage || '添加失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('新增房屋异常：', err);
        wx.showToast({
          title: '网络异常，请重试',
          icon: 'none'
        });
      })
      .finally(() => {
        this.setData({
          isSubmitting: false
        });
      });
  }
})
