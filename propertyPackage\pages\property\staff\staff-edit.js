// pages/property/staff/staff-edit.js
const util = require('../../../../utils/util.js')

Page({
  data: {
    darkMode: false,
    mode: 'add', // 'add' 或 'edit'
    staffId: '',
    isLoading: true,
    isSubmitting: false,

    // 表单数据
    name: '',
    gender: '',
    age: '',
    phone: '',
    idCard: '',
    employeeId: '',
    department: '',
    departmentIndex: -1,
    position: '',
    positionIndex: -1,
    entryDate: '',
    status: '在职',
    statusIndex: 0,
    email: '',
    address: '',
    emergencyContact: '',
    emergencyPhone: '',
    education: '',
    educationIndex: -1,
    major: '',
    skills: '',
    certificates: '',
    performance: '',
    performanceIndex: -1,
    salary: '',
    notes: '',
    employeeCardPhotoPath: '',
    facePhotoPath: '',

    // 表单验证状态
    nameValid: false,
    nameError: false,
    phoneValid: false,
    phoneError: false,
    idCardValid: false,
    idCardError: false,
    employeeIdValid: false,
    employeeIdError: false,
    departmentValid: false,
    departmentError: false,
    positionValid: false,
    positionError: false,

    // 选项数据
    genders: ['男', '女'],
    departments: ['物业管理部', '客服部', '工程部', '保安部', '保洁部', '绿化部', '财务部'],
    positions: ['经理', '主管', '工程师', '客服专员', '保安', '保洁员', '绿化工', '财务专员'],
    statuses: ['在职', '离职', '休假'],
    educations: ['初中', '高中', '中专', '大专', '本科', '硕士', '博士'],
    performances: ['优秀', '良好', '一般', '待改进'],

    errorMsg: ''
  },

  onLoad: function(options) {
    // 检查暗黑模式
    const app = getApp()
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      })
    }

    // 设置模式和员工ID
    if (options.mode) {
      this.setData({
        mode: options.mode
      });
    }

    if (options.id && options.mode === 'edit') {
      this.setData({
        staffId: options.id
      });

      // 设置导航栏标题
      wx.setNavigationBarTitle({
        title: '编辑员工'
      });

      // 加载员工数据
      this.loadStaffData();
    } else {
      // 设置导航栏标题
      wx.setNavigationBarTitle({
        title: '添加员工'
      });

      // 设置默认值
      this.setData({
        entryDate: this.formatDate(new Date()),
        isLoading: false
      });
    }
  },

  // 加载员工数据
  loadStaffData: function() {
    this.setData({
      isLoading: true
    });

    // 模拟从服务器获取员工详情
    setTimeout(() => {
      // 模拟数据
      const staffList = [
        {
          id: '001',
          name: '张三',
          gender: '男',
          age: 35,
          phone: '13800138001',
          idCard: '110101198505079876',
          employeeId: 'ZH001',
          department: '物业管理部',
          position: '经理',
          entryDate: '2020-01-15',
          employeeCardPhoto: '/images/default-avatar.svg',
          facePhoto: '/images/default-avatar.svg',
          status: '在职',
          email: '<EMAIL>',
          address: '北京市朝阳区建国路88号',
          emergencyContact: '李四',
          emergencyPhone: '13900139000',
          education: '本科',
          major: '物业管理',
          skills: '物业管理,客户服务,团队管理',
          certificates: '物业管理师证书',
          performance: '优秀',
          salary: '8000',
          notes: '工作认真负责，善于团队协作'
        },
        {
          id: '002',
          name: '李四',
          gender: '女',
          age: 28,
          phone: '13900139002',
          idCard: '110101199201234567',
          employeeId: 'ZH002',
          department: '客服部',
          position: '客服专员',
          entryDate: '2021-03-20',
          employeeCardPhoto: '/images/default-avatar.svg',
          facePhoto: '/images/default-avatar.svg',
          status: '在职',
          email: '<EMAIL>',
          address: '北京市海淀区中关村大街1号',
          emergencyContact: '王五',
          emergencyPhone: '13700137000',
          education: '大专',
          major: '市场营销',
          skills: '客户服务,沟通协调,问题解决',
          certificates: '客服专员证书',
          performance: '良好',
          salary: '6000',
          notes: '服务态度好，沟通能力强'
        },
        {
          id: '003',
          name: '王五',
          gender: '男',
          age: 42,
          phone: '13700137003',
          idCard: '110101197901234567',
          employeeId: 'ZH003',
          department: '工程部',
          position: '工程师',
          entryDate: '2019-05-10',
          employeeCardPhoto: '/images/default-avatar.svg',
          facePhoto: '/images/default-avatar.svg',
          status: '在职',
          email: '<EMAIL>',
          address: '北京市丰台区丰台路20号',
          emergencyContact: '赵六',
          emergencyPhone: '13600136000',
          education: '大专',
          major: '电气工程',
          skills: '电气维修,设备维护,故障排除',
          certificates: '电工证,安全生产证',
          performance: '优秀',
          salary: '7500',
          notes: '技术过硬，经验丰富'
        },
        {
          id: '004',
          name: '赵六',
          gender: '女',
          age: 31,
          phone: '13600136004',
          idCard: '110101199001234567',
          employeeId: 'ZH004',
          department: '财务部',
          position: '财务专员',
          entryDate: '2022-01-05',
          employeeCardPhoto: '/images/default-avatar.svg',
          facePhoto: '/images/default-avatar.svg',
          status: '在职',
          email: '<EMAIL>',
          address: '北京市西城区西长安街10号',
          emergencyContact: '张三',
          emergencyPhone: '13800138001',
          education: '本科',
          major: '会计学',
          skills: '财务管理,会计核算,预算编制',
          certificates: '会计师证书',
          performance: '良好',
          salary: '7000',
          notes: '工作细致，责任心强'
        },
        {
          id: '005',
          name: '钱七',
          gender: '男',
          age: 45,
          phone: '13500135005',
          idCard: '110101197601234567',
          employeeId: 'ZH005',
          department: '保安部',
          position: '主管',
          entryDate: '2018-08-18',
          employeeCardPhoto: '/images/default-avatar.svg',
          facePhoto: '/images/default-avatar.svg',
          status: '在职',
          email: '<EMAIL>',
          address: '北京市东城区东长安街15号',
          emergencyContact: '孙八',
          emergencyPhone: '13400134000',
          education: '高中',
          major: '安保管理',
          skills: '安全管理,团队协调,应急处理',
          certificates: '保安证',
          performance: '优秀',
          salary: '6500',
          notes: '工作认真负责，经验丰富'
        }
      ];

      // 查找对应ID的员工
      const staffInfo = staffList.find(staff => staff.id === this.data.staffId);

      if (staffInfo) {
        // 查找部门索引
        const departmentIndex = this.data.departments.findIndex(item => item === staffInfo.department);

        // 查找职位索引
        const positionIndex = this.data.positions.findIndex(item => item === staffInfo.position);

        // 查找状态索引
        const statusIndex = this.data.statuses.findIndex(item => item === staffInfo.status);

        // 查找学历索引
        const educationIndex = this.data.educations.findIndex(item => item === staffInfo.education);

        // 查找绩效索引
        const performanceIndex = this.data.performances.findIndex(item => item === staffInfo.performance);

        this.setData({
          name: staffInfo.name,
          gender: staffInfo.gender,
          age: staffInfo.age,
          phone: staffInfo.phone,
          idCard: staffInfo.idCard,
          employeeId: staffInfo.employeeId,
          department: staffInfo.department,
          departmentIndex: departmentIndex !== -1 ? departmentIndex : -1,
          position: staffInfo.position,
          positionIndex: positionIndex !== -1 ? positionIndex : -1,
          entryDate: staffInfo.entryDate,
          status: staffInfo.status,
          statusIndex: statusIndex !== -1 ? statusIndex : 0,
          email: staffInfo.email || '',
          address: staffInfo.address || '',
          emergencyContact: staffInfo.emergencyContact || '',
          emergencyPhone: staffInfo.emergencyPhone || '',
          education: staffInfo.education || '',
          educationIndex: educationIndex !== -1 ? educationIndex : -1,
          major: staffInfo.major || '',
          skills: staffInfo.skills || '',
          certificates: staffInfo.certificates || '',
          performance: staffInfo.performance || '',
          performanceIndex: performanceIndex !== -1 ? performanceIndex : -1,
          salary: staffInfo.salary || '',
          notes: staffInfo.notes || '',
          employeeCardPhotoPath: staffInfo.employeeCardPhoto,
          facePhotoPath: staffInfo.facePhoto,

          // 设置验证状态
          nameValid: true,
          phoneValid: true,
          idCardValid: true,
          employeeIdValid: true,
          departmentValid: true,
          positionValid: true,

          isLoading: false
        });
      } else {
        wx.showToast({
          title: '未找到员工信息',
          icon: 'none',
          duration: 2000,
          complete: () => {
            setTimeout(() => {
              wx.navigateBack();
            }, 2000);
          }
        });
      }
    }, 1000);
  },

  // 格式化日期
  formatDate: function(date) {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  // 输入姓名
  inputName: function(e) {
    this.setData({
      name: e.detail.value
    });
    this.validateName();
  },

  // 验证姓名
  validateName: function() {
    const name = this.data.name.trim();
    const isValid = name.length >= 2;
    this.setData({
      nameValid: isValid,
      nameError: name.length > 0 && !isValid
    });
    return isValid;
  },

  // 选择性别
  bindGenderChange: function(e) {
    this.setData({
      gender: this.data.genders[e.detail.value]
    });
  },

  // 输入年龄
  inputAge: function(e) {
    this.setData({
      age: e.detail.value
    });
  },

  // 输入手机号
  inputPhone: function(e) {
    this.setData({
      phone: e.detail.value
    });
    this.validatePhone();
  },

  // 验证手机号
  validatePhone: function() {
    const util = require('@/utils/util');
    const phone = this.data.phone.trim();
    const isValid = util.validatePhone(phone);
    this.setData({
      phoneValid: isValid,
      phoneError: phone.length > 0 && !isValid
    });
    return isValid;
  },

  // 输入身份证号
  inputIdCard: function(e) {
    this.setData({
      idCard: e.detail.value
    });
    this.validateIdCard();
  },

  // 验证身份证号
  validateIdCard: function() {
    const util = require('@/utils/util');
    const idCard = this.data.idCard.trim();
    const isValid = util.validateIdCard(idCard);
    this.setData({
      idCardValid: isValid,
      idCardError: idCard.length > 0 && !isValid
    });
    return isValid;
  },

  // 输入员工编号
  inputEmployeeId: function(e) {
    this.setData({
      employeeId: e.detail.value
    });
    this.validateEmployeeId();
  },

  // 验证员工编号
  validateEmployeeId: function() {
    const employeeId = this.data.employeeId.trim();
    const isValid = employeeId.length >= 2;
    this.setData({
      employeeIdValid: isValid,
      employeeIdError: employeeId.length > 0 && !isValid
    });
    return isValid;
  },

  // 选择部门
  bindDepartmentChange: function(e) {
    const index = e.detail.value;
    this.setData({
      department: this.data.departments[index],
      departmentIndex: index,
      departmentValid: true,
      departmentError: false
    });
  },

  // 选择职位
  bindPositionChange: function(e) {
    const index = e.detail.value;
    this.setData({
      position: this.data.positions[index],
      positionIndex: index,
      positionValid: true,
      positionError: false
    });
  },

  // 选择入职日期
  bindEntryDateChange: function(e) {
    this.setData({
      entryDate: e.detail.value
    });
  },

  // 选择状态
  bindStatusChange: function(e) {
    const index = e.detail.value;
    this.setData({
      status: this.data.statuses[index],
      statusIndex: index
    });
  },

  // 输入邮箱
  inputEmail: function(e) {
    this.setData({
      email: e.detail.value
    });
  },

  // 输入地址
  inputAddress: function(e) {
    this.setData({
      address: e.detail.value
    });
  },

  // 输入紧急联系人
  inputEmergencyContact: function(e) {
    this.setData({
      emergencyContact: e.detail.value
    });
  },

  // 输入紧急联系电话
  inputEmergencyPhone: function(e) {
    this.setData({
      emergencyPhone: e.detail.value
    });
  },

  // 选择学历
  bindEducationChange: function(e) {
    const index = e.detail.value;
    this.setData({
      education: this.data.educations[index],
      educationIndex: index
    });
  },

  // 输入专业
  inputMajor: function(e) {
    this.setData({
      major: e.detail.value
    });
  },

  // 输入技能
  inputSkills: function(e) {
    this.setData({
      skills: e.detail.value
    });
  },

  // 输入证书
  inputCertificates: function(e) {
    this.setData({
      certificates: e.detail.value
    });
  },

  // 选择绩效
  bindPerformanceChange: function(e) {
    const index = e.detail.value;
    this.setData({
      performance: this.data.performances[index],
      performanceIndex: index
    });
  },

  // 输入薪资
  inputSalary: function(e) {
    this.setData({
      salary: e.detail.value
    });
  },

  // 输入备注
  inputNotes: function(e) {
    this.setData({
      notes: e.detail.value
    });
  },

  // 上传工作证照片
  uploadEmployeeCard: function() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 获取图片临时路径
        const tempFilePath = res.tempFilePaths[0];

        // 显示上传中
        wx.showLoading({
          title: '上传中...',
        });

        // 模拟上传过程
        setTimeout(() => {
          this.setData({
            employeeCardPhotoPath: tempFilePath
          });
          wx.hideLoading();
        }, 1000);
      }
    });
  },

  // 上传人脸照片
  uploadFacePhoto: function() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 获取图片临时路径
        const tempFilePath = res.tempFilePaths[0];

        // 显示上传中
        wx.showLoading({
          title: '上传中...',
        });

        // 模拟上传过程
        setTimeout(() => {
          this.setData({
            facePhotoPath: tempFilePath
          });
          wx.hideLoading();
        }, 1000);
      }
    });
  },

  // 提交表单
  submitForm: function() {
    // 验证表单
    let isValid = true;

    // 验证姓名
    if (!this.validateName()) {
      isValid = false;
    }

    // 验证手机号
    if (!this.validatePhone()) {
      isValid = false;
    }

    // 验证身份证号
    if (!this.validateIdCard()) {
      isValid = false;
    }

    // 验证员工编号
    if (!this.validateEmployeeId()) {
      isValid = false;
    }

    // 验证部门
    if (!this.data.department) {
      this.setData({
        departmentError: true
      });
      isValid = false;
    }

    // 验证职位
    if (!this.data.position) {
      this.setData({
        positionError: true
      });
      isValid = false;
    }

    // 验证工作证照片
    if (!this.data.employeeCardPhotoPath) {
      this.showError('请上传工作证照片');
      isValid = false;
    }

    if (!isValid) {
      return;
    }

    // 开始提交
    this.setData({
      isSubmitting: true
    });

    // 模拟提交
    setTimeout(() => {
      this.setData({
        isSubmitting: false
      });

      // 显示成功提示
      wx.showToast({
        title: this.data.mode === 'add' ? '添加成功' : '更新成功',
        icon: 'success',
        duration: 2000,
        complete: () => {
          // 设置需要刷新员工列表和详情的标志
          wx.setStorageSync('staffDataNeedRefresh', true);
          if (this.data.mode === 'edit') {
            wx.setStorageSync('staffDetailNeedRefresh', true);
          }

          // 返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 2000);
        }
      });
    }, 2000);
  },

  // 显示错误信息
  showError: function(msg) {
    this.setData({
      errorMsg: msg
    });
    setTimeout(() => {
      this.setData({
        errorMsg: ''
      });
    }, 3000);
  },

  // 取消编辑
  cancelEdit: function() {
    wx.navigateBack();
  }
})
