// 我的租客页面
const app = getApp()
const util = require('../../../../utils/util.js')
const familyApi = require('../../../../api/familyApi.js')
const houseApi = require('../../../../api/houseApi.js')



Page({
  data: {
    familyMembers: [],
    loading: false,
    showActionMenu: false,
    actionMenuTop: 0,
    actionMenuLeft: 0,
    currentFamilyId: '',
    showModal: false,
    isEditing: false,
    isSaving: false,
    formData: {
      id: '',
      name: '',
      phone: '',
      idCardNumber: '',
      certificateType: '',
      house: '',
      houseId: '',
      roomId: '',
      residentType:'tenant'
    },
    showHousePicker: false,
    housePickerValue: [0],
    houses: [],
    showDeleteConfirm: false,
    // 证件类型相关
    certificateTypes: [],
    showCertificateTypePicker: false,
    certificateTypePickerValue: [0],
    // 分页相关
    pageNum: 1,
    pageSize: 10,
    hasMore: true
  },

  onLoad: function (options) {
    // 加载证件类型字典
    this.loadCertificateTypes()

    // 加载房屋列表
    this.loadHouses()
  },

  // 加载证件类型字典
  loadCertificateTypes: function () {
    try {
      const certificateTypeDict = util.getDictByNameEn('certificate_type');
      if (certificateTypeDict && certificateTypeDict.length > 0 && certificateTypeDict[0].children) {
        this.setData({
          certificateTypes: certificateTypeDict[0].children
        });
      } else {
        // 使用默认数据
        this.setData({
          certificateTypes: [
            { nameCn: '身份证', nameEn: 'id_card' },
            { nameCn: '护照', nameEn: 'passport' },
            { nameCn: '港澳通行证', nameEn: 'hk_macao_pass' },
            { nameCn: '台湾通行证', nameEn: 'taiwan_pass' }
          ]
        });
      }
    } catch (error) {
      console.error('加载证件类型字典失败:', error);
      // 使用默认数据
      this.setData({
        certificateTypes: [
          { nameCn: '身份证', nameEn: 'id_card' },
          { nameCn: '护照', nameEn: 'passport' },
          { nameCn: '港澳通行证', nameEn: 'hk_macao_pass' },
          { nameCn: '台湾通行证', nameEn: 'taiwan_pass' }
        ]
      });
    }
  },

  // 加载租客列表
  loadFamilyMembers: function (refresh = false) {
    if (refresh) {
      this.setData({
        pageNum: 1,
        familyMembers: [],
        hasMore: true
      })
    }

    if (this.data.loading || !this.data.hasMore) {
      return
    }

    this.setData({ loading: true })

    var params = {
      pageNum: this.data.pageNum,
      pageSize: this.data.pageSize,
      communityId: wx.getStorageSync('selectedCommunity').id,
      residentType:'tenant'
    }
    familyApi.getFamilyList(params).then(res => {
       
      console.log('租客列表数据：', res)
       
      if (res.code === 0 && res.data && res.data.list) {


         
        const newMembers = res.data.list.map(member => {
          // 获取名字首字母
          const nameInitial = member.residentName ? member.residentName.charAt(0) : '?'

          // 格式化房屋信息
          const house = this.formatHouseInfo(member)

          return {
            id: member.id,
            name: member.residentName,
            phone: member.phone,
            idCardNumber: member.idCardNumber,
            certificateType: member.certificateType,
            roomId: member.roomId,
            residentId: member.residentId,
            nameInitial,
            house,
            relationText: '租客', // 简化为统一的家属关系
            relationClass: 'relation-tenant'
          }
        })

        const allMembers = refresh ? newMembers : [...this.data.familyMembers, ...newMembers]

        this.setData({
          familyMembers: allMembers,
          pageNum: this.data.pageNum + 1,
          hasMore: newMembers.length === this.data.pageSize,
          loading: false
        })

        wx.setStorageSync('tenant_members', allMembers)
      } else{
        this.setData({
          
          loading: false
        })

      }




    }).catch(err => {
       
      console.error('获取租客列表异常：', err)
      this.setData({ loading: false })
      wx.showToast({
        title: '获取租客列表异常，请重试',
        icon: 'none'
      })
    })
  },

  // 格式化房屋信息
  formatHouseInfo: function (member) {
    if (!member.roomId) {
      return ''
    }

    // 从房屋列表中查找对应的房屋信息
    const house = this.data.houses.find(h => h.roomId === member.roomId)
    if (house) {
      return house.fullAddress || house.address || `房间${member.roomId}`
    }

    return `房间${member.roomId}`
  },

  // 加载房屋列表
  loadHouses: function () {
    houseApi.getHouseList().then(res => {
      console.log('房屋列表数据：', res)

      if (res.code === 0 && res.data && Array.isArray(res.data.list)) {
        const houses = res.data.list.map(house => ({
          id: house.id,
          roomId: house.roomId,
          fullAddress: this.formatHouseAddress(house),
          address: this.formatHouseAddress(house),
          isDefault: house.isDefault || false
        }))

        // 按默认房屋优先排序
        const sortedHouses = houses.sort((a, b) => {
          if (a.isDefault && !b.isDefault) return -1
          if (!a.isDefault && b.isDefault) return 1
          return 0
        })

        this.setData({
          houses: sortedHouses
        })


        // 加载租客列表
        this.loadFamilyMembers()



      } else {
        console.error('获取房屋列表失败：', res)

      }
    }).catch(err => {
      console.error('获取房屋列表异常：', err)

    })
  },

  // 格式化房屋地址
  formatHouseAddress: function (house) {
    const parts = []
    if (house.buildingNumber) parts.push(house.buildingNumber)
    if (house.unitNumber) parts.push(house.unitNumber)
    if (house.roomNumber) parts.push(house.roomNumber)
    return parts.join(' ') || `房间${house.roomId}`
  },


  // 显示操作菜单
  showActionMenu: function (e) {
    const id = e.currentTarget.dataset.id

    // 获取点击位置信息
    const { clientY } = e.touches ? e.touches[0] : e

    // 只设置菜单的垂直位置，水平位置由CSS控制
    const top = clientY

    this.setData({
      showActionMenu: true,
      actionMenuTop: top,
      currentFamilyId: id
    })
  },

  // 隐藏操作菜单
  hideActionMenu: function () {
    this.setData({
      showActionMenu: false
    })
  },

  // 编辑租客信息
  editFamilyMember: function () {
    const id = this.data.currentFamilyId
    const member = this.data.familyMembers.find(m => m.id === id)

    if (member) {
      // 查找房屋在列表中的索引
      const houseIndex = this.data.houses.findIndex(h => h.roomId === member.roomId)

      // 如果找到对应房屋，使用房屋列表中的地址信息；否则使用成员原有的房屋信息
      let houseDisplayText = member.house || ''
      let houseId = member.houseId || ''
      let roomId = member.roomId || ''

      if (houseIndex > -1) {
        const foundHouse = this.data.houses[houseIndex]
        houseDisplayText = foundHouse.fullAddress || foundHouse.address
        houseId = foundHouse.id
        roomId = foundHouse.roomId
      }
       
      this.setData({
        isEditing: true,
        formData: {
          id: member.id,
          name: member.name,
          phone: member.phone || '',
          idCardNumber: member.idCardNumber || '',
          certificateType: member.certificateType || 'id_card',
          house: houseDisplayText,
          houseId: houseId,
          roomId: roomId,
          residentId: member.residentId || '',
          residentType:'tenant'
        },
        
        housePickerValue: [houseIndex > -1 ? houseIndex : 0],
        showActionMenu: false,
        showModal: true
      })
    }
  },

  // 显示删除确认弹窗
  showDeleteConfirm: function () {
    this.setData({
      showActionMenu: false,
      showDeleteConfirm: true
    })
  },

  // 隐藏删除确认弹窗
  hideDeleteConfirm: function () {
    this.setData({
      showDeleteConfirm: false
    })
  },

  // 删除租客信息
  deleteFamilyMember: function () {
    const id = this.data.currentFamilyId

    wx.showLoading({
      title: '删除中...'
    })

    familyApi.deleteFamily(id).then(res => {
      wx.hideLoading()

      if (res.code === 0) {
        // 从列表中移除
        const familyMembers = this.data.familyMembers.filter(m => m.id !== id)

        this.setData({
          familyMembers,
          showDeleteConfirm: false
        })

        wx.showToast({
          title: '删除成功',
          icon: 'success'
        })
      } else {
        console.error('删除租客失败：', res)
        wx.showToast({
          title: res.errorMessage || '删除失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      wx.hideLoading()
      console.error('删除租客异常：', err)
      wx.showToast({
        title: '删除租客异常，请重试',
        icon: 'none'
      })
    })
  },

  // 显示添加租客弹窗
  showAddFamilyModal: function () {
    // 设置默认房屋选择（选择第一个房屋）
    let defaultHouse = ''
    let defaultHouseId = ''
    let defaultRoomId = ''
    let defaultPickerValue = [0]

    if (this.data.houses && this.data.houses.length > 0) {
      const firstHouse = this.data.houses[0]
      defaultHouse = firstHouse.fullAddress || firstHouse.address
      defaultHouseId = firstHouse.id
      defaultRoomId = firstHouse.roomId
    }

    // 设置默认证件类型（选择第一个证件类型）
    let defaultCertificateType = ''
    let defaultCertificateTypePickerValue = [0]

    if (this.data.certificateTypes && this.data.certificateTypes.length > 0) {
      defaultCertificateType = this.data.certificateTypes[0].nameEn
    }

    this.setData({
      isEditing: false,
      formData: {
        id: '',
        name: '',
        phone: '',
        idCardNumber: '',
        certificateType: defaultCertificateType,
        house: defaultHouse,
        houseId: defaultHouseId,
        roomId: defaultRoomId
      },
      housePickerValue: defaultPickerValue,
      certificateTypePickerValue: defaultCertificateTypePickerValue,
      showModal: true
    })
  },

  // 关闭弹窗
  closeModal: function () {
    this.setData({
      showModal: false,
      showImportPanel: false,
      showCertificateTypePicker: false
    })
  },

  // 显示证件类型选择器
  showCertificateTypePicker: function () {
    this.setData({
      showCertificateTypePicker: true
    })
  },

  // 隐藏证件类型选择器
  hideCertificateTypePicker: function () {
    this.setData({
      showCertificateTypePicker: false
    })
  },

  // 证件类型选择变化
  onCertificateTypeChange: function (e) {
    const index = e.detail.value[0]
    const certificateTypes = this.data.certificateTypes

    if (index >= 0 && index < certificateTypes.length) {
      const selectedType = certificateTypes[index]
      this.setData({
        'formData.certificateType': selectedType.nameEn,
        certificateTypePickerValue: [index],
        showCertificateTypePicker: false
      })
    }
  },

  // 防止滑动穿透
  preventTouchMove: function () {
    return false
  },

  // 阻止事件冒泡
  stopPropagation: function () {
    // 阻止事件冒泡
  },

  // 表单输入处理
  onNameInput: function (e) {
    this.setData({
      'formData.name': e.detail.value
    })
  },

  onPhoneInput: function (e) {
    this.setData({
      'formData.phone': e.detail.value
    })
  },

  onIdCardInput: function (e) {
    this.setData({
      'formData.idCardNumber': e.detail.value
    })
  },



  // 显示房屋选择器
  showHousePicker: function () {
    if (this.data.houses.length === 0) {
      wx.showToast({
        title: '暂无房屋信息',
        icon: 'none'
      })
      return
    }

    this.setData({
      showHousePicker: true
    })
  },

  // 隐藏房屋选择器
  hideHousePicker: function () {
    // 确保当前选中的房屋被保存到formData中
    const currentIndex = this.data.housePickerValue[0] || 0
    const houses = this.data.houses

    if (houses && houses.length > 0 && currentIndex < houses.length) {
      const selectedHouse = houses[currentIndex]
      this.setData({
        'formData.houseId': selectedHouse.id,
        'formData.roomId': selectedHouse.roomId,
        'formData.house': selectedHouse.fullAddress || selectedHouse.address
      })
    }

    this.setData({
      showHousePicker: false
    })
  },

  // 房屋选择变化
  onHouseChange: function (e) {
    const index = e.detail.value[0]
    const house = this.data.houses[index]
    this.setData({
      'formData.houseId': house.id,
      'formData.roomId': house.roomId,
      'formData.house': house.fullAddress || house.address,
      housePickerValue: [index]
    })
  },



  // 保存租客信息
  saveFamilyMember: function () {
    // 表单验证
    if (!this.data.formData.name) {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      })
      return
    }

    // 设置保存中状态
    this.setData({
      isSaving: true
    })

    const formData = this.data.formData
    const familyData = {
      residentName: formData.name,
      phone: formData.phone,
      idCardNumber: formData.idCardNumber,
      certificateType: formData.certificateType,
      roomId: formData.roomId || null,
      residentType:'tenant'
    }
     
    let apiCall
    if (this.data.isEditing)
     {
        
      // 编辑模式
      familyData.id = formData.id
      familyData.familyResidentId = formData.residentId
      
      apiCall = familyApi.updateFamily(familyData)
    } else {
      // 新增模式
      apiCall = familyApi.addFamily(familyData)
    }

    apiCall.then(res => {
      console.log('保存租客信息结果：', res)

      if (res.code === 0) {
        // 重新加载租客列表
        this.loadFamilyMembers(true)

        // 关闭弹窗
        this.closeModal()

        wx.showToast({
          title: this.data.isEditing ? '更新成功' : '添加成功',
          icon: 'success'
        })
      } else {
        console.error('保存租客信息失败：', res)
        wx.showToast({
          title: res.errorMessage || '保存失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error('保存租客信息异常：', err)
      wx.showToast({
        title: '保存租客信息失败',
        icon: 'none'
      })
    }).finally(() => {
      // 重置保存状态
      this.setData({
        isSaving: false
      })
    })
  },

  // 下拉刷新
  onPullDownRefresh: function () {
     
    this.loadFamilyMembers(true)
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  // 上拉加载更多
  onReachBottom: function () {
    this.loadFamilyMembers()
  }
})
