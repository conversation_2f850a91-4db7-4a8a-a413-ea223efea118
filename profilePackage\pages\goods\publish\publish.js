// 发布商品页
const util = require('@/utils/util.js')
const commApi = require('@/api/commApi.js')
const goodsApi = require('@/api/goods.js')
const app = getApp()

Page({
  data: {
    apiUrl:wx.getStorageSync('apiUrl')+'/common-api/v1/file/',
    darkMode: false,
    isEdit: false, // 是否是编辑模式
    goodsId: null, // 编辑时的商品ID
    isAgreed: false, // 是否同意发布须知
    formData: {
      stuffDescribe: '', // 商品描述
      amount: '', // 价格
      categoryCode: '', // 分类代码
      type: '', // 商品类型
      stock: '1', // 库存
      media: [], // 图片数组
      address: '', // 交易地点
      points: '0' // 积分（保留但隐藏）
    },
    // 字典数据
    typeOptions: [], // 好物类型字典
    categoryOptions: [], // 好物分类字典
    typeIndex: 0,
    categoryIndex: -1,
    selectedCategoryName: ''
  },

  onLoad: function(options) {
    // 检查是否已认证
    if (!util.checkAuthentication()) {
      util.showAuthModal()
      return
    }
/**
 * 计算两个数字的和
 * @param {number} a - 第一个加数
 * @param {number} b - 第二个加数
 * @returns {number} 两个参数的和
 */

    // 加载字典数据
    this.loadDictionaries()

    // 检查是否是编辑模式
    if (options.id) {
      this.setData({
        isEdit: true,
        goodsId: options.id
      })
      this.loadGoodsDetail(options.id)
    }
  },

  onShow: function() {
    // 检查暗黑模式
    this.setData({
      darkMode: app.globalData.darkMode || false
    })

    // 监听暗黑模式变化
    if (app.globalData.darkModeChangeEvent) {
      this.setData({
        darkMode: app.globalData.darkModeChangeEvent.darkMode
      })
    }
  },

  // 加载字典数据
  loadDictionaries: function() {
    try {
      // 使用统一的字典获取方法
      const typeDict = util.getDictByNameEn('good_stuff_type');
      const categoryDict = util.getDictByNameEn('good_stuff_category');

      this.setData({
        typeOptions: typeDict && typeDict.length > 0 && typeDict[0].children ? typeDict[0].children : [],
        categoryOptions: categoryDict && categoryDict.length > 0 && categoryDict[0].children ? categoryDict[0].children : []
      });

      console.log('发布页字典数据加载完成:', {
        typeOptions: this.data.typeOptions,
        categoryOptions: this.data.categoryOptions
      });
    } catch (error) {
      console.error('加载发布页字典数据失败:', error);
      this.setData({
        typeOptions: [],
        categoryOptions: []
      });
    }
  },


  // 加载商品详情（编辑模式）
  loadGoodsDetail: function(id) {
    wx.showLoading({
      title: '加载中...',
      mask: true
    })

    // 使用我的商品详情接口
    goodsApi.getPlatformGoodsDetail(id).then(res => {
      if (res.code === 0 && res.data) {
        const goods = res.data

        // 处理图片数据
        let media = []
        if (goods.media && typeof goods.media === 'string') {
          media = goods.media.split(',').map(img => img.trim()).filter(img => img)
        }

        // 设置表单数据
        const formData = {
          stuffDescribe: goods.stuffDescribe || '',
          amount: goods.amount ? goods.amount.toString() : '',
          categoryCode: goods.categoryCode || '',
          type: goods.type || 'free',
          stock: goods.stock ? goods.stock.toString() : '1',
          media: media,
          address: goods.address || '',
          points: goods.points ? goods.points.toString() : '0'
        }

        // 设置选择器索引
        const typeIndex = this.data.typeOptions.findIndex(item => item.nameEn === goods.type)
        const categoryIndex = this.data.categoryOptions.findIndex(item => item.nameEn === goods.categoryCode)

        this.setData({
          formData,
          typeIndex: typeIndex !== -1 ? typeIndex : 0,
          categoryIndex: categoryIndex !== -1 ? categoryIndex : -1,
          selectedCategoryName: categoryIndex !== -1 ? this.data.categoryOptions[categoryIndex].nameCn : ''
        })
      } else {
        throw new Error(res.message || '获取商品详情失败');
      }
    }).catch(err => {
      console.error('加载商品详情失败:', err);
      wx.showToast({
        title: err.message || '加载失败',
        icon: 'none'
      });
    }).finally(() => {
      wx.hideLoading()
    })
  },

  // 选择图片
  chooseImage: function() {
    wx.chooseMedia({
      count: 9 - this.data.formData.media.length,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 上传图片
        this.uploadImages(res.tempFiles)
      }
    })
  },

  // 上传图片
  uploadImages: function(tempFiles) {
    if (!tempFiles || tempFiles.length === 0) return

    const uploadPromises = tempFiles.map(file => {
      return commApi.upLoadFile(file.tempFilePath)
    })

    Promise.all(uploadPromises)
      .then(results => {
        // 提取上传成功的图片URL
        const urls = results.map(result => result.data || '').filter(url => url)

        // 更新图片列表
        this.setData({
          'formData.media': [...this.data.formData.media, ...urls]
        })
      })
      .catch(err => {
        console.error('图片上传失败:', err)
        wx.showToast({
          title: '图片上传失败',
          icon: 'none'
        })
      })
  },

  // 删除图片
  deleteImage: function(e) {
    const index = e.currentTarget.dataset.index
    const media = [...this.data.formData.media]
    media.splice(index, 1)

    this.setData({
      'formData.media': media
    })
  },

  // 输入标题
  inputTitle: function(e) {
    this.setData({
      'formData.title': e.detail.value
    })
  },

  // 输入描述
  inputDescription: function(e) {
    this.setData({
      'formData.stuffDescribe': e.detail.value
    })
  },

  // 选择商品类型
  selectType: function(e) {
    const type = e.currentTarget.dataset.type

    this.setData({
      'formData.type': type
    })

    // 如果选择免费送，清空价格字段
    if (type === 'free') {
      this.setData({
        'formData.amount': ''
      })
    }
  },

  // 选择分类
  categoryChange: function(e) {
    const index = e.detail.value
    const selectedCategory = this.data.categoryOptions[index]

    if (selectedCategory) {
      this.setData({
        categoryIndex: index,
        'formData.categoryCode': selectedCategory.nameEn,
        selectedCategoryName: selectedCategory.nameCn
      })
    }
  },

  // 库存步进器
  increaseStock: function() {
    let stock = parseInt(this.data.formData.stock) || 1
    stock += 1

    this.setData({
      'formData.stock': stock.toString()
    })
  },

  decreaseStock: function() {
    let stock = parseInt(this.data.formData.stock) || 1
    if (stock > 1) {
      stock -= 1

      this.setData({
        'formData.stock': stock.toString()
      })
    }
  },

  // 选择位置
  chooseLocation: function() {
     
    wx.chooseLocation({
      latitude:  31,
      longitude: 120,
      success: (res) => {
         
        this.setData({
          'formData.address': res.name || res.address
        })
      }
    })
  },

  // 联系方式多选方法已删除

  // 联系方式相关方法已删除

  // 同意发布须知
  agreeChange: function(e) {
    this.setData({
      isAgreed: e.detail.value.includes('agree')
    })
  },

  // 输入价格
  inputPrice: function(e) {
    var amount= e.detail.value>9999999999?9999999999: e.detail.value
    this.setData({
      'formData.amount': amount
    })
  },

  // 输入库存
  inputStock: function(e) {
    var stock= e.detail.value>9999?9999: e.detail.value
    this.setData({
      'formData.stock':stock
    })
  },


  // 输入交易地点
  inputAddress: function(e) {
     
    this.setData({
      'formData.address': e.detail.value
    })
  },

  // 输入积分
  inputPoints: function(e) {
    this.setData({
      'formData.points': e.detail.value
    })
  },

  // 联系方式选择相关方法已删除

  // 积分相关方法已删除（积分设置已隐藏）

  // 取消发布
  cancelPublish: function() {
    wx.navigateBack()
  },

  // 提交表单
  submitForm: function() {
    // 验证表单
    if (!this.validateForm()) {
      return false
    }

    // 验证是否同意发布须知
    if (!this.data.isAgreed) {
      wx.showToast({
        title: '请阅读并同意发布规则和交易条款',
        icon: 'none'
      })
      return false
    }

    wx.showLoading({
      title: this.data.isEdit ? '保存中...' : '发布中...',
      mask: true
    })

    // 构建请求数据
    const formData = this.data.formData

    // 获取用户位置信息（从全局数据或用户定位）
    const userInfo = wx.getStorageSync('userInfo') || {}
    const communityInfo = wx.getStorageSync('selectedCommunity') || {}

    const requestData = {
      stuffDescribe: formData.stuffDescribe,
      categoryCode: formData.categoryCode,
      type: formData.type,
      amount: formData.type !== 'free' ? parseFloat(formData.amount) || 0 : 0,
      stock: parseInt(formData.stock) || 1,
      points: parseInt(formData.points) || 0,
      media: formData.media.join(','),
      lng: communityInfo.lng || 0.1,
      lat: communityInfo.lat || 0.1,
      address: formData.address || communityInfo.address || ''
    }

    // 调用API
    const apiCall = this.data.isEdit ?
      goodsApi.updateMyGoods({ ...requestData, id: this.data.goodsId }) :
      goodsApi.addMyGoods(requestData)

    apiCall.then(res => {
      if (res.code === 0 || res.code === 200) {
        wx.showToast({
          title: this.data.isEdit ? '保存成功' : '发布成功',
          icon: 'success'
        })

        // 返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        wx.showToast({
          title: res.message || (this.data.isEdit ? '保存失败' : '发布失败'),
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error('提交失败:', err)
      wx.showToast({
        title: this.data.isEdit ? '保存失败' : '发布失败',
        icon: 'none'
      })
    }).finally(() => {
      wx.hideLoading()
    })
  },



  // 表单验证
  validateForm: function() {
    const formData = this.data.formData

    // 验证图片
    if (formData.media.length === 0) {
      wx.showToast({
        title: '请上传至少一张商品图片',
        icon: 'none'
      })
      return false
    }

    // 验证描述
    if (!formData.stuffDescribe.trim()) {
      wx.showToast({
        title: '请输入商品描述',
        icon: 'none'
      })
      return false
    }

    // 验证分类
    if (!formData.categoryCode) {
      wx.showToast({
        title: '请选择商品分类',
        icon: 'none'
      })
      return false
    }

    // 验证类型
    if (!formData.type) {
      wx.showToast({
        title: '请选择商品类型',
        icon: 'none'
      })
      return false
    }

    // 验证价格（非免费送）
    if (formData.type !== 'free') {
      if (!formData.amount) {
        wx.showToast({
          title: '请输入商品价格',
          icon: 'none'
        })
        return false
      }

      if (isNaN(parseFloat(formData.amount)) || parseFloat(formData.amount) < 0) {
        wx.showToast({
          title: '请输入有效的商品价格',
          icon: 'none'
        })
        return false
      }
    }

    // 验证库存
    if (!formData.stock || isNaN(parseInt(formData.stock)) || parseInt(formData.stock) <= 0) {
      wx.showToast({
        title: '请输入有效的库存数量',
        icon: 'none'
      })
      return false
    }

    // 验证交易地点
    if (!formData.address.trim()) {
      wx.showToast({
        title: '请输入交易地点',
        icon: 'none'
      })
      return false
    }

    // 联系方式验证已删除

    return true
  }
})
