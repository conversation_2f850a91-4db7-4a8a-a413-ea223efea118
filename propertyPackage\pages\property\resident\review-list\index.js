// pages/property/resident/review-list/index.js
const util = require('../../../../../utils/util.js')

Page({
  data: {
    activeTab: 'all', // 当前激活的标签：all, identity, house, vehicle
    activeTabName: '全部', // 当前激活的标签名称
    searchText: '', // 搜索文本
    reviews: [], // 审核列表
    hasMore: false, // 是否有更多数据
    isLoading: false, // 是否正在加载
    showRejectModal: false, // 是否显示拒绝原因弹窗
    showApproveModal: false, // 是否显示通过确认弹窗
    rejectReason: '', // 拒绝原因
    currentReviewId: null, // 当前操作的审核ID


  },

  onLoad: function() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '信息审核'
    });

    // 加载审核列表
    this.loadReviews();
  },

  // 加载审核列表
  loadReviews: function() {
    this.setData({ isLoading: true });

    // 模拟加载数据
    setTimeout(() => {
      // 模拟审核数据
      const reviews = [
        {
          id: 1,
          type: 'identity',
          title: '实名认证审核',
          applicant: '张三',
          phone: '138****1234',
          idNumber: '410******1234',
          submitTime: '2023-10-15 14:30',
          status: 'pending',
          statusText: '待审核',
          iconClass: 'icon-identity',
          style: ''
        },
        {
          id: 2,
          type: 'house',
          title: '房屋信息审核',
          applicant: '李四',
          phone: '139****5678',
          address: '3栋2单元501室',
          submitTime: '2023-10-14 10:15',
          status: 'pending',
          statusText: '待审核',
          iconClass: 'icon-house',
          style: ''
        },
        {
          id: 3,
          type: 'vehicle',
          title: '车辆信息审核',
          applicant: '王五',
          phone: '137****9012',
          plateNumber: '京A·12345',
          submitTime: '2023-10-13 16:45',
          status: 'pending',
          statusText: '待审核',
          iconClass: 'icon-vehicle',
          style: ''
        },
        {
          id: 4,
          type: 'identity',
          title: '实名认证审核',
          applicant: '赵六',
          phone: '136****3456',
          idNumber: '320******5678',
          submitTime: '2023-10-12 09:30',
          status: 'approved',
          statusText: '已通过',
          iconClass: 'icon-identity',
          style: ''
        },
        {
          id: 5,
          type: 'house',
          title: '房屋信息审核',
          applicant: '钱七',
          phone: '135****7890',
          address: '5栋1单元302室',
          submitTime: '2023-10-11 11:20',
          status: 'rejected',
          statusText: '已拒绝',
          iconClass: 'icon-house',
          style: ''
        }
      ];

      // 根据当前标签筛选数据
      let filteredReviews = reviews;
      if (this.data.activeTab !== 'all') {
        filteredReviews = reviews.filter(review => review.type === this.data.activeTab);
      }

      // 根据搜索文本筛选
      if (this.data.searchText) {
        const searchText = this.data.searchText.toLowerCase();
        filteredReviews = filteredReviews.filter(review =>
          review.applicant.toLowerCase().includes(searchText) ||
          review.phone.includes(searchText) ||
          (review.idNumber && review.idNumber.includes(searchText)) ||
          (review.address && review.address.toLowerCase().includes(searchText)) ||
          (review.plateNumber && review.plateNumber.toLowerCase().includes(searchText))
        );
      }

      this.setData({
        reviews: filteredReviews,
        hasMore: false,
        isLoading: false
      });
    }, 500);
  },

  // 切换标签
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    let tabName = '全部';

    switch(tab) {
      case 'identity':
        tabName = '实名认证';
        break;
      case 'house':
        tabName = '房屋信息';
        break;
      case 'vehicle':
        tabName = '车辆信息';
        break;
      default:
        tabName = '全部';
    }

    this.setData({
      activeTab: tab,
      activeTabName: tabName
    });

    // 重新加载审核列表
    this.loadReviews();
  },

  // 搜索输入
  onSearchInput: function(e) {
    this.setData({
      searchText: e.detail.value
    });
  },

  // 清除搜索
  clearSearch: function() {
    this.setData({
      searchText: ''
    });

    // 重新加载审核列表
    this.loadReviews();
  },

  // 显示筛选
  showFilter: function() {
    wx.showToast({
      title: '筛选功能开发中',
      icon: 'none'
    });
  },

  // 查看详情
  viewDetail: function(e) {
    console.log(e)
    const { id, type } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/propertyPackage/pages/property/resident/review-detail/index?id=${id}&type=${type}`
    });
  },



  // 显示拒绝原因弹窗
  showRejectModal: function(e) {
    const id = e.currentTarget.dataset.id;
    this.setData({
      showRejectModal: true,
      currentReviewId: id,
      rejectReason: ''
    });
  },

  // 显示通过确认弹窗
  showApproveModal: function(e) {
    const id = e.currentTarget.dataset.id;
    this.setData({
      showApproveModal: true,
      currentReviewId: id
    });
  },

  // 隐藏弹窗
  hideModal: function() {
    this.setData({
      showRejectModal: false,
      showApproveModal: false
    });
  },

  // 输入拒绝原因
  inputRejectReason: function(e) {
    this.setData({
      rejectReason: e.detail.value
    });
  },

  // 确认拒绝
  confirmReject: function() {
    if (!this.data.rejectReason.trim()) {
      wx.showToast({
        title: '请输入拒绝原因',
        icon: 'none'
      });
      return;
    }

    // 模拟拒绝操作
    const reviews = this.data.reviews;
    const index = reviews.findIndex(review => review.id === this.data.currentReviewId);

    if (index !== -1) {
      reviews[index].status = 'rejected';
      reviews[index].statusText = '已拒绝';

      this.setData({
        reviews: reviews,
        showRejectModal: false
      });

      wx.showToast({
        title: '已拒绝审核',
        icon: 'success'
      });
    }
  },

  // 确认通过
  confirmApprove: function() {
    // 模拟通过操作
    const reviews = this.data.reviews;
    const index = reviews.findIndex(review => review.id === this.data.currentReviewId);

    if (index !== -1) {
      reviews[index].status = 'approved';
      reviews[index].statusText = '已通过';

      this.setData({
        reviews: reviews,
        showApproveModal: false
      });

      wx.showToast({
        title: '已通过审核',
        icon: 'success'
      });
    }
  },

  // 加载更多
  loadMore: function() {
    if (this.data.hasMore && !this.data.isLoading) {
      this.loadReviews();
    }
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    // 重新加载审核列表
    this.loadReviews();

    // 停止下拉刷新
    wx.stopPullDownRefresh();
  },

  // 阻止滑动穿透
  preventTouchMove: function() {
    return false;
  }
})
