const REQUEST = require('../utils/request.js')

//文件上传
function upLoadFile(filePath, formData = {}) {
  const apiUrl = 'http://**********:8080'; // 从request.js获取

  let headerGet = {
    "Content-Type": "multipart/form-data",
    'Authorization': wx.getStorageSync('access_token')
  }

  wx.showLoading({
    title: '上传中',
  });

  return new Promise((resolve, reject) => {
    // 本地测试环境，图片上传为非必填项
    if (!filePath) {
      wx.hideLoading();
      resolve({ data: '', message: '本地测试环境，图片上传已跳过' });
      return;
    }

    wx.uploadFile({
      url: apiUrl + "/common-api/v1/file/upload",
      method: 'POST',
      header: headerGet,
      filePath: filePath,
      name: 'file',
      formData: formData,
      success: res => {
        wx.hideLoading();
          
        try {
           
          var jj = JSON.parse(res.data);
          let code = jj.code
          switch (code) {
            case 0:
              resolve(jj);
              break;
            case 200:
              resolve(jj);
              break
            case 503:
            case 500:
              wx.showModal({
                title: '错误提示',
                content: '上传图片失败',
                showCancel: false,
              })
              reject(jj);
              break
            default:
              reject(jj)
          }
        } catch (error) {
          console.log('解析上传结果失败', error);
          reject(error);
        }
      },
      fail: function (error) {
        console.log('上传失败', error)
        wx.hideLoading();
        // 本地测试环境，不显示上传失败提示
        console.log('本地测试环境，图片上传失败已忽略');
        resolve({ data: '', message: '本地测试环境，图片上传失败已忽略' });
      }
    });
  })
}

//获取smscode验证码
function getSmsCode(phone) {
  // 通过手机号获取验证码 /common-api/v1/sms/code post 参数为phone
  return REQUEST.request('/common-api/v1/sms/code?phone='+phone, 'POST', {  }, true);
}


//查询字典
function getDictByNameEn(nameEn) {
  // 通过字典名称查询字典 /users-api/v1/dict/search get 参数为nameEn
  //返回结果示例
  // {
  //   "errorMessage": null,
  //   "code": 0,
  //   "data": [
  //     {
  //       "id": "141",
  //       "nameEn": "property_notice",
  //       "nameCn": "物业公告",
  //       "cssClass": null,
  //       "createTime": "2025-05-26 09:28:44",
  //       "updateTime": "2025-05-27 14:48:28",
  //       "parentId": "140",
  //       "sort": 0,
  //       "note": null
  //     },
  //     {
  //       "id": "150",
  //       "nameEn": "customer_message",
  //       "nameCn": "客服消息",
  //       "cssClass": null,
  //       "createTime": "2025-05-27 14:48:52",
  //       "updateTime": null,
  //       "parentId": "140",
  //       "sort": 0,
  //       "note": null
  //     },
  //     {
  //       "id": "151",
  //       "nameEn": "community_notice",
  //       "nameCn": "社区公告",
  //       "cssClass": null,
  //       "createTime": "2025-05-27 14:49:21",
  //       "updateTime": null,
  //       "parentId": "140",
  //       "sort": 0,
  //       "note": null
  //     },
  //     {
  //       "id": "152",
  //       "nameEn": "emergency_notice",
  //       "nameCn": "紧急通告",
  //       "cssClass": null,
  //       "createTime": "2025-05-27 14:49:36",
  //       "updateTime": null,
  //       "parentId": "140",
  //       "sort": 0,
  //       "note": null
  //     }
  //   ]
  // }


  return REQUEST.request('/users-api/v1/dict/search?nameEn='+nameEn, 'GET', {  }, true);
}


//获取所有字典
function getAllDict()
{ 
  var params={pageNum:1,pageSize:500}
  
  return REQUEST.request('/users-api/v1/dict/page', 'GET', params, true);
}

module.exports = {
	upLoadFile,
	getSmsCode,
  getDictByNameEn,
  getAllDict
}