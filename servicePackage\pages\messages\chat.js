// 聊天页面
const dateUtil = require('../../../utils/dateUtil.js')
const app = getApp()

Page({
  data: {
    targetId: '', // 聊天对象ID
    targetName: '', // 聊天对象名称
    targetAvatar: '', // 聊天对象头像
    goodsId: '', // 商品ID（如果是商品相关的聊天）
    goodsInfo: null, // 商品信息
    inputContent: '', // 输入框内容
    messages: [], // 聊天消息列表
    scrollIntoView: '', // 滚动到指定消息
    userInfo: {
      id: '0', // 当前用户ID
      name: '您', // 当前用户名称
      avatar: 'https://img.freepik.com/free-photo/young-bearded-man-with-striped-shirt_273609-5677.jpg' // 当前用户头像
    }
  },

  onLoad: function(options) {
    // 获取参数
    const { targetId, targetName, goodsId, targetAvatar, goodsTitle, goodsImage } = options

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: targetName || '聊天'
    })

    this.setData({
      targetId: targetId || '1',
      targetName: targetName || '对方',
      targetAvatar: targetAvatar ? decodeURIComponent(targetAvatar) : '/images/default-avatar.svg',
      goodsId: goodsId || ''
    })

    // 如果有商品ID，加载商品信息
    if (goodsId) {
      // 如果传入了商品标题和图片，直接使用
      if (goodsTitle && goodsImage) {
        this.setData({
          goodsInfo: {
            id: goodsId,
            title: decodeURIComponent(goodsTitle),
            price: '',  // 价格可能需要从商品详情中获取
            image: decodeURIComponent(goodsImage)
          }
        });
      } else {
        // 否则加载商品信息
        this.loadGoodsInfo(goodsId);
      }
    }

    // 加载聊天记录
    this.loadChatHistory()
  },

  onShow: function() {
    // 滚动到最新消息
    this.scrollToBottom()
  },

  // 加载商品信息
  loadGoodsInfo: function(goodsId) {
    // 模拟加载商品信息
    this.setData({
      goodsInfo: {
        id: goodsId,
        title: '全新iPhone 13 Pro Max 256GB 远峰蓝',
        price: '6999.00',
        image: 'https://img.freepik.com/free-photo/smartphone-balancing-with-blue-background_23-2150271746.jpg'
      }
    })
  },

  // 加载聊天记录
  loadChatHistory: function() {
    // 模拟聊天记录数据 - 以卖家口吻回复
    const messages = [
      {
        id: 'msg1',
        senderId: '0', // 自己ID (买家)
        content: '您好，请问这个商品还在出售吗？',
        time: '10:30',
        timestamp: new Date().getTime() - 3600000 // 1小时前
      },
      {
        id: 'msg2',
        senderId: '1', // 对方ID (卖家)
        content: '您好，是的，商品还在售，成色很新，几乎没有使用痕迹。',
        time: '10:32',
        timestamp: new Date().getTime() - 3500000
      },
      {
        id: 'msg3',
        senderId: '0', // 买家
        content: '价格可以再便宜一点吗？',
        time: '10:35',
        timestamp: new Date().getTime() - 3400000
      },
      {
        id: 'msg4',
        senderId: '1', // 卖家
        content: '可以考虑，您想出多少合适？这个是去年买的，用了不到一个月，配件都很齐全。',
        time: '10:40',
        timestamp: new Date().getTime() - 3300000
      }
    ]

    this.setData({
      messages
    })

    // 滚动到最新消息
    this.scrollToBottom()
  },

  // 处理输入框内容变化
  handleInputChange: function(e) {
    this.setData({
      inputContent: e.detail.value
    })
  },

  // 发送消息
  sendMessage: function() {
    const { inputContent, messages, userInfo } = this.data

    // 如果输入为空，不发送
    if (!inputContent.trim()) {
      return
    }

    // 创建新消息
    const newMessage = {
      id: 'msg' + (messages.length + 1),
      senderId: userInfo.id,
      content: inputContent,
      time: dateUtil.formatTime(new Date()).substring(11, 16), // 只取时分
      timestamp: new Date().getTime()
    }

    // 添加到消息列表
    const updatedMessages = [...messages, newMessage]

    this.setData({
      messages: updatedMessages,
      inputContent: '' // 清空输入框
    })

    // 滚动到最新消息
    this.scrollToBottom()

    // 模拟对方回复
    this.simulateReply()
  },

  // 模拟对方(卖家)回复
  simulateReply: function() {
    const { messages, targetId, targetName } = this.data

    // 延迟1-3秒回复
    const delay = Math.floor(Math.random() * 2000) + 1000

    setTimeout(() => {
      // 随机回复内容 - 卖家口吻
      const replies = [
        '好的，没问题，您什么时候方便交易？',
        '可以的，这个价格我能接受',
        '商品确实很新，您可以放心购买',
        '我可以送货上门，不过需要加收配送费',
        '这个是正品，有发票和包装盒',
        '如果您今天下单，我可以给您再优惠一点',
        '您可以先看看实物再决定，我可以发更多图片给您'
      ]

      const replyContent = replies[Math.floor(Math.random() * replies.length)]

      // 创建回复消息
      const replyMessage = {
        id: 'msg' + (messages.length + 1),
        senderId: targetId,
        content: replyContent,
        time: dateUtil.formatTime(new Date()).substring(11, 16), // 只取时分
        timestamp: new Date().getTime()
      }

      // 添加到消息列表
      const updatedMessages = [...messages, replyMessage]

      this.setData({
        messages: updatedMessages
      })

      // 滚动到最新消息
      this.scrollToBottom()
    }, delay)
  },

  // 滚动到最新消息
  scrollToBottom: function() {
    const { messages } = this.data

    if (messages.length > 0) {
      this.setData({
        scrollIntoView: 'msg' + messages.length
      })
    }
  },

  // 点击商品卡片
  handleGoodsTap: function() {
    const { goodsId } = this.data

    if (goodsId) {
      wx.navigateTo({
        url: `/pages/goods/detail/detail?id=${goodsId}`
      })
    }
  },

  // 发送图片
  sendImage: function() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0]

        // 模拟发送图片消息
        const { messages, userInfo } = this.data

        // 创建新消息
        const newMessage = {
          id: 'msg' + (messages.length + 1),
          senderId: userInfo.id,
          content: tempFilePath,
          isImage: true,
          time: dateUtil.formatTime(new Date()).substring(11, 16), // 只取时分
          timestamp: new Date().getTime()
        }

        // 添加到消息列表
        const updatedMessages = [...messages, newMessage]

        this.setData({
          messages: updatedMessages
        })

        // 滚动到最新消息
        this.scrollToBottom()
      }
    })
  },

  // 预览图片
  previewImage: function(e) {
    const { url } = e.currentTarget.dataset

    wx.previewImage({
      current: url,
      urls: [url]
    })
  }
})
