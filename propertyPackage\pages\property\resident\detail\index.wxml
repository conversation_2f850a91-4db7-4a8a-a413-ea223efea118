<!--居民详情页-->
<view class="container">
  <!-- 顶部操作栏 -->
  <view class="action-bar">
    <block wx:if="{{!isEditing}}">
      <button class="btn-edit" bindtap="enterEditMode">编辑</button>
    </block>
    <block wx:else>
      <button class="btn-cancel" bindtap="cancelEdit">取消</button>
      <button class="btn-save" bindtap="saveEdit" disabled="{{submitting}}">保存</button>
    </block>
  </view>

  <!-- 标签页导航 -->
  <view class="tab-nav">
    <view class="tab {{activeTab === 'basic' ? 'active' : ''}}" bindtap="switchTab" data-tab="basic">基本信息</view>
    <view class="tab {{activeTab === 'house' ? 'active' : ''}}" bindtap="switchTab" data-tab="house">房屋信息</view>
    <view class="tab {{activeTab === 'vehicle' ? 'active' : ''}}" bindtap="switchTab" data-tab="vehicle">车辆信息</view>
    <view class="tab {{activeTab === 'record' ? 'active' : ''}}" bindtap="switchTab" data-tab="record">操作记录</view>
  </view>

  <!-- 基本信息标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'basic'}}">
    <view class="detail-card">
      <view class="resident-header">
        <view class="resident-name">{{residentData.name}}</view>
        <view class="resident-badges">
          <view class="badge {{residentData.type}}">{{residentData.typeText}}</view>
          <view class="badge {{residentData.status}}">{{residentData.statusText}}</view>
        </view>
      </view>

      <view class="info-list">
        <!-- 非编辑模式 -->
        <block wx:if="{{!isEditing}}">
          <view class="info-item">
            <view class="info-label">手机号</view>
            <view class="info-value">{{residentData.phone}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">身份证</view>
            <view class="info-value">{{residentData.idNumber}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">性别</view>
            <view class="info-value">{{residentData.gender}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">出生日期</view>
            <view class="info-value">{{residentData.birthDate}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">年龄</view>
            <view class="info-value">{{residentData.age}}岁</view>
          </view>
          <view class="info-item">
            <view class="info-label">邮箱</view>
            <view class="info-value">{{residentData.email}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">紧急联系人</view>
            <view class="info-value">{{residentData.emergencyContact}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">紧急电话</view>
            <view class="info-value">{{residentData.emergencyPhone}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">注册时间</view>
            <view class="info-value">{{residentData.registerTime}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">最后更新</view>
            <view class="info-value">{{residentData.lastUpdateTime}}</view>
          </view>
        </block>

        <!-- 编辑模式 -->
        <block wx:else>
          <view class="info-item">
            <view class="info-label">手机号</view>
            <input class="info-input" type="number" value="{{editData.phone}}" data-field="phone" bindinput="onEditInput" placeholder="请输入手机号" />
          </view>
          <view class="info-item">
            <view class="info-label">身份证</view>
            <input class="info-input" type="idcard" value="{{editData.idNumber}}" data-field="idNumber" bindinput="onEditInput" placeholder="请输入身份证号" />
          </view>
          <view class="info-item">
            <view class="info-label">性别</view>
            <radio-group class="radio-group" data-field="gender" bindchange="onEditInput">
              <label class="radio"><radio value="男" checked="{{editData.gender === '男'}}"/>男</label>
              <label class="radio"><radio value="女" checked="{{editData.gender === '女'}}"/>女</label>
            </radio-group>
          </view>
          <view class="info-item">
            <view class="info-label">出生日期</view>
            <picker mode="date" value="{{editData.birthDate}}" data-field="birthDate" bindchange="onEditInput">
              <view class="picker-value">{{editData.birthDate || '请选择出生日期'}}</view>
            </picker>
          </view>
          <view class="info-item">
            <view class="info-label">邮箱</view>
            <input class="info-input" type="text" value="{{editData.email}}" data-field="email" bindinput="onEditInput" placeholder="请输入邮箱" />
          </view>
          <view class="info-item">
            <view class="info-label">紧急联系人</view>
            <input class="info-input" type="text" value="{{editData.emergencyContact}}" data-field="emergencyContact" bindinput="onEditInput" placeholder="请输入紧急联系人" />
          </view>
          <view class="info-item">
            <view class="info-label">紧急电话</view>
            <input class="info-input" type="number" value="{{editData.emergencyPhone}}" data-field="emergencyPhone" bindinput="onEditInput" placeholder="请输入紧急联系电话" />
          </view>
        </block>
      </view>
    </view>
  </view>

  <!-- 房屋信息标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'house'}}">
    <view class="detail-card">
      <view class="card-header">
        <view class="card-title">关联房屋</view>
        <view class="card-action" bindtap="addHouse">添加</view>
      </view>

      <block wx:if="{{residentData.houses && residentData.houses.length > 0}}">
        <view class="house-list">
          <view class="house-item" wx:for="{{residentData.houses}}" wx:key="id" bindtap="viewHouseDetail" data-id="{{item.id}}">
            <view class="house-info">
              <view class="house-address">{{item.address}}</view>
              <view class="house-details">
                <text>{{item.type}} | {{item.area}}</text>
                <text class="house-relation">{{item.relation}}</text>
              </view>
              <view class="house-date">入住时间: {{item.startDate}}</view>
            </view>
            <view class="house-arrow"></view>
          </view>
        </view>
      </block>

      <block wx:else>
        <view class="empty-state">
          <view class="empty-icon"></view>
          <view class="empty-text">暂无关联房屋</view>
        </view>
      </block>
    </view>
  </view>

  <!-- 车辆信息标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'vehicle'}}">
    <view class="detail-card">
      <view class="card-header">
        <view class="card-title">关联车辆</view>
        <view class="card-action" bindtap="addVehicle">添加</view>
      </view>

      <block wx:if="{{residentData.vehicles && residentData.vehicles.length > 0}}">
        <view class="vehicle-list">
          <view class="vehicle-item" wx:for="{{residentData.vehicles}}" wx:key="id" bindtap="viewVehicleDetail" data-id="{{item.id}}">
            <view class="vehicle-info">
              <view class="vehicle-plate">{{item.plateNumber}}</view>
              <view class="vehicle-details">
                <text>{{item.brand}} {{item.model}} | {{item.color}}</text>
              </view>
              <view class="vehicle-parking">车位: {{item.parkingSpace}}</view>
            </view>
            <view class="vehicle-arrow"></view>
          </view>
        </view>
      </block>

      <block wx:else>
        <view class="empty-state">
          <view class="empty-icon"></view>
          <view class="empty-text">暂无关联车辆</view>
        </view>
      </block>
    </view>
  </view>

  <!-- 操作记录标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'record'}}">
    <view class="detail-card">
      <view class="card-title">操作记录</view>

      <block wx:if="{{residentData.records && residentData.records.length > 0}}">
        <view class="record-list">
          <view class="record-item" wx:for="{{residentData.records}}" wx:key="id">
            <view class="record-icon {{item.type}}"></view>
            <view class="record-info">
              <view class="record-title">{{item.title}}</view>
              <view class="record-details">
                <text>{{item.time}}</text>
                <text class="record-operator">操作人: {{item.operator}}</text>
              </view>
            </view>
          </view>
        </view>
      </block>

      <block wx:else>
        <view class="empty-state">
          <view class="empty-icon"></view>
          <view class="empty-text">暂无操作记录</view>
        </view>
      </block>
    </view>
  </view>

  <!-- 底部操作区 -->
  <view class="bottom-actions" wx:if="{{!isEditing}}">
    <button class="btn-contact" bindtap="showContactModal">联系居民</button>
    <button class="btn-notify" bindtap="showNotifyModal">发送通知</button>
  </view>

  <!-- 联系方式弹窗 -->
  <view class="modal {{showContactModal ? 'show' : ''}}" catchtouchmove="preventTouchMove">
    <view class="modal-mask" bindtap="hideContactModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">联系方式</text>
      </view>
      <view class="modal-body">
        <view class="contact-item" bindtap="makePhoneCall">
          <view class="contact-icon phone"></view>
          <view class="contact-text">拨打电话</view>
        </view>
        <view class="contact-item" bindtap="sendSMS">
          <view class="contact-icon sms"></view>
          <view class="contact-text">发送短信</view>
        </view>
      </view>
      <view class="modal-footer">
        <button class="btn-cancel" bindtap="hideContactModal">取消</button>
      </view>
    </view>
  </view>

  <!-- 发送通知弹窗 -->
  <view class="modal {{showNotifyModal ? 'show' : ''}}" catchtouchmove="preventTouchMove">
    <view class="modal-mask" bindtap="hideNotifyModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">发送通知</text>
      </view>
      <view class="modal-body">
        <textarea class="notify-content" placeholder="请输入通知内容..." value="{{notifyContent}}" bindinput="inputNotifyContent"></textarea>
      </view>
      <view class="modal-footer">
        <button class="btn-cancel" bindtap="hideNotifyModal">取消</button>
        <button class="btn-confirm" bindtap="sendNotification" disabled="{{submitting}}">发送</button>
      </view>
    </view>
  </view>
</view>