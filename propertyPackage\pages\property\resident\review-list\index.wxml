<!--居民信息审核列表-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrap">
      <view class="search-icon"></view>
      <input class="search-input" type="text" placeholder="搜索申请人、ID、手机号等" value="{{searchText}}" bindinput="onSearchInput" confirm-type="search" />
      <view class="clear-icon" bindtap="clearSearch" wx:if="{{searchText}}"></view>
    </view>
    <view class="filter-btn" bindtap="showFilter">
      <view class="filter-icon"></view>
    </view>
  </view>

  <!-- 分类标签 -->
  <view class="category-tabs">
    <view class="tab {{activeTab === 'all' ? 'active' : ''}}" bindtap="switchTab" data-tab="all">全部</view>
    <view class="tab {{activeTab === 'identity' ? 'active' : ''}}" bindtap="switchTab" data-tab="identity">实名认证</view>
    <view class="tab {{activeTab === 'house' ? 'active' : ''}}" bindtap="switchTab" data-tab="house">房屋信息</view>
    <view class="tab {{activeTab === 'vehicle' ? 'active' : ''}}" bindtap="switchTab" data-tab="vehicle">车辆信息</view>
  </view>

  <!-- 审核列表 -->
  <view class="review-list">
    <block wx:if="{{reviews.length > 0}}">
      <view class="review-card" wx:for="{{reviews}}" wx:key="id" data-index="{{index}}" data-id="{{item.id}}" data-type="{{item.type}}" style="{{item.style}}">
        <view class="review-content" bindtap="viewDetail" data-id="{{item.id}}" data-type="{{item.type}}">
          <view class="review-header">
            <view class="review-title">
              <view class="review-icon {{item.iconClass}}"></view>
              <text>{{item.title}}</text>
            </view>
            <view class="review-tag {{item.status}}">{{item.statusText}}</view>
          </view>

          <view class="review-info">
            <view class="info-row">
              <view class="info-label">申请人</view>
              <view class="info-value">{{item.applicant}}</view>
            </view>
            <view class="info-row">
              <view class="info-label">手机号</view>
              <view class="info-value">{{item.phone}}</view>
            </view>

            <!-- 根据类型显示不同信息 -->
            <block wx:if="{{item.type === 'identity'}}">
              <view class="info-row">
                <view class="info-label">身份证</view>
                <view class="info-value">{{item.idNumber}}</view>
              </view>
            </block>

            <block wx:elif="{{item.type === 'house'}}">
              <view class="info-row">
                <view class="info-label">房屋地址</view>
                <view class="info-value">{{item.address}}</view>
              </view>
            </block>

            <block wx:elif="{{item.type === 'vehicle'}}">
              <view class="info-row">
                <view class="info-label">车牌号</view>
                <view class="info-value">{{item.plateNumber}}</view>
              </view>
            </block>

            <view class="info-row">
              <view class="info-label">申请时间</view>
              <view class="info-value">{{item.submitTime}}</view>
            </view>
          </view>

          <view class="review-actions">
            <button class="btn-secondary" catchtap="viewDetail" data-id="{{item.id}}" data-type="{{item.type}}">查看详情</button>
          </view>
        </view>


      </view>
    </block>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{reviews.length === 0}}">
      <view class="empty-icon"></view>
      <view class="empty-text">暂无{{activeTab === 'all' ? '' : activeTabName}}审核信息</view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore}}">
    <view class="loading-indicator" wx:if="{{isLoading}}"></view>
    <text wx:else bindtap="loadMore">加载更多</text>
  </view>

  <!-- 拒绝原因弹窗 -->
  <view class="modal {{showRejectModal ? 'show' : ''}}" catchtouchmove="preventTouchMove">
    <view class="modal-mask" bindtap="hideModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">驳回原因</text>
      </view>
      <view class="modal-body">
        <textarea class="reject-reason" placeholder="请输入驳回原因..." value="{{rejectReason}}" bindinput="inputRejectReason"></textarea>
      </view>
      <view class="modal-footer">
        <button class="btn-cancel" bindtap="hideModal">取消</button>
        <button class="btn-confirm" bindtap="confirmReject">确认</button>
      </view>
    </view>
  </view>

  <!-- 通过确认弹窗 -->
  <view class="modal {{showApproveModal ? 'show' : ''}}" catchtouchmove="preventTouchMove">
    <view class="modal-mask" bindtap="hideModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">确认通过</text>
      </view>
      <view class="modal-body">
        <text class="confirm-text">确定通过此审核申请吗？</text>
      </view>
      <view class="modal-footer">
        <button class="btn-cancel" bindtap="hideModal">取消</button>
        <button class="btn-confirm" bindtap="confirmApprove">确认</button>
      </view>
    </view>
  </view>
</view>
