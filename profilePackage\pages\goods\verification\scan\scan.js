// pages/goods/verification/scan.js
const dateUtil = require('@/utils/dateUtil.js')
const goodsApi = require('@/api/goods.js')
const util = require('@/utils/util.js')

Page({
  data: {
    darkMode: false,
    goodsId: null,
    scanResult: null,
    verificationSuccess: false,
    verificationFailed: false,
    errorMessage: '',
    orderInfo: null,
    showOrderConfirm: false // 是否显示订单确认界面
  },

  onLoad: function(options) {
    if (options.goodsId) {
      this.setData({
        goodsId: options.goodsId
      })
    }

    // 自动启动扫码
    this.startScan()
  },

  // 检测订单是否超时
  checkOrderExpired: function(expireTime) {
    if (!expireTime) {
      return false; // 没有过期时间，认为未超时
    }

    try {
      const now = new Date();
      const expireDate = new Date(expireTime);

      console.log('扫码核销超时检测:', {
        now: now.toISOString(),
        expireTime: expireTime,
        expireDate: expireDate.toISOString(),
        isExpired: now > expireDate
      });

      return now > expireDate;
    } catch (error) {
      console.error('解析过期时间失败:', error);
      return false;
    }
  },

  // 启动扫码
  startScan: function() {
    wx.scanCode({
      onlyFromCamera: true,
      scanType: ['qrCode'],
      success: (res) => {
        console.log('扫码结果:', res)
        this.setData({
          scanResult: res.result
        })

        // 验证二维码
        this.verifyQRCode(res.result)
      },
      fail: (err) => {
        console.log('扫码失败:', err)
        if (err.errMsg !== 'scanCode:fail cancel') {
          wx.showToast({
            title: '扫码失败',
            icon: 'none'
          })
        }

        // 返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
    })
  },

  // 验证二维码
  verifyQRCode: function(qrCodeContent) {
    wx.showLoading({
      title: '验证中...'
    });

    console.log('扫码内容:', qrCodeContent);

    // 检查二维码格式：order: + 订单id
    if (!qrCodeContent.startsWith('order:')) {
      this.showVerificationError('无效的订单二维码');
      return;
    }

    // 提取订单ID
    const orderId = qrCodeContent.split(':')[1];
    if (!orderId) {
      this.showVerificationError('订单ID无效');
      return;
    }

    // 获取订单详情
    goodsApi.getMyGoodsOrderDetail(orderId).then(res => {
      if (res.code === 0 && res.data) {
        const orderData = res.data;

        // 检查订单状态
        if (orderData.status === 'complete') {
          this.showVerificationError('订单已完成，无需重复核销');
          return;
        }

        if (orderData.status === 'cancel') {
          this.showVerificationError('订单已取消，无法核销');
          return;
        }

        // 检查订单是否超时
        if (this.checkOrderExpired(orderData.expireTime)) {
          this.showVerificationError('订单已超时，无法完成核销');
          return;
        }

        // 解析商品信息
        let goodsInfo = {};
        if (orderData.stuffSnapshot) {
          try {
            goodsInfo = JSON.parse(orderData.stuffSnapshot);
          } catch (e) {
            console.error('解析商品快照失败:', e);
          }
        }

        // 显示订单确认信息
        this.setData({
          orderInfo: {
            id: orderData.id,
            orderNo: orderData.orderNo || '',
            buyerName: '买家', // 可以从订单数据中获取
            stuffDescribe: goodsInfo.stuffDescribe || '',
            quantity: orderData.quantity || 1,
            totalAmount: orderData.totalAmount || 0,
            unitAmount: orderData.unitAmount || 0,
            createTime: orderData.createTime ? dateUtil.formatTime(new Date(orderData.createTime)) : '',
            status: orderData.status,
            address: goodsInfo.address || ''
          },
          showOrderConfirm: true
        });

        wx.hideLoading();
      } else {
        this.showVerificationError(res.message || '订单不存在');
      }
    }).catch(err => {
      console.error('获取订单详情失败:', err);
      this.showVerificationError('获取订单信息失败，请重试');
    });
  },

  // 显示验证错误
  showVerificationError: function(message) {
    this.setData({
      verificationFailed: true,
      errorMessage: message
    });

    wx.hideLoading();

    wx.showToast({
      title: '核销失败',
      icon: 'none'
    });
  },

  // 确认完成订单
  confirmCompleteOrder: function() {
    const orderInfo = this.data.orderInfo;
    if (!orderInfo || !orderInfo.id) {
      wx.showToast({
        title: '订单信息错误',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '处理中...'
    });

    // 调用完成订单API
    goodsApi.completeMyGoodsOrder({
      id: orderInfo.id,
      note: '卖家扫码完成订单'
    }).then(res => {
      if (res.code === 0) {
        // 更新UI显示成功
        this.setData({
          verificationSuccess: true,
          showOrderConfirm: false,
          'orderInfo.verifyTime': dateUtil.formatTime(new Date())
        });

        wx.hideLoading();

        wx.showToast({
          title: '核销成功',
          icon: 'success'
        });
      } else {
        throw new Error(res.message || '完成订单失败');
      }
    }).catch(err => {
      console.error('完成订单失败:', err);
      wx.hideLoading();
      wx.showToast({
        title: err.message || '核销失败',
        icon: 'none'
      });
    });
  },

  // 取消确认
  cancelConfirm: function() {
    this.setData({
      showOrderConfirm: false,
      orderInfo: null
    });

    wx.navigateBack()
  },

  // 重新扫码
  rescan: function() {
    this.setData({
      scanResult: null,
      verificationSuccess: false,
      verificationFailed: false,
      errorMessage: '',
      orderInfo: null,
      showOrderConfirm: false
    })

    this.startScan()
  },

  // 返回列表
  backToList: function() {
    wx.navigateBack()
  },

  // 查看订单详情
  viewOrderDetail: function() {
    wx.navigateTo({
      url: `/pages/goods/order/order?id=${this.data.orderInfo.id}`
    })
  }
})
