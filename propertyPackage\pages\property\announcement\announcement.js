/**
 * 公告列表页
 * 展示已发布及草稿公告，进行管理操作
 */

const announcementApi = require('../../../../utils/announcement-api');
const dateUtil = require('../../../../utils/dateUtil');

Page({
  data: {
    darkMode: false,
    activeTab: 'all', // 当前选中的标签：all, property_notice, activity_notice, emergency_notice, draft
    searchValue: '', // 搜索关键词
    announcements: [], // 公告列表
    isLoading: true, // 是否正在加载
    isEmpty: false, // 是否为空状态
    isLoadingMore: false, // 是否正在加载更多
    hasMore: true, // 是否还有更多数据
    page: 1, // 当前页码
    pageSize: 10, // 每页数量
    showActionMenu: false, // 是否显示操作菜单
    currentAnnouncement: null, // 当前操作的公告
  },

  onLoad: function() {
    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '公告管理'
    });

    // 创建防抖搜索函数
    this.debouncedSearch = this.debounce(this.search, 300);

    // 加载公告列表
    this.loadAnnouncements();
  },

  // 页面显示时刷新数据
  onShow: function() {
    // 如果不是第一次加载，刷新列表
    if (!this.data.isLoading) {
      this.refreshAnnouncements();
    }
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.refreshAnnouncements();
  },

  // 上拉加载更多
  onReachBottom: function() {
    if (this.data.hasMore && !this.data.isLoadingMore) {
      this.loadMoreAnnouncements();
    }
  },

  // 加载公告列表
  loadAnnouncements: function() {
    const { activeTab, searchValue, page, pageSize } = this.data;

    this.setData({ isLoading: true });

    // 构建查询参数
    const params = {
      page,
      pageSize,
      keyword: searchValue
    };

    // 根据标签设置类型和状态
    if (activeTab === 'draft') {
      params.status = 'draft';
    } else if (activeTab !== 'all') {
      params.type = activeTab;
      params.status = 'published';
    }

    // 调用API获取公告列表
    announcementApi.getAnnouncementList(params)
      .then(res => {
        const { list, total, currentPage, totalPages } = res.data;

        // 处理公告数据，添加额外信息
        const announcements = list.map(item => this.processAnnouncementItem(item));

        // 保存到共享数据中，供详情页使用
        announcementApi.setRecentAnnouncementList(announcements);
        console.log('保存公告列表到共享数据', announcements);

        this.setData({
          announcements,
          isLoading: false,
          isEmpty: announcements.length === 0,
          hasMore: currentPage < totalPages
        });

        // 停止下拉刷新
        wx.stopPullDownRefresh();
      })
      .catch(err => {
        console.error('加载公告列表失败', err);
        this.setData({
          isLoading: false,
          isEmpty: true
        });

        // 停止下拉刷新
        wx.stopPullDownRefresh();

        // 显示错误提示
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },

  // 刷新公告列表
  refreshAnnouncements: function() {
    this.setData({
      page: 1,
      hasMore: true
    }, () => {
      this.loadAnnouncements();
    });
  },

  // 加载更多公告
  loadMoreAnnouncements: function() {
    if (!this.data.hasMore || this.data.isLoadingMore) return;

    const { activeTab, searchValue, page, pageSize, announcements } = this.data;

    this.setData({ isLoadingMore: true });

    // 构建查询参数
    const params = {
      page: page + 1,
      pageSize,
      keyword: searchValue
    };

    // 根据标签设置类型和状态
    if (activeTab === 'draft') {
      params.status = 'draft';
    } else if (activeTab !== 'all') {
      params.type = activeTab;
      params.status = 'published';
    }

    // 调用API获取更多公告
    announcementApi.getAnnouncementList(params)
      .then(res => {
        const { list, total, currentPage, totalPages } = res.data;

        // 处理公告数据，添加额外信息
        const newAnnouncements = list.map(item => this.processAnnouncementItem(item));

        // 合并公告列表
        const allAnnouncements = [...announcements, ...newAnnouncements];

        // 保存到共享数据中，供详情页使用
        announcementApi.setRecentAnnouncementList(allAnnouncements);
        console.log('保存更新后的公告列表到共享数据', allAnnouncements);

        this.setData({
          announcements: allAnnouncements,
          page: currentPage,
          isLoadingMore: false,
          hasMore: currentPage < totalPages
        });
      })
      .catch(err => {
        console.error('加载更多公告失败', err);
        this.setData({
          isLoadingMore: false
        });

        // 显示错误提示
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },

  // 处理公告数据，添加额外信息
  processAnnouncementItem: function(item) {
    // 判断是否为新公告（24小时内发布）
    const isNew = this.isNewAnnouncement(item.publishTime);

    // 格式化日期
    let formattedDate;
    if (item.status === 'draft') {
      // 如果是草稿，显示"草稿，保存于XX-XX XX:XX"
      if (typeof item.updatedAt === 'string') {
        if (item.updatedAt.includes('/')) {
          // 如果是"YYYY/MM/DD HH:MM:SS"格式
          const dateParts = item.updatedAt.split(' ')[0].split('/');
          const timeParts = item.updatedAt.split(' ')[1].split(':');
          formattedDate = `草稿，保存于${dateParts[1]}-${dateParts[2]} ${timeParts[0]}:${timeParts[1]}`;
        } else {
          // 如果是ISO格式
          formattedDate = `草稿，保存于${dateUtil.formatTime(new Date(item.updatedAt)).substring(5)}`;
        }
      } else {
        formattedDate = "草稿";
      }
    } else {
      // 如果是已发布公告，显示发布日期
      if (typeof item.publishTime === 'string') {
        if (item.publishTime.includes('/')) {
          // 如果是"YYYY/MM/DD HH:MM:SS"格式
          const dateParts = item.publishTime.split(' ')[0].split('/');
          const timeParts = item.publishTime.split(' ')[1].split(':');
          formattedDate = `${dateParts[1]}-${dateParts[2]} ${timeParts[0]}:${timeParts[1]}`;
        } else {
          // 如果是ISO格式
          formattedDate = dateUtil.formatTime(new Date(item.publishTime));
        }
      } else {
        formattedDate = "已发布";
      }
    }

    // 处理内容预览
    let preview = '';
    if (item.content) {
      // 如果内容是HTML格式，去除HTML标签
      if (item.content.includes('<')) {
        preview = item.content.replace(/<[^>]+>/g, '');
      } else {
        preview = item.content;
      }
      // 截取前50个字符作为预览
      preview = preview.substring(0, 50) + (preview.length > 50 ? '...' : '');
    }

    // 返回处理后的数据
    return {
      ...item,
      isNew,
      formattedDate,
      typeText: this.getTypeText(item.type),
      preview
    };
  },

  // 判断是否为新公告（24小时内发布）
  isNewAnnouncement: function(publishTime) {
    if (!publishTime) return false;

    const now = new Date();
    let publish;

    if (typeof publishTime === 'string') {
      if (publishTime.includes('/')) {
        // 如果是"YYYY/MM/DD HH:MM:SS"格式
        const [datePart, timePart] = publishTime.split(' ');
        const [year, month, day] = datePart.split('/');
        const [hour, minute, second] = timePart.split(':');
        publish = new Date(year, month - 1, day, hour, minute, second);
      } else {
        // 如果是ISO格式
        publish = new Date(publishTime);
      }
    } else {
      return false;
    }

    const diffHours = (now - publish) / (1000 * 60 * 60);

    return diffHours <= 24;
  },

  // 获取公告类型文本
  getTypeText: function(type) {
    switch (type) {
      case 'property_notice':
        return '物业通知';
      case 'activity_notice':
        return '活动通知';
      case 'emergency_notice':
        return '紧急通知';
      default:
        return '通知';
    }
  },

  // 切换标签
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;

    if (tab !== this.data.activeTab) {
      this.setData({
        activeTab: tab,
        page: 1,
        hasMore: true,
        announcements: []
      }, () => {
        this.loadAnnouncements();
      });
    }
  },

  // 搜索框输入
  onSearchInput: function(e) {
    const value = e.detail.value;
    this.setData({ searchValue: value });
    this.debouncedSearch(value);
  },

  // 清空搜索
  clearSearch: function() {
    this.setData({ searchValue: '' });
    this.refreshAnnouncements();
  },

  // 搜索
  search: function(value) {
    this.setData({
      page: 1,
      hasMore: true
    }, () => {
      this.loadAnnouncements();
    });
  },

  // 防抖函数
  debounce: function(func, wait) {
    let timeout;
    return function() {
      const context = this;
      const args = arguments;
      clearTimeout(timeout);
      timeout = setTimeout(() => {
        func.apply(context, args);
      }, wait);
    };
  },

  // 点击公告卡片
  onAnnouncementTap: function(e) {
    const id = e.currentTarget.dataset.id;
    const status = e.currentTarget.dataset.status;

    console.log('点击公告卡片', id, status);

    if (status === 'draft') {
      // 草稿进入编辑页
      wx.navigateTo({
        url: `/propertyPackage/pages/property/announcement/publish/index?id=${id}&mode=edit`
      });
    } else {
      // 已发布进入详情页
      wx.navigateTo({
        url: `/propertyPackage/pages/property/announcement/detail/index?id=${id}`
      });

      // 记录查看的公告ID，用于调试
      wx.setStorage({
        key: 'last_viewed_announcement',
        data: id
      });
    }
  },

  // 长按公告卡片或点击更多按钮
  showActionMenu: function(e) {
    const announcement = e.currentTarget.dataset.item;

    this.setData({
      currentAnnouncement: announcement,
      showActionMenu: true
    });
  },

  // 关闭操作菜单
  closeActionMenu: function() {
    this.setData({
      showActionMenu: false,
      currentAnnouncement: null
    });
  },

  // 编辑公告
  editAnnouncement: function() {
    const { id } = this.data.currentAnnouncement;

    this.closeActionMenu();

    wx.navigateTo({
      url: `/pages/property/announcement/publish/index?id=${id}&mode=edit`
    });
  },

  // 删除公告
  deleteAnnouncement: function() {
    const { id, title } = this.data.currentAnnouncement;

    this.closeActionMenu();

    // 二次确认
    wx.showModal({
      title: '确认删除',
      content: `确定要删除公告"${title}"吗？`,
      confirmColor: '#ff8c00',
      success: (res) => {
        if (res.confirm) {
          // 调用API删除公告
          announcementApi.deleteAnnouncement(id)
            .then(() => {
              // 删除成功，刷新列表
              this.refreshAnnouncements();

              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
            })
            .catch(err => {
              console.error('删除公告失败', err);

              wx.showToast({
                title: '删除失败，请重试',
                icon: 'none'
              });
            });
        }
      }
    });
  },

  // 置顶/取消置顶公告
  togglePinAnnouncement: function() {
    const { id, isPinned } = this.data.currentAnnouncement;

    this.closeActionMenu();

    const action = isPinned ? '取消置顶' : '置顶';
    const apiMethod = isPinned ? announcementApi.unpinAnnouncement : announcementApi.pinAnnouncement;

    // 调用API置顶/取消置顶公告
    apiMethod(id)
      .then(() => {
        // 操作成功，刷新列表
        this.refreshAnnouncements();

        wx.showToast({
          title: `${action}成功`,
          icon: 'success'
        });
      })
      .catch(err => {
        console.error(`${action}公告失败`, err);

        wx.showToast({
          title: `${action}失败，请重试`,
          icon: 'none'
        });
      });
  },

  // 撤回公告
  retractAnnouncement: function() {
    const { id, title } = this.data.currentAnnouncement;

    this.closeActionMenu();

    // 二次确认
    wx.showModal({
      title: '确认撤回',
      content: `确定要撤回公告"${title}"吗？`,
      confirmColor: '#ff8c00',
      success: (res) => {
        if (res.confirm) {
          // 调用API撤回公告
          announcementApi.retractAnnouncement(id)
            .then(() => {
              // 撤回成功，刷新列表
              this.refreshAnnouncements();

              wx.showToast({
                title: '撤回成功',
                icon: 'success'
              });
            })
            .catch(err => {
              console.error('撤回公告失败', err);

              wx.showToast({
                title: '撤回失败，请重试',
                icon: 'none'
              });
            });
        }
      }
    });
  },

  // 添加新公告
  addAnnouncement: function() {
    wx.navigateTo({
      url: '/propertyPackage/pages/property/announcement/publish/index?mode=create&new=true'
    });
  },

  // 返回上一页方法已移除，使用微信小程序自带的返回功能
});
