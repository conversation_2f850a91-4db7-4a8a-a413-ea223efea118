<!--数据统计页面-->
<view class="container {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}">
  <!-- 标签页导航 -->
  <view class="tab-nav">
    <view class="tab-item {{activeTab === 'dashboard' ? 'active' : ''}}" bindtap="switchTab" data-tab="dashboard">
      <text>运营概览</text>
    </view>
    <view class="tab-item {{activeTab === 'category' ? 'active' : ''}}" bindtap="switchTab" data-tab="category">
      <text>分类快照</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">数据加载中...</text>
  </view>

  <!-- 错误状态 -->
  <view class="error-container" wx:elif="{{pageState === 'error'}}">
    <view class="error-icon"></view>
    <text class="error-text">{{errorMsg || '数据加载失败'}}</text>
    <view class="retry-button" bindtap="loadTabData" data-tab="{{activeTab}}">重试</view>
  </view>

  <!-- 仪表盘内容 -->
  <view class="dashboard-container" wx:if="{{activeTab === 'dashboard' && !isLoading}}">
    <view class="dashboard-header">
      <text class="dashboard-title">运营概览</text>
      <text class="dashboard-date">{{currentDate}}</text>
    </view>

    <view class="dashboard-grid">
      <!-- 工单卡片 -->
      <view class="dashboard-card" bindtap="navigateToWorkOrder">
        <view class="card-icon work-order-icon"></view>
        <view class="card-content">
          <text class="card-value">{{dashboardData.workOrderCount || 0}}</text>
          <text class="card-label">今日待办工单</text>
          <text class="card-subtext">新增 {{dashboardData.newWorkOrderCount || 0}} 件</text>
        </view>
      </view>

      <!-- 访客卡片 -->
      <view class="dashboard-card" bindtap="navigateToVisitor">
        <view class="card-icon visitor-icon"></view>
        <view class="card-content">
          <text class="card-value">{{dashboardData.visitorCount || 0}}</text>
          <text class="card-label">今日访客</text>
          <text class="card-subtext">当前在访 {{dashboardData.currentVisitorCount || 0}} 人</text>
        </view>
      </view>

      <!-- 设施卡片 -->
      <view class="dashboard-card" bindtap="navigateToFacility">
        <view class="card-icon facility-icon"></view>
        <view class="card-content">
          <text class="card-value {{dashboardData.abnormalFacilityCount > 0 ? 'warning-text' : ''}}">
            {{dashboardData.abnormalFacilityCount || 0}}
          </text>
          <text class="card-label">设施异常</text>
          <text class="card-subtext">监控在线率 {{dashboardData.monitorOnlineRate || '100%'}}</text>
        </view>
      </view>

      <!-- 居民卡片 -->
      <view class="dashboard-card" bindtap="navigateToResident">
        <view class="card-icon resident-icon"></view>
        <view class="card-content">
          <text class="card-value">{{dashboardData.residentCount || 0}}</text>
          <text class="card-label">居民总数</text>
          <text class="card-subtext">认证率 {{dashboardData.residentVerifyRate || '0%'}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 分类快照内容 -->
  <view class="category-container" wx:if="{{activeTab === 'category' && !isLoading}}">
    <view class="category-header">
      <text class="category-title">{{getCategoryTitle()}}</text>

      <!-- 分类选择器 -->
      <view class="category-selector">
        <view
          class="category-item {{currentCategory === 'resident' ? 'active' : ''}}"
          bindtap="switchCategory"
          data-category="resident">居民</view>
        <view
          class="category-item {{currentCategory === 'house' ? 'active' : ''}}"
          bindtap="switchCategory"
          data-category="house">房屋</view>
        <view
          class="category-item {{currentCategory === 'parking' ? 'active' : ''}}"
          bindtap="switchCategory"
          data-category="parking">车位</view>
        <view
          class="category-item {{currentCategory === 'facility' ? 'active' : ''}}"
          bindtap="switchCategory"
          data-category="facility">设施</view>
        <view
          class="category-item {{currentCategory === 'workorder' ? 'active' : ''}}"
          bindtap="switchCategory"
          data-category="workorder">工单</view>
        <view
          class="category-item {{currentCategory === 'visitor' ? 'active' : ''}}"
          bindtap="switchCategory"
          data-category="visitor">访客</view>
      </view>

      <!-- 时间筛选器 -->
      <view class="time-filter">
        <view
          class="time-item {{timeRange === 'today' ? 'active' : ''}}"
          bindtap="switchTimeRange"
          data-range="today">今日</view>
        <view
          class="time-item {{timeRange === 'week' ? 'active' : ''}}"
          bindtap="switchTimeRange"
          data-range="week">本周</view>
        <view
          class="time-item {{timeRange === 'month' ? 'active' : ''}}"
          bindtap="switchTimeRange"
          data-range="month">本月</view>
      </view>
    </view>

    <!-- 居民分类内容 -->
    <view class="category-content" wx:if="{{currentCategory === 'resident'}}">
      <view class="summary-row">
        <view class="summary-item">
          <text class="summary-value">{{categoryData.resident.totalCount || 0}}</text>
          <text class="summary-label">居民总数</text>
        </view>
        <view class="summary-item">
          <text class="summary-value">{{categoryData.resident.verifiedRate || '0%'}}</text>
          <text class="summary-label">认证率</text>
        </view>
      </view>

      <!-- 居民类型分布 -->
      <view class="chart-card">
        <view class="chart-header">
          <text class="chart-title">居民类型分布</text>
        </view>
        <view class="chart-content">
          <view class="pie-chart-container">
            <canvas canvas-id="residentPieChart" class="chart-canvas" style="width: 280rpx; height: 280rpx;"></canvas>
          </view>
        </view>
        <view class="chart-legend-container">
          <view class="legend-item">
            <view class="legend-color" style="background-color: #4f46e5;"></view>
            <text class="legend-text">业主 {{categoryData.resident.ownerCount || 0}}人</text>
          </view>
          <view class="legend-item">
            <view class="legend-color" style="background-color: #3b82f6;"></view>
            <text class="legend-text">租户 {{categoryData.resident.tenantCount || 0}}人</text>
          </view>
          <view class="legend-item">
            <view class="legend-color" style="background-color: #60a5fa;"></view>
            <text class="legend-text">家属 {{categoryData.resident.familyCount || 0}}人</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 工单分类内容 -->
    <view class="category-content" wx:if="{{currentCategory === 'workorder'}}">
      <view class="summary-row">
        <view class="summary-item">
          <text class="summary-value">{{categoryData.workorder.totalCount || 0}}</text>
          <text class="summary-label">工单总数</text>
        </view>
        <view class="summary-item">
          <text class="summary-value">{{categoryData.workorder.completionRate || '0%'}}</text>
          <text class="summary-label">完成率</text>
        </view>
      </view>

      <!-- 工单类型分布 -->
      <view class="chart-card">
        <view class="chart-header">
          <text class="chart-title">工单类型分布</text>
        </view>
        <view class="chart-content">
          <view class="pie-chart-container">
            <canvas canvas-id="workorderTypeChart" class="chart-canvas" style="width: 280rpx; height: 280rpx;"></canvas>
          </view>
          <view class="chart-legend">
            <view class="legend-item" wx:for="{{categoryData.workorder.typeDistribution}}" wx:key="type">
              <view class="legend-color" style="background-color: {{item.type === '维修' ? '#4f46e5' : item.type === '投诉' ? '#3b82f6' : item.type === '建议' ? '#60a5fa' : '#93c5fd'}};"></view>
              <text class="legend-text">{{item.type}} {{item.count}}件</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 工单状态分布 -->
      <view class="chart-card">
        <view class="chart-header">
          <text class="chart-title">工单状态分布</text>
        </view>
        <view class="chart-content">
          <view class="bar-chart-container" style="height: 350rpx;">
            <canvas canvas-id="workorderStatusChart" class="chart-canvas" style="width: 100%; height: 100%;"></canvas>
          </view>
        </view>
      </view>
    </view>

    <!-- 房屋分类内容 -->
    <view class="category-content" wx:if="{{currentCategory === 'house'}}">
      <view class="summary-row">
        <view class="summary-item">
          <text class="summary-value">{{categoryData.house.totalCount || 0}}</text>
          <text class="summary-label">房屋总数</text>
        </view>
        <view class="summary-item">
          <text class="summary-value">{{categoryData.house.occupancyRate || '0%'}}</text>
          <text class="summary-label">入住率</text>
        </view>
      </view>

      <!-- 房屋类型分布 -->
      <view class="chart-card">
        <view class="chart-header">
          <text class="chart-title">房屋类型分布</text>
        </view>
        <view class="chart-content">
          <view class="pie-chart-container">
            <canvas canvas-id="houseTypeChart" class="chart-canvas" style="width: 280rpx; height: 280rpx;"></canvas>
          </view>
          <view class="chart-legend">
            <view class="legend-item" wx:for="{{categoryData.house.typeDistribution}}" wx:key="type">
              <view class="legend-color" style="background-color: {{index === 0 ? '#4f46e5' : index === 1 ? '#3b82f6' : index === 2 ? '#60a5fa' : '#93c5fd'}};"></view>
              <text class="legend-text">{{item.type}} {{item.count}}套</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 楼栋分布 -->
      <view class="chart-card">
        <view class="chart-header">
          <text class="chart-title">楼栋分布</text>
        </view>
        <view class="chart-content">
          <view class="bar-chart-container" style="height: 350rpx;">
            <canvas canvas-id="houseBuildingChart" class="chart-canvas" style="width: 100%; height: 100%;"></canvas>
          </view>
        </view>
      </view>
    </view>

    <!-- 车位分类内容 -->
    <view class="category-content" wx:if="{{currentCategory === 'parking'}}">
      <view class="summary-row">
        <view class="summary-item">
          <text class="summary-value">{{categoryData.parking.totalCount || 0}}</text>
          <text class="summary-label">车位总数</text>
        </view>
        <view class="summary-item">
          <text class="summary-value">{{categoryData.parking.usageRate || '0%'}}</text>
          <text class="summary-label">使用率</text>
        </view>
      </view>

      <!-- 车位类型分布 -->
      <view class="chart-card">
        <view class="chart-header">
          <text class="chart-title">车位类型分布</text>
        </view>
        <view class="chart-content">
          <view class="pie-chart-container">
            <canvas canvas-id="parkingTypeChart" class="chart-canvas" style="width: 280rpx; height: 280rpx;"></canvas>
          </view>
          <view class="chart-legend">
            <view class="legend-item" wx:for="{{categoryData.parking.typeDistribution}}" wx:key="type">
              <view class="legend-color" style="background-color: {{index === 0 ? '#4f46e5' : '#3b82f6'}};"></view>
              <text class="legend-text">{{item.type}} {{item.count}}个</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 车辆类型分布 -->
      <view class="chart-card">
        <view class="chart-header">
          <text class="chart-title">车辆类型分布</text>
        </view>
        <view class="chart-content">
          <view class="pie-chart-container">
            <canvas canvas-id="vehicleTypeChart" class="chart-canvas" style="width: 280rpx; height: 280rpx;"></canvas>
          </view>
          <view class="chart-legend">
            <view class="legend-item" wx:for="{{categoryData.parking.vehicleTypeDistribution}}" wx:key="type">
              <view class="legend-color" style="background-color: {{index === 0 ? '#4f46e5' : index === 1 ? '#3b82f6' : '#60a5fa'}};"></view>
              <text class="legend-text">{{item.type}} {{item.count}}辆</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 设施分类内容 -->
    <view class="category-content" wx:if="{{currentCategory === 'facility'}}">
      <view class="summary-row">
        <view class="summary-item">
          <text class="summary-value">{{categoryData.facility.totalCount || 0}}</text>
          <text class="summary-label">设施总数</text>
        </view>
        <view class="summary-item">
          <text class="summary-value">{{categoryData.facility.statusRate || '0%'}}</text>
          <text class="summary-label">正常率</text>
        </view>
      </view>

      <!-- 设施类型分布 -->
      <view class="chart-card">
        <view class="chart-header">
          <text class="chart-title">设施类型分布</text>
        </view>
        <view class="chart-content">
          <view class="pie-chart-container">
            <canvas canvas-id="facilityTypeChart" class="chart-canvas" style="width: 280rpx; height: 280rpx;"></canvas>
          </view>
          <view class="chart-legend">
            <view class="legend-item" wx:for="{{categoryData.facility.typeDistribution}}" wx:key="type">
              <view class="legend-color" style="background-color: {{index === 0 ? '#4f46e5' : index === 1 ? '#3b82f6' : index === 2 ? '#60a5fa' : index === 3 ? '#93c5fd' : '#bfdbfe'}};"></view>
              <text class="legend-text">{{item.type}} {{item.count}}个</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 维保记录 -->
      <view class="chart-card">
        <view class="chart-header">
          <text class="chart-title">近期维保记录</text>
        </view>
        <view class="chart-content">
          <view class="maintenance-list">
            <view class="maintenance-item" wx:for="{{categoryData.facility.maintenanceRecords}}" wx:key="date">
              <text class="maintenance-date">{{item.date}}</text>
              <text class="maintenance-count">{{item.count}}次</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 访客分类内容 -->
    <view class="category-content" wx:if="{{currentCategory === 'visitor'}}">
      <view class="summary-row">
        <view class="summary-item">
          <text class="summary-value">{{categoryData.visitor.totalCount || 0}}</text>
          <text class="summary-label">访客总数</text>
        </view>
        <view class="summary-item">
          <text class="summary-value">{{categoryData.visitor.currentCount || 0}}</text>
          <text class="summary-label">当前在访</text>
        </view>
      </view>

      <!-- 访客目的分布 -->
      <view class="chart-card">
        <view class="chart-header">
          <text class="chart-title">访客目的分布</text>
        </view>
        <view class="chart-content">
          <view class="pie-chart-container">
            <canvas canvas-id="visitorPurposeChart" class="chart-canvas" style="width: 280rpx; height: 280rpx;"></canvas>
          </view>
          <view class="chart-legend">
            <view class="legend-item" wx:for="{{categoryData.visitor.purposeDistribution}}" wx:key="purpose">
              <view class="legend-color" style="background-color: {{index === 0 ? '#4f46e5' : index === 1 ? '#3b82f6' : index === 2 ? '#60a5fa' : '#93c5fd'}};"></view>
              <text class="legend-text">{{item.purpose}} {{item.count}}人</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 访客时段分布 -->
      <view class="chart-card">
        <view class="chart-header">
          <text class="chart-title">访客时段分布</text>
        </view>
        <view class="chart-content">
          <view class="bar-chart-container" style="height: 350rpx;">
            <canvas canvas-id="visitorTimeChart" class="chart-canvas" style="width: 100%; height: 100%;"></canvas>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
