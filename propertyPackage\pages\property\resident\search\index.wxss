/* 居民搜索页面样式 */
.container {
  min-height: 100vh;
  background-color: #F2F2F7;
  padding-bottom: 120rpx;
}

/* 搜索栏 */
.search-bar {
  position: sticky;
  top: 0;
  z-index: 100;
  padding: 24rpx 32rpx;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.search-input-wrap {
  flex: 1;
  height: 72rpx;
  background: #F2F2F7;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.search-input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
  color: #000000;
}

.clear-icon {
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='15' y1='9' x2='9' y2='15'%3E%3C/line%3E%3Cline x1='9' y1='9' x2='15' y2='15'%3E%3C/line%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.filter-btn {
  width: 72rpx;
  height: 72rpx;
  border-radius: 36rpx;
  background: #F2F2F7;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
}

.filter-icon {
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='4' y1='21' x2='4' y2='14'%3E%3C/line%3E%3Cline x1='4' y1='10' x2='4' y2='3'%3E%3C/line%3E%3Cline x1='12' y1='21' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='8' x2='12' y2='3'%3E%3C/line%3E%3Cline x1='20' y1='21' x2='20' y2='16'%3E%3C/line%3E%3Cline x1='20' y1='12' x2='20' y2='3'%3E%3C/line%3E%3Cline x1='1' y1='14' x2='7' y2='14'%3E%3C/line%3E%3Cline x1='9' y1='8' x2='15' y2='8'%3E%3C/line%3E%3Cline x1='17' y1='16' x2='23' y2='16'%3E%3C/line%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

/* 筛选标签样式 */
.filter-tags {
  display: flex;
  padding: 16rpx 32rpx;
  background: #FFFFFF;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
  overflow-x: auto;
  white-space: nowrap;
}

.tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 56rpx;
  padding: 0 24rpx;
  border-radius: 28rpx;
  font-size: 26rpx;
  background: #F2F2F7;
  color: #8E8E93;
  margin-right: 16rpx;
  transition: all 0.3s;
}

.tag.active {
  background: rgba(255, 140, 0, 0.1);
  color: #FF8C00;
}

/* 居民列表样式 */
.resident-list {
  padding: 24rpx 32rpx;
}

.resident-card {
  background: #FFFFFF;
  border-radius: 28rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
  position: relative;
  transition: transform 0.3s;
}

.resident-content {
  padding: 32rpx;
}

.resident-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.resident-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
}

.resident-badges {
  display: flex;
}

.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 40rpx;
  padding: 0 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  margin-left: 16rpx;
}

.badge.owner {
  background: rgba(255, 140, 0, 0.1);
  color: #FF8C00;
}

.badge.tenant {
  background: rgba(0, 122, 255, 0.1);
  color: #007AFF;
}

.badge.verified {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.badge.unverified {
  background: rgba(142, 142, 147, 0.1);
  color: #8E8E93;
}

.resident-info {
  margin-bottom: 16rpx;
}

.info-row {
  display: flex;
  margin-bottom: 16rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 140rpx;
  font-size: 28rpx;
  color: #8E8E93;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #000000;
}

/* 空状态样式 */
.empty-state {
  padding: 80rpx 0;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 24rpx;
  background: rgba(142, 142, 147, 0.1);
  border-radius: 60rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='48' height='48' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2'%3E%3C/path%3E%3Ccircle cx='8.5' cy='7' r='4'%3E%3C/circle%3E%3Cline x1='18' y1='8' x2='23' y2='13'%3E%3C/line%3E%3Cline x1='23' y1='8' x2='18' y2='13'%3E%3C/line%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: 48rpx 48rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #8E8E93;
}

/* 加载更多样式 */
.load-more {
  text-align: center;
  padding: 32rpx 0;
  color: #8E8E93;
  font-size: 28rpx;
}

.loading-indicator {
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid rgba(255, 140, 0, 0.1);
  border-top: 4rpx solid #FF8C00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 扫码按钮 */
/* 滑动操作样式 */
.slide-actions {
  position: absolute;
  top: 0;
  right: -80rpx;
  height: 100%;
  display: flex;
  width: 80rpx;
}

.action-view {
  width: 80rpx;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  font-size: 28rpx;
  background: #FF8C00;
}

.scan-btn {
  position: fixed;
  left: 50%;
  bottom: 40rpx;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 240rpx;
  height: 80rpx;
  background: #FF8C00;
  border-radius: 40rpx;
  color: #FFFFFF;
  font-size: 28rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 140, 0, 0.3);
  z-index: 90;
}

.scan-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23FFFFFF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M3 9h18v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z'%3E%3C/path%3E%3Cpath d='M8 9V5a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v4'%3E%3C/path%3E%3Cline x1='12' y1='11' x2='12' y2='15'%3E%3C/line%3E%3Cline x1='10' y1='13' x2='14' y2='13'%3E%3C/line%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

/* 高级筛选弹窗 */
.advanced-filter {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  justify-content: flex-end;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.advanced-filter.show {
  opacity: 1;
  visibility: visible;
}

.filter-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(4px);
}

.filter-content {
  position: relative;
  width: 600rpx;
  height: 100%;
  background: #FFFFFF;
  transform: translateX(100%);
  transition: transform 0.3s;
  display: flex;
  flex-direction: column;
}

.advanced-filter.show .filter-content {
  transform: translateX(0);
}

.filter-header {
  padding: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
}

.filter-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #000000;
}

.filter-close {
  width: 40rpx;
  height: 40rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.filter-body {
  flex: 1;
  padding: 32rpx;
  overflow-y: auto;
}

.filter-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #000000;
  margin-bottom: 24rpx;
}

.option-list {
  display: flex;
  flex-wrap: wrap;
}

.option {
  height: 64rpx;
  padding: 0 24rpx;
  border-radius: 32rpx;
  font-size: 28rpx;
  background: #F2F2F7;
  color: #8E8E93;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.option.active {
  background: rgba(255, 140, 0, 0.1);
  color: #FF8C00;
}

.filter-footer {
  padding: 24rpx 32rpx;
  display: flex;
  border-top: 1rpx solid rgba(60, 60, 67, 0.1);
}

.btn-reset, .btn-apply {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.btn-reset {
  background: #FFFFFF;
  color: #8E8E93;
  border: 1rpx solid rgba(142, 142, 147, 0.2);
  margin-right: 24rpx;
}

.btn-apply {
  background: #FF8C00;
  color: #FFFFFF;
}
