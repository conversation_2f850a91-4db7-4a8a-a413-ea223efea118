// 实名认证页面逻辑
const app = getApp();
const commApi = require('../../../api/commApi.js');
const userApi = require('../../../api/userApi.js');

Page({
  data: {
    // 当前步骤
    currentStep: 1,

    // 用户类型：resident(住户) 或 property(物业员工)
    userType: 'resident',

    // 认证方式：resident(住户认证) 或 property(物业员工认证)
    authMethod: 'resident',

    // 住户表单数据
    name: '',
    phone: '',
    verifyCode: '',
    idCard: '',
    residentPhotoPath: '',
    fileUploadPath: '',//服务器文件路径
    appointmentTime: '',
    appointmentRemark: '',

    // 物业员工表单数据
    employeeId: '',
    department: '',
    departmentIndex: -1,
    position: '',
    positionIndex: -1,
    workCardPhotoPath: '',
    workCardUploadPath: '',



    // 表单验证状态
    nameValid: false,
    nameError: false,
    phoneValid: false,
    phoneError: false,
    verifyCodeValid: false,
    verifyCodeError: false,
    idCardValid: false,
    idCardError: false,
    employeeIdValid: false,
    employeeIdError: false,
    departmentValid: false,
    departmentError: false,
    positionValid: false,
    positionError: false,
    appointmentError: false,

    // 验证码状态
    codeSent: false,
    countDown: 60,
    codeKey: '',

    // 选项数据
    departments: ['客服部', '工程部', '保安部', '保洁部', '绿化部', '财务部', '管理部'],
    positions: ['经理', '主管', '客服专员', '维修工程师', '保安队长', '保安员', '保洁员', '绿化工', '财务专员'],



    // 提交状态
    submitting: false,

    // 弹窗状态
    showSuccessDialogFlag: false,
    showAppointmentSelectorFlag: false,

    // 成功弹窗内容
    successTitle: '',
    successMessage: '',

    // 预约时间选择
    availableDates: [],
    timeSlots: [],
    selectedDateIndex: 0,
    selectedTimeSlot: null,


    isChecked:false,
    hasAuth:false
  },

  onLoad: function (options) {
    // 获取URL参数
    const userType = options.userType;
    const hasAuth = options.hasAuth === 'true';

    // 如果用户已认证，检查其当前认证类型并默认切换到另一种认证类型
    if (hasAuth) {

      // 显示已有认证的提示信息
      this.showAlreadyAuthenticatedTip();
    }
    // 如果URL中指定了用户类型，切换到对应表单
    else if (userType === 'property') {
      this.setData({ userType: 'property' });
    }

    // 初始化预约日期数据
    this.initAppointmentDates();

    // 添加调试代码，用于检查照片上传功能
    console.log('页面加载完成，照片上传功能初始化');
  },



  // 初始化预约日期数据
  initAppointmentDates: function () {
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const availableDates = [];

    // 生成未来7天的日期
    const now = new Date();
    for (let i = 1; i <= 7; i++) {
      const date = new Date(now);
      date.setDate(now.getDate() + i);

      // 跳过周末
      if (date.getDay() === 0 || date.getDay() === 6) {
        continue;
      }

      availableDates.push({
        date: date.toISOString().split('T')[0],
        day: date.getDate(),
        month: date.getMonth() + 1,
        weekday: weekdays[date.getDay()]
      });
    }

    // 初始化时间段
    const timeSlots = [
      { time: '09:00-10:00', available: true },
      { time: '10:00-11:00', available: true },
      { time: '11:00-12:00', available: true },
      { time: '14:00-15:00', available: true },
      { time: '15:00-16:00', available: true },
      { time: '16:00-17:00', available: true }
    ];

    // 随机设置一些时间段为不可用
    timeSlots.forEach((_, index) => {
      if (Math.random() < 0.3) {
        timeSlots[index].available = false;
      }
    });

    this.setData({
      availableDates,
      timeSlots
    });
  },

  // 切换用户类型
  switchUserType: function (e) {
    const type = e.currentTarget.dataset.type || e.target.dataset.type;
    if (type && type !== this.data.userType) {
      this.setData({ userType: type });
    }
  },

  // 切换认证方式
  switchAuthMethod: function (e) {
    const method = e.currentTarget.dataset.method;
    if (method && method !== this.data.authMethod) {
      this.setData({ authMethod: method });
    }
  },

  // 返回上一页
  goBack: function () {
    const pages = getCurrentPages();
    if (pages.length > 1) {
      wx.navigateBack();
    } else {
      wx.switchTab({
        url: '/pages/index/index'
      });
    }
  },

  // 跳转到隐私政策页面
  goToPrivacyPolicy: function () {
    wx.navigateTo({
      url: '/pages/settings/privacy/privacy'
    });
  },

  // 住户表单验证
  onNameInput: function (e) {
    this.setData({ name: e.detail.value });
    this.validateName();
  },

  onPhoneInput: function (e) {
    this.setData({ phone: e.detail.value });
    this.validatePhone();
  },

  onVerifyCodeInput: function (e) {
    this.setData({ verifyCode: e.detail.value });
    this.validateVerifyCode();
  },

  onIdCardInput: function (e) {
    this.setData({ idCard: e.detail.value });
    this.validateIdCard();
  },

  onEmployeeIdInput: function (e) {
    this.setData({ employeeId: e.detail.value });
    this.validateEmployeeId();
  },

  onAppointmentRemarkInput: function (e) {
    this.setData({ appointmentRemark: e.detail.value });
  },

  validateName: function () {
    const name = this.data.name.trim();
    const isValid = name.length >= 2;
    this.setData({
      nameValid: isValid,
      nameError: name.length > 0 && !isValid
    });
    return isValid;
  },

  validatePhone: function () {
    const util = require('@/utils/util');
    const phone = this.data.phone.trim();
    const isValid = util.validatePhone(phone);
    this.setData({
      phoneValid: isValid,
      phoneError: phone.length > 0 && !isValid
    });
    return isValid;
  },

  validateVerifyCode: function () {
    const verifyCode = this.data.verifyCode.trim();
    const isValid = /^\d{6}$/.test(verifyCode);
    this.setData({
      verifyCodeValid: isValid,
      verifyCodeError: verifyCode.length > 0 && !isValid
    });
    return isValid;
  },

  validateIdCard: function () {
    const idCard = this.data.idCard.trim();
    const isValid = /^\d{17}[\dXx]$/.test(idCard);
    this.setData({
      idCardValid: isValid,
      idCardError: idCard.length > 0 && !isValid
    });
    return isValid;
  },

  validateEmployeeId: function () {
    const employeeId = this.data.employeeId.trim();
    const isValid = employeeId.length >= 6;
    this.setData({
      employeeIdValid: isValid,
      employeeIdError: employeeId.length > 0 && !isValid
    });
    return isValid;
  },

  // 发送验证码
  sendVerifyCode: function () {
    if (!this.validatePhone()) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }

    // 显示发送中提示
    wx.showLoading({
      title: '发送中...',
      mask: true
    });

    // 使用新的API发送验证码
    commApi.getSmsCode(this.data.phone)
      .then((res) => {
        wx.hideLoading();

        // 设置验证码已发送状态
        this.setData({
          codeSent: true,
          codeKey: res.data
        });

        // 开始倒计时
        this.startCountDown();

        wx.showToast({
          title: '验证码已发送',
          icon: 'success'
        });
      })
      .catch(err => {
        wx.hideLoading();
        console.log('发送验证码失败', err);

        // 本地测试环境，模拟发送成功
        this.setData({
          codeSent: false
        });


        wx.showToast({
          title: '验证码已发送（测试环境）',
          icon: 'error'
        });
      });
  },

  // 开始倒计时
  startCountDown: function () {
    const countDownTimer = setInterval(() => {
      if (this.data.countDown <= 1) {
        clearInterval(countDownTimer);
        this.setData({
          codeSent: false,
          countDown: 60
        });
      } else {
        this.setData({
          countDown: this.data.countDown - 1
        });
      }
    }, 1000);
  },







  // 从相册选择照片
  chooseFromAlbum: function () {
    console.log('调用从相册选择照片方法');
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    // 尝试使用旧的API，可能在某些设备上更稳定
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album'],
      success: (res) => {
        console.log('选择图片成功', res);
        this.setData({ residentPhotoPath: res.tempFilePaths[0] });
        //上传图片
        this.uploadPhoto(res.tempFilePaths[0])

        wx.hideLoading();
      },
      fail: (err) => {
        console.log('选择图片失败(旧API)', err);
        // 如果旧API失败，尝试使用新API
        wx.chooseMedia({
          count: 1,
          mediaType: ['image'],
          sourceType: ['album'],
          sizeType: ['compressed'],
          success: (res) => {
            console.log('选择图片成功(新API)', res);
            // 检查文件大小，限制为5MB
            const tempFile = res.tempFiles[0];
            const fileSizeInMB = tempFile.size / (1024 * 1024);

            if (fileSizeInMB > 5) {
              wx.hideLoading();
              wx.showToast({
                title: '图片大小不能超过5MB',
                icon: 'none',
                duration: 2000
              });
              return;
            }

            this.setData({ residentPhotoPath: tempFile.tempFilePath });
            wx.hideLoading();
          },
          fail: (err2) => {
            console.log('选择图片失败(新API)', err2);
            // 用户取消选择不显示错误提示
            if (err2.errMsg !== "chooseMedia:fail cancel") {
              wx.showToast({
                title: '选择图片失败',
                icon: 'none',
                duration: 2000
              });
            }
            wx.hideLoading();
          }
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  // 拍照上传 - 简化版，只使用一种API
  takePhoto: function () {
    console.log('调用拍照上传方法 - 简化版');
    wx.showLoading({
      title: '准备相机...',
      mask: true
    });

    // 直接使用wx.chooseImage，这是最基础的API，兼容性最好
    wx.chooseImage({
      count: 1, // 最多可以选择的图片张数
      sizeType: ['compressed'], // 所选的图片的尺寸压缩模式
      sourceType: ['camera'], // 设置只允许使用相机，强制直接打开相机
      success: (res) => {
        console.log('拍照成功', res);
        // 设置照片路径
        this.setData({ residentPhotoPath: res.tempFilePaths[0] });

        //上传图片（本地测试环境为非必填项）
        this.uploadPhoto(res.tempFilePaths[0]);

        wx.hideLoading();
      },
      fail: (err) => {
        console.log('拍照失败', err);
        // 用户取消拍照不显示错误提示
        if (err.errMsg !== "chooseImage:fail cancel") {
          wx.showToast({
            title: '拍照失败，请重试',
            icon: 'none',
            duration: 2000
          });
        }
        wx.hideLoading();
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  // 删除照片
  removePhoto: function () {
    this.setData({
      residentPhotoPath: '',
      fileUploadPath: ''
    });
  },

  // 显示部门选择器
  showDepartmentPicker: function () {
    wx.showActionSheet({
      itemList: this.data.departments,
      success: (res) => {
        this.setData({
          department: this.data.departments[res.tapIndex],
          departmentIndex: res.tapIndex,
          departmentValid: true,
          departmentError: false
        });
      }
    });
  },

  // 显示职位选择器
  showPositionPicker: function () {
    wx.showActionSheet({
      itemList: this.data.positions,
      success: (res) => {
        this.setData({
          position: this.data.positions[res.tapIndex],
          positionIndex: res.tapIndex,
          positionValid: true,
          positionError: false
        });
      }
    });
  },

  // 选择工作证照片
  chooseWorkCardPhoto: function () {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.setData({ workCardPhotoPath: res.tempFilePaths[0] });
        // 上传工作证照片
        this.uploadWorkCardPhoto(res.tempFilePaths[0]);
      }
    });
  },

  // 删除工作证照片
  removeWorkCardPhoto: function () {
    this.setData({
      workCardPhotoPath: '',
      workCardUploadPath: ''
    });
  },

  // 上传工作证照片
  uploadWorkCardPhoto: function (src) {
    if (!src) {
      console.log('本地测试环境，工作证照片上传已跳过');
      return Promise.resolve({ data: '', message: '本地测试环境，工作证照片上传已跳过' });
    }

    return commApi.upLoadFile(src)
      .then((result) => {
        console.log('工作证照片上传成功', result);
        this.setData({
          workCardUploadPath: result.data || ''
        });
        wx.showToast({
          title: '工作证照片上传成功',
          icon: 'success',
          duration: 1500
        });
        return result;
      })
      .catch((errorMsg) => {
        console.log('工作证照片上传失败', errorMsg);
        console.log('本地测试环境，工作证照片上传失败已忽略');
        return { data: '', message: '本地测试环境，工作证照片上传失败已忽略' };
      });
  },


  uploadPhoto(src) {
    if (!src) {
      console.log('本地测试环境，图片上传已跳过');
      return Promise.resolve({ data: '', message: '本地测试环境，图片上传已跳过' });
    }

    return commApi.upLoadFile(src)
      .then((result) => {
        console.log('图片上传成功', result);

        this.setData({
          fileUploadPath: result.data || ''
        });

        wx.showToast({
          title: '图片上传成功',
          icon: 'success',
          duration: 1500
        });

        return result;
      })
      .catch((errorMsg) => {
        console.log('图片上传失败', errorMsg);

        // 本地测试环境，不显示错误提示
        console.log('本地测试环境，图片上传失败已忽略');

        return { data: '', message: '本地测试环境，图片上传失败已忽略' };
      });
  },



  // 保留原方法用于兼容性
  chooseResidentPhoto: function () {
    this.chooseFromAlbum();
  },



  // 提交表单
  submitForm: function () {
    if (this.data.authMethod === 'resident') {
      this.submitResidentForm();
    } else if (this.data.authMethod === 'property') {
      this.submitPropertyForm();
    }
  },

  // 预约时间选择器
  showAppointmentSelector: function () {
    this.setData({ showAppointmentSelectorFlag: true });
  },

  closeAppointmentSelector: function () {
    this.setData({ showAppointmentSelectorFlag: false });
  },

  selectDate: function (e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      selectedDateIndex: index,
      selectedTimeSlot: null
    });
  },

  selectTimeSlot: function (e) {
    const index = e.currentTarget.dataset.index;
    const available = e.currentTarget.dataset.available;

    if (!available) {
      return;
    }

    this.setData({ selectedTimeSlot: index });
  },

  confirmAppointment: function () {
    if (this.data.selectedTimeSlot === null) {
      return;
    }

    const selectedDate = this.data.availableDates[this.data.selectedDateIndex];
    const selectedTime = this.data.timeSlots[this.data.selectedTimeSlot];

    const appointmentTime = `${selectedDate.month}月${selectedDate.day}日 ${selectedDate.weekday} ${selectedTime.time}`;

    this.setData({
      appointmentTime: appointmentTime,
      appointmentError: false,
      showAppointmentSelectorFlag: false
    });
  },

  // 提交住户认证表单
  submitResidentForm: function () {
    console.log("开始提交住户认证表单");

    // 验证表单
    let isValid = true;

    // 验证姓名
    if (!this.validateName()) {
      isValid = false;
    }

    // 验证手机号
    if (!this.validatePhone()) {
      isValid = false;
    }

    // 验证验证码（如果不是已认证用户）
    if (!this.data.hasAuth && !this.validateVerifyCode()) {
      isValid = false;
    }

    // 验证身份证号
    if (!this.validateIdCard()) {
      isValid = false;
    }

    // // 根据认证方式验证不同的字段
    // if (this.data.authMethod === 'online') {
    //   // 线上认证，图片上传为非必填项（本地测试环境）
    //   console.log('线上认证，图片上传为非必填项（本地测试环境）');
    // } else {
    //   // 线下认证需要验证预约时间
    //   if (!this.data.appointmentTime) {
    //     this.setData({ appointmentError: true });
    //     wx.showToast({
    //       title: '请选择预约时间',
    //       icon: 'none'
    //     });
    //     isValid = false;
    //   }
    // }

    if (isValid) {
      console.log("表单验证通过，开始保存数据");

      // 显示加载状态
      this.setData({ submitting: true });

      // 准备认证数据
      const authData = {
        residentName: this.data.name,
        certificateType: 'ID_CARD',
        idCardNumber: this.data.idCard,
        phone: this.data.phone,
        codeKey: this.data.codeKey,
        code: this.data.verifyCode,
        photo: this.data.fileUploadPath || ''
      };

      // 调用实名认证API
      userApi.submitRealNameAuth(authData)
        .then(res => {
          this.showSuccessDialog('认证信息提交成功');
          // this.handleAuthSuccess();
        })
        .catch(err => {
          console.log('实名认证提交失败', err);
          // 本地测试环境，模拟成功
          wx.showToast({
            title: '住户认证失败' + (err.errorMessage ? (',' + err.errorMessage) : ''),
            icon: 'error',
            duration: 1500
          });

        });
    } else {
      console.log("表单验证失败");
      wx.showToast({
        title: '请填写正确的信息',
        icon: 'none'
      });
    }
  },

  // 提交物业员工认证表单
  submitPropertyForm: function () {
    console.log("开始提交物业员工认证表单");

    // 验证表单
    let isValid = true;

    // 验证姓名
    if (!this.validateName()) {
      isValid = false;
    }

    // 验证手机号
    if (!this.validatePhone()) {
      isValid = false;
    }

    // 验证身份证号
    if (!this.validateIdCard()) {
      isValid = false;
    }

    // 验证员工编号
    if (!this.validateEmployeeId()) {
      isValid = false;
    }

    // 验证部门
    if (!this.data.department) {
      this.setData({ departmentError: true });
      wx.showToast({
        title: '请选择所属部门',
        icon: 'none'
      });
      isValid = false;
    }

    // 验证职位
    if (!this.data.position) {
      this.setData({ positionError: true });
      wx.showToast({
        title: '请选择职位',
        icon: 'none'
      });
      isValid = false;
    }

    // 验证工作证照片（本地测试环境为非必填项）
    if (!this.data.workCardPhotoPath) {
      console.log('本地测试环境，工作证照片为非必填项');
    }

    if (isValid) {
      console.log("物业员工表单验证通过，开始保存数据");

      // 显示加载状态
      this.setData({ submitting: true });

      // 准备认证数据
      const authData = {
        name: this.data.name,
        phone: this.data.phone,
        idCard: this.data.idCard,
        employeeId: this.data.employeeId,
        department: this.data.department,
        position: this.data.position,
        workCardPhoto: this.data.workCardUploadPath || ''
      };

      // 模拟API调用（实际项目中应该调用真实的物业员工认证API）
      setTimeout(() => {
        this.setData({ submitting: false });

        // 保存物业员工信息到本地存储
        let userInfo = wx.getStorageSync('userInfo') || {};
        userInfo.realName = this.data.name;
        userInfo.phone = this.data.phone;
        userInfo.idCardNumber = this.data.idCard;
        userInfo.employeeId = this.data.employeeId;
        userInfo.department = this.data.department;
        userInfo.position = this.data.position;
        userInfo.authStatus = 'verified';
        userInfo.authMethod = 'property';
        userInfo.authTimestamp = Date.now().toString();
        userInfo.userType = 'property';

        wx.setStorageSync('userInfo', userInfo);
        wx.setStorageSync('isPropertyStaff', true);

        this.showSuccessDialog('物业员工认证提交成功');
      }, 2000);
    } else {
      console.log("物业员工表单验证失败");
      wx.showToast({
        title: '请填写正确的信息',
        icon: 'none'
      });
    }
  },

  // 处理认证成功
  handleAuthSuccess: function () {
    // 获取现有用户信息
    let userInfo = wx.getStorageSync('userInfo') || {};

    // 更新用户信息 - 直接更新userInfo对象，与API接口返回的memberDetail字段保持一致
    // 登录时获取的是memberDetail，但保存时直接保存为userInfo
    userInfo.realName = this.data.name;  // 对应API返回的memberDetail.realName
    userInfo.phone = this.data.phone;    // 对应API返回的memberDetail.phone

    // 线上认证才保存身份证号（新增字段，用于存储认证信息）
    if (this.data.authMethod === 'online') {
      userInfo.idCardNumber = this.data.idCard;
    }

    // 保存照片相关信息（新增字段，用于存储认证照片）
    userInfo.photoPath = this.data.residentPhotoPath;        // 本地照片路径
    userInfo.uploadedPhotoPath = this.data.fileUploadPath;   // 服务器照片路径

    // 设置认证状态相关字段（新增字段，用于标识认证状态）
    userInfo.authStatus = 'verified';
    userInfo.authMethod = this.data.authMethod;
    userInfo.authTimestamp = Date.now().toString();

    // 如果是线下认证，保存预约信息
    // if (this.data.authMethod === 'offline') {
    //   userInfo.appointmentTime = this.data.appointmentTime;
    //   userInfo.appointmentRemark = this.data.appointmentRemark;
    // }

    // 保持API接口原有字段不变
    // userInfo.role 保持原值（如 "tourist"，等待后端更新）
    // userInfo.id, openid, unionid, gender, birthday, avatarUrl, nickName, residentId 等保持不变

    // 统一保存用户信息
    wx.setStorageSync('userInfo', userInfo);

    // 恢复按钮状态
    // this.setData({ submitting: false });

    // 更新进度条状态
    // this.setData({ currentStep: 2 });

    // 显示成功弹窗
    // if (this.data.authMethod === 'online') {
      this.showSuccessDialog('住户认证提交成功');
    // } else {
    //   this.showSuccessDialog('预约提交成功', `您已成功预约线下认证，预约时间：${this.data.appointmentTime}，请携带身份证、房产证等原件前往物业管理处办理认证。点击确定返回首页。`);
    // }
  },



  // 显示成功弹窗
  showSuccessDialog: function (title, message) {
    this.setData({
      successTitle: title,
      successMessage: message,
      showSuccessDialogFlag: true
    });
  },

  closeSuccessDialog: function () {
    this.setData({ showSuccessDialogFlag: false });
  },

  confirmSuccess: function () {
    this.setData({ showSuccessDialogFlag: false });

    // 检查是否有重定向页面
    const redirectPage = wx.getStorageSync('redirectAfterAuth');

    if (redirectPage) {
      // 清除重定向信息
      wx.removeStorageSync('redirectAfterAuth');

      // 跳转到指定页面
      wx.redirectTo({
        url: redirectPage
      });
    } else {
      // 默认跳转到首页
      wx.switchTab({
        url: '/pages/index/index'
      });
    }
  },

  // 阻止事件冒泡
  stopPropagation: function () {
    return false;
  },

  // 显示已有认证的提示信息
  showAlreadyAuthenticatedTip: function () {

    const userInfo = wx.getStorageSync('userInfo') || {};

    //this.setData使用userInfo回填认证信息
//     avatarUrl: null
// birthday: "1990-06-05 23:00:00"
// gender: "man"
// id: "3"
// nickName: null
// openid: "o7nun7a9uuYlWTOSj6XlTtnz5M4U"
// phone: "13685156020"
// residentId: "1"
// role: "user"
// unionid: null
// realName: "卜凡傲"
    this.setData({
      // hasAuth:true,
      name:userInfo.realName,
      phone:userInfo.phone,
      idCard:userInfo.idCardNumber
    })

    wx.showModal({
      title: '温馨提示',
      content: '您已完成住户身份认证。',
      showCancel: false,
      confirmText: '我知道了'
    });
  },
  switchChange(e)
  {
    this.setData({
      isChecked:e.detail.value
    })
  }
});
