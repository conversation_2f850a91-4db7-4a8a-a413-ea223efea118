<view class="chat-container">
  <!-- 商品信息卡片 -->
  <view class="goods-card" wx:if="{{goodsInfo}}" bindtap="handleGoodsTap">
    <image class="goods-image" src="{{goodsInfo.image}}" mode="aspectFill"></image>
    <view class="goods-info">
      <view class="goods-title">{{goodsInfo.title}}</view>
      <view class="goods-price">¥{{goodsInfo.price}}</view>
    </view>
    <view class="goods-arrow">
      <image class="arrow-icon" src="/images/icons/arrow-right.svg"></image>
    </view>
  </view>

  <!-- 聊天消息列表 -->
  <scroll-view
    class="message-list"
    scroll-y="true"
    scroll-into-view="{{scrollIntoView}}"
    scroll-with-animation="true"
    enable-flex="true"
  >
    <view class="message-list-inner">
      <block wx:for="{{messages}}" wx:key="id">
        <!-- 对方发送的消息 -->
        <view class="message-item other" wx:if="{{item.senderId !== userInfo.id}}" id="{{item.id}}">
          <view class="avatar-container">
            <image class="avatar" src="{{targetAvatar}}"></image>
          </view>
          <view class="message-content">
            <view class="message-name">{{targetName}}</view>
            <view class="message-bubble" wx:if="{{!item.isImage}}">
              {{item.content}}
            </view>
            <view class="message-bubble image" wx:else bindtap="previewImage" data-url="{{item.content}}">
              <image class="message-image" src="{{item.content}}" mode="widthFix"></image>
            </view>
            <view class="message-time">{{item.time}}</view>
          </view>
        </view>

        <!-- 自己发送的消息 -->
        <view class="message-item self" wx:else id="{{item.id}}">
          <view class="avatar-container">
            <image class="avatar" src="{{userInfo.avatar}}"></image>
          </view>
          <view class="message-content">
            <view class="message-name">{{userInfo.name}}</view>
            <view class="message-bubble" wx:if="{{!item.isImage}}">
              {{item.content}}
            </view>
            <view class="message-bubble image" wx:else bindtap="previewImage" data-url="{{item.content}}">
              <image class="message-image" src="{{item.content}}" mode="widthFix"></image>
            </view>
            <view class="message-time">{{item.time}}</view>
          </view>
        </view>
      </block>

      <!-- 占位元素，确保滚动到底部 -->
      <view class="message-placeholder" id="msg{{messages.length}}"></view>
    </view>
  </scroll-view>

  <!-- 底部输入区域 -->
  <view class="input-area">
    <view class="input-actions">
      <view class="action-item" bindtap="sendImage">
        <image class="action-icon" src="/images/icons/image.svg"></image>
      </view>
    </view>
    <view class="input-box">
      <input
        class="message-input"
        value="{{inputContent}}"
        bindinput="handleInputChange"
        placeholder="请输入消息..."
        confirm-type="send"
        bindconfirm="sendMessage"
      />
    </view>
    <view class="send-btn {{inputContent ? 'active' : ''}}" bindtap="sendMessage">发送</view>
  </view>
</view>
