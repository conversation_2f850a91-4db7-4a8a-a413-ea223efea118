/* 公告发布/编辑页样式 */

.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 48rpx;
}

/* 导航栏样式已移除，使用微信小程序自带的导航栏 */

/* 表单区域 */
.form-container {
  padding: 32rpx;
}

.form-item {
  margin-bottom: 32rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.form-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  font-size: 32rpx;
  color: #333;
}

.title-input {
  font-size: 36rpx;
  font-weight: 500;
}

.char-count {
  font-size: 24rpx;
  color: #999;
  text-align: right;
  margin-top: 8rpx;
}

/* 选择器 */
.form-picker {
  width: 100%;
}

.picker-content {
  display: flex;
  align-items: center;
  height: 80rpx;
  font-size: 32rpx;
  color: #333;
}

.type-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  margin-right: 16rpx;
  background-size: 32rpx;
  background-position: center;
  background-repeat: no-repeat;
}

.type-icon.property_notice {
  background-color: rgba(64, 128, 255, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%234080FF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9'%3E%3C/path%3E%3Cpath d='M13.73 21a2 2 0 0 1-3.46 0'%3E%3C/path%3E%3C/svg%3E");
}

.type-icon.activity_notice {
  background-color: rgba(82, 196, 26, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%2352C41A' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolygon points='11 5 6 9 2 9 2 15 6 15 11 19 11 5'%3E%3C/polygon%3E%3Cpath d='M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07'%3E%3C/path%3E%3C/svg%3E");
}

.type-icon.emergency_notice {
  background-color: rgba(245, 34, 45, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23F5222D' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z'%3E%3C/path%3E%3Cline x1='12' y1='9' x2='12' y2='13'%3E%3C/line%3E%3Cline x1='12' y1='17' x2='12.01' y2='17'%3E%3C/line%3E%3C/svg%3E");
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: auto;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E") no-repeat center;
  background-size: contain;
}

/* 楼栋选择 */
.building-select {
  margin-top: -16rpx;
  background-color: #f8f8f8;
  box-shadow: none;
}

.building-select-btn {
  display: flex;
  align-items: center;
  height: 80rpx;
  font-size: 32rpx;
  color: #ff8c00;
  background-color: #fff;
  border-radius: 8rpx;
  padding: 0 24rpx;
}

.building-select-btn .arrow-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E");
}

.selected-buildings {
  margin-top: 16rpx;
  font-size: 28rpx;
  color: #666;
}

.building-names {
  color: #333;
}

/* 内容编辑区 */
.content-edit-area {
  padding-bottom: 32rpx;
}

/* 格式工具栏 */
.format-toolbar {
  display: flex;
  align-items: center;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx 8rpx 0 0;
  padding: 0 16rpx;
  margin-bottom: 2rpx;
  border: 1rpx solid #eee;
  border-bottom: none;
}

.toolbar-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 4rpx;
  border-radius: 4rpx;
}

.toolbar-btn:active {
  background-color: rgba(255, 140, 0, 0.1);
}

.toolbar-icon {
  width: 40rpx;
  height: 40rpx;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.bold-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23333333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 4h8a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z'%3E%3C/path%3E%3Cpath d='M6 12h9a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z'%3E%3C/path%3E%3C/svg%3E");
}

.italic-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23333333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='19' y1='4' x2='10' y2='4'%3E%3C/line%3E%3Cline x1='14' y1='20' x2='5' y2='20'%3E%3C/line%3E%3Cline x1='15' y1='4' x2='9' y2='20'%3E%3C/line%3E%3C/svg%3E");
}

.underline-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23333333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 3v7a6 6 0 0 0 6 6 6 6 0 0 0 6-6V3'%3E%3C/path%3E%3Cline x1='4' y1='21' x2='20' y2='21'%3E%3C/line%3E%3C/svg%3E");
}

.list-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23333333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='8' y1='6' x2='21' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='12' x2='21' y2='12'%3E%3C/line%3E%3Cline x1='8' y1='18' x2='21' y2='18'%3E%3C/line%3E%3Cline x1='3' y1='6' x2='3.01' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='12' x2='3.01' y2='12'%3E%3C/line%3E%3Cline x1='3' y1='18' x2='3.01' y2='18'%3E%3C/line%3E%3C/svg%3E");
}

.indent-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23333333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='3' y1='8' x2='21' y2='8'%3E%3C/line%3E%3Cline x1='3' y1='16' x2='21' y2='16'%3E%3C/line%3E%3Cline x1='8' y1='12' x2='21' y2='12'%3E%3C/line%3E%3Cpolyline points='4 16 8 12 4 8'%3E%3C/polyline%3E%3C/svg%3E");
}

.clear-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23333333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M5 12h14'%3E%3C/path%3E%3Cpath d='M12 5v14'%3E%3C/path%3E%3C/svg%3E");
  transform: rotate(45deg);
}

.toolbar-divider {
  width: 1rpx;
  height: 40rpx;
  background-color: #ddd;
  margin: 0 12rpx;
}

/* 内容文本框 */
.content-textarea {
  width: 100%;
  min-height: 300rpx;
  border: 1rpx solid #eee;
  border-radius: 0 0 8rpx 8rpx;
  padding: 16rpx;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  line-height: 1.5;
}

/* 图片上传区域 */
.image-section {
  margin-top: 32rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.section-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.section-subtitle {
  font-size: 24rpx;
  color: #999;
}

/* 图片上传 */
.image-upload-area {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8rpx;
}

.image-item {
  width: calc(33.33% - 16rpx);
  height: 200rpx;
  margin: 8rpx;
  position: relative;
  border-radius: 8rpx;
  overflow: hidden;
}

.uploaded-image, .uploading-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.delete-icon {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.5) url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23FFFFFF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E") no-repeat center;
  background-size: 24rpx;
  border-radius: 20rpx;
}

.add-image {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border: 1rpx dashed #ddd;
}

.add-icon {
  font-size: 60rpx;
  color: #999;
  line-height: 1;
}

.upload-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  padding: 0 8rpx;
}

.progress-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: rgba(255, 140, 0, 0.5);
  z-index: 1;
}

.progress-text {
  font-size: 24rpx;
  color: #fff;
  z-index: 2;
  position: relative;
}

.upload-progress.error {
  background-color: rgba(245, 34, 45, 0.5);
}

/* 附加选项区域 */
.options-area {
  padding: 24rpx;
}

.option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.switch-label {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  color: #333;
}

.option-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.pin-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 2L12 12'%3E%3C/path%3E%3Cpath d='M12 22L12 19'%3E%3C/path%3E%3Cpath d='M4.93 10.93L7.76 8.1a2 2 0 0 1 2.83 0l2.83 2.83a2 2 0 0 1 0 2.83l-2.83 2.83a2 2 0 0 1-2.83 0L4.93 13.76a2 2 0 0 1 0-2.83'%3E%3C/path%3E%3C/svg%3E");
}

.time-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
}

/* 定时发布时间选择 */
.scheduled-time {
  padding: 24rpx 0 8rpx;
}

.time-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 0 24rpx;
}

.time-value {
  font-size: 28rpx;
  color: #333;
}

/* 底部操作区 */
.bottom-actions {
  margin: 48rpx 0 24rpx;
}

.action-row {
  display: flex;
  margin-bottom: 16rpx;
}

.action-btn {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  border-radius: 44rpx;
  margin: 0 8rpx;
}

.preview, .save-draft {
  flex: 1;
}

.preview {
  background-color: #fff;
  color: #ff8c00;
  border: 1rpx solid #ff8c00;
}

.save-draft {
  background-color: #f8f8f8;
  color: #666;
  border: 1rpx solid #ddd;
}

.publish {
  background-color: #ff8c00;
  color: #fff;
  width: 100%;
  margin: 0 8rpx;
}

.auto-save-tip {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  margin-top: 16rpx;
}

/* 日期选择器 */
.date-picker-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.date-picker-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 1001;
  padding-bottom: env(safe-area-inset-bottom);
}

.date-picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  border-bottom: 1rpx solid #eee;
}

.date-picker-cancel, .date-picker-confirm {
  font-size: 32rpx;
}

.date-picker-cancel {
  color: #999;
}

.date-picker-confirm {
  color: #ff8c00;
}

.date-picker-title {
  font-size: 32rpx;
  font-weight: 500;
}

.date-picker {
  height: 400rpx;
  width: 100%;
}

.picker-item {
  line-height: 50px;
  text-align: center;
}

/* 暗黑模式 */
.dark-mode {
  background-color: #1f1f1f;
  color: #fff;
}

.dark-mode .form-item,
.dark-mode .building-select-btn,
.dark-mode .date-picker-container,
.dark-mode .save-draft {
  background-color: #2d2d2d;
}

.dark-mode .form-label {
  color: #aaa;
}

.dark-mode .form-input,
.dark-mode .picker-content,
.dark-mode .switch-label,
.dark-mode .time-select-label {
  color: #fff;
}

/* 移除了不需要的编辑器样式 */

.dark-mode .add-image {
  background-color: #3d3d3d;
  border-color: #4d4d4d;
}

.dark-mode .date-picker-header {
  border-bottom-color: #3d3d3d;
}

.dark-mode .save-draft {
  border-color: #ff8c00;
}
