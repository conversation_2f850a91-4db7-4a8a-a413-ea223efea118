// 添加房屋页面逻辑
const app = getApp()
const houseApi = require('../../../../../api/houseApi.js')

Page({
  data: {
    // 页面模式
    mode: 'add', // 'add' 或 'edit'
    houseId: '', // 编辑模式下的房屋ID
    pageTitle: '添加房屋',
    submitButtonText: '添加房屋',

    // 选择状态
    selectedBuildingId: null,
    selectedBuildingName: '',
    selectedBuildingIndex: -1,
    selectedRoomId: null,
    selectedRoomNumber: '',
    selectedRoomIndex: -1,
    selectedResidentType: '',

    // 数据列表
    buildings: [],
    rooms: [],

    // Picker数据
    buildingPickerRange: [],
    roomPickerRange: [],

    // 加载状态
    buildingsLoading: true,
    roomsLoading: false,
    isSubmitting: false,

    // 表单状态
    canSubmit: false,
    showSuccessDialog: false
  },

  onLoad: function (options) {
    // 获取URL参数
    this.fromPage = options.from || '';
    this.redirectAfterAdd = options.redirect === 'true';

    // 判断页面模式
    const mode = options.mode || 'add';
    const houseId = options.houseId || '';

    this.setData({
      mode: mode,
      houseId: houseId
    });

    if (mode === 'edit' && houseId) {
      // 编辑模式
      this.setData({
        pageTitle: '编辑房屋',
        submitButtonText: '保存修改'
      });

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: '编辑房屋'
      });

      // 加载房屋数据
      this.loadHouseData(houseId);
    } else {
      // 新增模式
      this.setData({
        pageTitle: '添加房屋',
        submitButtonText: '添加房屋'
      });

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: '添加房屋'
      });
    }

    // 加载楼栋数据
    this.loadBuildings();
  },

  // 加载楼栋数据
  loadBuildings: function () {
    this.setData({ buildingsLoading: true });
    var params={
      pageNum: 1,
      pageSize: 500,
      communityId: wx.getStorageSync('selectedCommunity').id,
      buildingNumber: ""
    }
    houseApi.getBuildingsByCommunity(params)
      .then(res => {
        console.log('楼栋数据：', res);

        if (res.code === 0 && res.data && Array.isArray(res.data.list)) {
          this.setData({
            buildings: res.data.list,
            buildingPickerRange: res.data.list,
            buildingsLoading: false
          });
        } else {
          console.error('获取楼栋数据失败：', res);
          this.setData({
            buildingsLoading: false,
            buildings: [],
            buildingPickerRange: []
          });
          wx.showToast({
            title: '获取楼栋数据失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('获取楼栋数据异常：', err);
        this.setData({
          buildingsLoading: false,
          buildings: [],
          buildingPickerRange: []
        });
        wx.showToast({
          title: '网络异常，请重试',
          icon: 'none'
        });
      });
  },

  // 加载房间数据
  loadRooms: function () {
    if (!this.data.selectedBuildingId) {
      return;
    }

    this.setData({ roomsLoading: true });

    houseApi.getRoomsByBuilding(this.data.selectedBuildingId)
      .then(res => {
        console.log('房间数据：', res);

        if (res.code === 0 && res.data) {
          // 处理分页数据或直接数组数据
          const roomList = Array.isArray(res.data.list) ? res.data.list :
                          Array.isArray(res.data) ? res.data : [];

          this.setData({
            rooms: roomList,
            roomPickerRange: roomList,
            roomsLoading: false
          });
        } else {
          console.error('获取房间数据失败：', res);
          this.setData({
            roomsLoading: false,
            rooms: [],
            roomPickerRange: []
          });
          wx.showToast({
            title: '获取房间数据失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('获取房间数据异常：', err);
        this.setData({
          roomsLoading: false,
          rooms: [],
          roomPickerRange: []
        });
        wx.showToast({
          title: '网络异常，请重试',
          icon: 'none'
        });
      });
  },

  // 加载房屋数据（编辑模式）
  loadHouseData: function (houseId) {
    // 这里需要根据实际API实现
    // 目前先从本地存储获取
    const houses = wx.getStorageSync('my_houses') || [];
    const house = houses.find(h => h.id === houseId);

    if (house) {
      this.setData({
        selectedBuildingId: house.buildingId,
        selectedBuildingName: house.buildingName,
        selectedRoomId: house.roomId,
        selectedRoomNumber: house.roomNumber,
        selectedResidentType: house.residentType
      });

      // 如果有楼栋ID，加载房间数据
      if (house.buildingId) {
        this.loadRooms();
      }

      // 设置picker索引（需要在数据加载完成后设置）
      setTimeout(() => {
        this.updatePickerIndexes();
      }, 500);

      this.checkFormStatus();
    }
  },

  // 更新picker索引
  updatePickerIndexes: function() {
    const { selectedBuildingId, selectedRoomId, buildingPickerRange, roomPickerRange } = this.data;

    // 设置楼栋索引
    let buildingIndex = -1;
    if (selectedBuildingId && buildingPickerRange.length > 0) {
      buildingIndex = buildingPickerRange.findIndex(item => item.id === selectedBuildingId);
    }

    // 设置房间索引
    let roomIndex = -1;
    if (selectedRoomId && roomPickerRange.length > 0) {
      roomIndex = roomPickerRange.findIndex(item => item.id === selectedRoomId);
    }

    this.setData({
      selectedBuildingIndex: buildingIndex,
      selectedRoomIndex: roomIndex
    });
  },

  // 楼栋picker选择事件
  onBuildingPickerChange: function(e) {
    const index = parseInt(e.detail.value);
    const building = this.data.buildingPickerRange[index];

    if (building) {
      this.setData({
        selectedBuildingIndex: index,
        selectedBuildingId: building.id,
        selectedBuildingName: building.buildingNumber,
        selectedRoomId: null,
        selectedRoomNumber: '',
        selectedRoomIndex: -1,
        selectedResidentType: '',
        rooms: [],
        roomPickerRange: []
      });

      // 加载该楼栋的房间数据
      this.loadRooms();
      this.checkFormStatus();
    }
  },

  // 房间picker选择事件
  onRoomPickerChange: function(e) {
    const index = parseInt(e.detail.value);
    const room = this.data.roomPickerRange[index];

    if (room) {
      this.setData({
        selectedRoomIndex: index,
        selectedRoomId: room.id,
        selectedRoomNumber: room.roomNumber,
        selectedResidentType: '' // 重置角色选择
      });

      this.checkFormStatus();
    }
  },

  // 选择角色
  selectResidentType: function (e) {
     
    const residentType = e.currentTarget.dataset.residenttype;

    this.setData({
      selectedResidentType: residentType
    });

    this.checkFormStatus();
  },

  // 检查表单状态
  checkFormStatus: function () {
    const { selectedBuildingId, selectedRoomId, selectedResidentType } = this.data;
    const canSubmit = selectedBuildingId && selectedRoomId && selectedResidentType;

    this.setData({
      canSubmit: canSubmit
    });
  },

  // 提交表单
  submitForm: function () {
    if (!this.data.canSubmit) {
      return;
    }

    const isEditMode = this.data.mode === 'edit';

    // 显示加载指示器
    this.setData({
      isSubmitting: true
    });

    // 构建房屋数据，严格按照接口文档
    const roomData = {
      buildingId: this.data.selectedBuildingId,
      roomId: this.data.selectedRoomId,
     residentType: this.data.selectedResidentType
    };

    // 编辑模式需要添加ID
    if (isEditMode) {
      roomData.id = this.data.houseId;
    }

    // 调用对应的API
    const apiCall = isEditMode ? houseApi.updateHouse(roomData) : houseApi.addHouse(roomData);

    apiCall
      .then(res => {

        console.log(isEditMode ? '编辑房屋成功' : '添加房屋成功', res);

        if (res.code === 0) {
          // 保存到本地存储作为备份
          if (!isEditMode) {
            this.saveHouseToLocal(roomData, res.data);
          }

          this.setData({
            isSubmitting: false,
            showSuccessDialog: true
          });
        } else {
           
          wx.showToast({
            title: res.errorMessage || (isEditMode ? '修改失败' : '添加失败'),
            icon: 'none'
          });
          this.setData({ isSubmitting: false });
        }
      })
      .catch(err => {
         
        console.error(isEditMode ? '编辑房屋异常' : '添加房屋异常', err);

        // API失败时，保存到本地存储（仅新增模式）
        if (!isEditMode) {
          this.saveHouseToLocal(roomData);
          this.setData({
            isSubmitting: false,
            showSuccessDialog: true
          });
        } else {
          wx.showToast({
            title: '网络异常，请重试',
            icon: 'none'
          });
          this.setData({ isSubmitting: false });
        }
      });
  },

  // 保存房屋信息到本地存储（备用方案）
  saveHouseToLocal: function (roomData, apiId) {
    const houseInfo = {
      id: apiId || 'house_' + Date.now(),
      buildingId: roomData.buildingId,
      buildingName: this.data.selectedBuildingName,
      roomId: roomData.roomId,
      roomNumber: this.data.selectedRoomNumber,
      fullAddress: `${this.data.selectedBuildingName}${this.data.selectedRoomNumber}`,
      residentType: roomData.residentType,
      residentTypeText: this.getResidentTypeText(roomData.residentType),
      isDefault: false,
      isVerified: false,
      createTime: new Date().toISOString()
    };

    // 从本地存储获取现有房屋列表
    let houses = wx.getStorageSync('my_houses') || [];

    // 如果是第一个房屋，设为默认
    if (houses.length === 0) {
      houseInfo.isDefault = true;
    }

    // 添加新房屋
    houses.push(houseInfo);

    // 保存到本地存储
    wx.setStorageSync('my_houses', houses);
  },

  // 获取角色文本
  getResidentTypeText: function (residentType) {
    switch (residentType) {
      case 'owner': return '业主';
      case 'tenant': return '租户';
      case 'family': return '家庭成员';
      default: return '未知';
    }
  },

  // 确认成功
  confirmSuccess: function () {
    this.setData({
      showSuccessDialog: false
    });

    // 如果需要重定向，则跳转到认证页面
    if (this.redirectAfterAdd) {
      wx.navigateTo({
        url: '/pages/auth/real-name/real-name'
      });
    } else {
      // 返回上一页
      this.goBack();
    }
  },

  // 返回上一页
  goBack: function () {
    wx.navigateBack({
      fail: function() {
        // 如果返回失败，则跳转到房屋列表页面
        wx.navigateTo({
          url: '/profilePackage/pages/profile/house/house'
        });
      }
    });
  }
})
