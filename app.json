{"pages": ["pages/index/index", "pages/profile/profile", "pages/auth/auth", "pages/auth/real-name/real-name", "pages/auth/property-auth/property-auth", "pages/community-select/community-select", "pages/about/about", "pages/settings/settings", "pages/goods/goods", "pages/messages/messages", "pages/points/points", "pages/points/rules/rules", "pages/points/record/record", "pages/points/earn/earn", "pages/points/level/level"], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#ff8c00", "navigationBarTitleText": "智慧物业", "navigationBarTextStyle": "white"}, "tabBar": {"color": "#8E8E93", "selectedColor": "#ff8c00", "backgroundColor": "#ffffff", "borderStyle": "white", "custom": true, "list": [{"pagePath": "pages/index/index", "text": "首页"}, {"pagePath": "pages/goods/goods", "text": "好物"}, {"pagePath": "pages/points/points", "text": "积分"}, {"pagePath": "pages/messages/messages", "text": "消息"}, {"pagePath": "pages/profile/profile", "text": "我的"}]}, "style": "v2", "sitemapLocation": "sitemap.json", "lazyCodeLoading": "requiredComponents", "usingComponents": {"svg-icon": "/components/svg-icon/svg-icon", "icon-text": "/components/icon-text/icon-text", "line-icon": "/components/line-icon/line-icon", "wx-icon": "/components/wx-icon/wx-icon", "virtual-list": "/components/virtual-list/index", "service-card": "/components/service-card/service-card", "info-card": "/components/info-card/info-card", "empty-state": "/components/empty-state/empty-state"}, "subpackages": [{"root": "communityPackage", "pages": ["pages/community/community", "pages/community/event-detail/event-detail", "pages/community/service/index", "pages/community/service/directory/index", "pages/community/service/directory/category", "pages/community/service/directory/detail", "pages/community/service/info/index", "pages/community/service/info/group-detail", "pages/community/service/info/lostfound-detail", "pages/community/service/consultation/index"]}, {"root": "propertyPackage", "pages": ["pages/property/property", "pages/property/staff/staff-list", "pages/property/staff/staff-detail", "pages/property/staff/staff-edit", "pages/property/visitor-verify/index", "pages/property/visitor-stats/index", "pages/property/staff/staff-stats", "pages/property/workorder/workorder", "pages/property/workorder/list/index", "pages/property/workorder/detail/index", "pages/property/workorder/stats/index", "pages/property/workorder/process/index", "pages/property/workorder/batch-assign/index", "pages/property/workorder/batch-complete/index", "pages/property/workorder/templates/index", "pages/property/announcement/announcement", "pages/property/announcement/publish/index", "pages/property/announcement/detail/index", "pages/property/announcement/building-select/index", "pages/property/announcement/building-select/tree-select", "pages/property/resident/resident", "pages/property/resident/review-list/index", "pages/property/resident/review-detail/index", "pages/property/resident/search/index", "pages/property/resident/detail/index", "pages/property/resident/add/index", "pages/property/resident/statistics/index", "pages/property/house/detail/index", "pages/property/house/add/index", "pages/property/vehicle/detail/index", "pages/property/vehicle/add/index", "pages/property/statistics/statistics", "pages/property/chart-test/chart-test", "pages/property/facility/facility", "pages/property/facility/detail/index", "pages/property/facility/repair/index", "pages/property/facility/maintenance/index", "pages/property/facility/add/index", "pages/property/facility/tasks/index", "pages/property/facility/tasks/execute/index", "pages/property/facility/task-management/index", "pages/property/login/login", "pages/property/inspection/inspection"]}, {"root": "servicePackage", "pages": ["pages/payment/payment", "pages/payment/history/history", "pages/payment/detail/detail", "pages/payment/invoice/invoice", "pages/payment/settings/settings", "pages/payment/analysis/analysis", "pages/messages/chat", "pages/access/access", "pages/recycle/green/index", "pages/recycle/guide/index", "pages/recycle/guide/detail", "pages/recycle/camera/index", "pages/recycle/quiz/index", "pages/recycle/map/index", "pages/recycle/contribution/index", "pages/recycle/appointment/index", "pages/recycle/my-appointments/index", "pages/garbage/garbage", "pages/recycle/recycle", "pages/repair/repair", "pages/visitor/registration/index", "pages/visitor/credential/index", "pages/visitor/list/index", "pages/visitor/batch-invite/index", "pages/workorder/list/index", "pages/workorder/detail/index", "pages/workorder/evaluate/index", "pages/renovation/index/index", "pages/renovation/create/create", "pages/renovation/plan/plan", "pages/renovation/materials/materials", "pages/renovation/commitment/commitment", "pages/renovation/status/status"]}, {"root": "profilePackage", "pages": ["pages/profile/my-activities/my-activities", "pages/profile/house/house", "pages/profile/vehicle/vehicle", "pages/profile/family/family", "pages/profile/house/add/add", "pages/profile/vehicle/add/add", "pages/profile/house/detail/detail", "pages/profile/complete-info/complete-info", "pages/goods/detail/detail", "pages/goods/publish/publish", "pages/goods/my/my", "pages/goods/order/order", "pages/goods/order/qrcode/qrcode", "pages/goods/verification/list/list", "pages/goods/verification/scan/scan", "pages/profile/tenant/tenant"]}], "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序位置接口的效果展示"}}, "requiredPrivateInfos": ["getLocation"], "resolveAlias": {"@/*": "/*"}}