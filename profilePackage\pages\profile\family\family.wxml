<!-- 我的家人页面 -->
<view class="container">
  <!-- 内容区域 -->
  <scroll-view scroll-y class="content-area">
    <view class="family-list" wx:if="{{familyMembers.length > 0}}">
      <view class="family-card" wx:for="{{familyMembers}}" wx:key="id">
        <view class="family-card-content">
          <view class="family-avatar">
            <text wx:if="{{!item.avatarUrl}}">{{item.nameInitial}}</text>
            <image wx:else src="{{item.avatarUrl}}" mode="aspectFill"></image>
          </view>
          <view class="family-info">
            <view class="family-name">{{item.name}}</view>
            <view class="family-relation-container">
              <view class="family-relation {{item.relationClass}}">{{item.relationText}}</view>
            </view>
            <view class="family-house" wx:if="{{item.house}}">
              <view class="house-icon"></view>
              <text>{{item.house}}</text>
            </view>
          </view>
          <view class="family-actions">
            <view class="action-button" catchtap="showActionMenu" data-id="{{item.id}}">
              <view class="more-icon"></view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{loading && familyMembers.length === 0}}">
      <view class="loading-text">正在加载家人信息...</view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:elif="{{!loading && familyMembers.length === 0}}">
      <view class="empty-icon"></view>
      <view class="empty-text">暂无家人信息</view>
      <view class="empty-subtext">点击下方按钮添加家人</view>
    </view>

    <!-- 加载更多状态 -->
    <view class="load-more" wx:if="{{loading && familyMembers.length > 0}}">
      <view class="loading-text">正在加载更多...</view>
    </view>
  </scroll-view>

  <!-- 底部添加按钮 -->
  <view class="add-button-container">
    <button class="add-button" bindtap="showAddFamilyModal">添加家人</button>
  </view>
</view>

<!-- 操作菜单 -->
<view class="action-menu-overlay" bindtap="hideActionMenu" wx:if="{{showActionMenu}}"></view>
<view class="action-menu" wx:if="{{showActionMenu}}" style="top: {{actionMenuTop}}px;">
  <view class="action-menu-item" bindtap="editFamilyMember">
    <view class="edit-icon"></view>
    <text>编辑</text>
  </view>
  <view class="action-menu-item delete" bindtap="showDeleteConfirm">
    <view class="delete-icon"></view>
    <text>删除</text>
  </view>
</view>

<!-- 添加/编辑家人弹窗 -->
<view class="modal-overlay {{showModal ? 'active' : ''}}" catchtouchmove="preventTouchMove">
  <view class="modal-container">
    <view class="modal-header">
      <view class="modal-title">{{isEditing ? '编辑家人' : '添加家人'}}</view>
      <view class="modal-actions">
        <view class="modal-close" bindtap="closeModal">
          <view class="close-icon"></view>
        </view>
      </view>
    </view>
    <view class="modal-body">
      <form>
        <view class="form-group">
          <view class="form-label">姓名<text class="required">*</text></view>
          <view class="form-input">
            <input type="text" placeholder="请输入家人姓名" value="{{formData.name}}" bindinput="onNameInput" maxlength="20" />
          </view>
        </view>
        <view class="form-group">
          <view class="form-label">手机号码</view>
          <view class="form-input">
            <input type="number" placeholder="请输入手机号码" value="{{formData.phone}}" bindinput="onPhoneInput" maxlength="11" />
          </view>
        </view>
        <view class="form-group">
          <view class="form-label">证件类型</view>
          <view class="form-selector" bindtap="showCertificateTypePicker">
            <text wx:if="{{formData.certificateType}}">
              <block wx:for="{{certificateTypes}}" wx:key="nameEn">
                <block wx:if="{{item.nameEn === formData.certificateType}}">{{item.nameCn}}</block>
              </block>
            </text>
            <text wx:else class="placeholder">请选择证件类型</text>
            <view class="arrow-icon"></view>
          </view>
        </view>
        <view class="form-group">
          <view class="form-label">证件号码</view>
          <view class="form-input">
            <input type="text" placeholder="请输入证件号码" value="{{formData.idCardNumber}}" bindinput="onIdCardInput" maxlength="18" />
          </view>
        </view>
        <view class="form-group">
          <view class="form-label">关联房屋</view>
          <view class="form-selector" bindtap="showHousePicker">
            <text wx:if="{{formData.house}}">{{formData.house}}</text>
            <text wx:else class="placeholder">请选择关联房屋</text>
            <view class="arrow-icon"></view>
          </view>
        </view>
      </form>
    </view>
    <view class="modal-footer">
      <button class="cancel-button" bindtap="closeModal">取消</button>
      <button class="submit-button" bindtap="saveFamilyMember" disabled="{{isSaving}}">{{isSaving ? '保存中...' : '保存'}}</button>
    </view>
  </view>
</view>

<!-- 关系选择器已替换为下拉菜单 -->

<!-- 证件类型选择器 -->
<view class="picker-overlay {{showCertificateTypePicker ? 'active' : ''}}" bindtap="hideCertificateTypePicker"></view>
<view class="picker-container {{showCertificateTypePicker ? 'active' : ''}}">
  <view class="picker-header">
    <view class="picker-title">选择证件类型</view>
    <view class="picker-close" bindtap="hideCertificateTypePicker">完成</view>
  </view>
  <view class="picker-content">
    <picker-view indicator-style="height: 40px;" style="width: 100%; height: 200px;" value="{{certificateTypePickerValue}}" bindchange="onCertificateTypeChange">
      <picker-view-column>
        <view wx:for="{{certificateTypes}}" wx:key="nameEn" class="picker-item">
          {{item.nameCn}}
        </view>
      </picker-view-column>
    </picker-view>
  </view>
</view>

<!-- 房屋选择器 -->
<view class="picker-overlay {{showHousePicker ? 'active' : ''}}" bindtap="hideHousePicker"></view>
<view class="picker-container {{showHousePicker ? 'active' : ''}}">
  <view class="picker-header">
    <view class="picker-title">选择关联房屋</view>
    <view class="picker-close" bindtap="hideHousePicker">完成</view>
  </view>
  <view class="picker-content">
    <picker-view indicator-style="height: 40px;" style="width: 100%; height: 200px;" value="{{housePickerValue}}" bindchange="onHouseChange">
      <picker-view-column>
        <view wx:for="{{houses}}" wx:key="id" class="picker-item {{item.isDefault ? 'default-house' : ''}}">
          {{item.fullAddress || item.address}}{{item.isDefault ? ' (默认)' : ''}}
        </view>
      </picker-view-column>
    </picker-view>
  </view>
</view>

<!-- 确认删除弹窗 -->
<view class="alert-overlay {{showDeleteConfirm ? 'show' : ''}}" bindtap="hideDeleteConfirm"></view>
<view class="custom-alert {{showDeleteConfirm ? 'show' : ''}}">
  <view class="alert-content">
    <view class="alert-title">确认删除</view>
    <view class="alert-message">确定要删除该家人信息吗？此操作不可恢复。</view>
  </view>
  <view class="alert-button-container confirm-buttons">
    <button class="alert-button alert-cancel-button" bindtap="hideDeleteConfirm">取消</button>
    <button class="alert-button alert-confirm-button" bindtap="deleteFamilyMember">删除</button>
  </view>
</view>
