<view class="visitor-verify-container">
  <!-- 标签页切换 -->
  <view class="tab-header">
    <view class="tab-item {{activeTab === 'scan' ? 'active' : ''}}" bindtap="switchTab" data-tab="scan">
      <image src="/images/icons/scan.svg" class="tab-icon"></image>
      <text>扫码核销</text>
    </view>
    <view class="tab-item {{activeTab === 'search' ? 'active' : ''}}" bindtap="switchTab" data-tab="search">
      <image src="/images/icons/search.svg" class="tab-icon"></image>
      <text>搜索核销</text>
    </view>
  </view>

  <!-- 扫码核销内容 -->
  <view class="tab-content" wx:if="{{activeTab === 'scan'}}">
    <view class="scan-container">
      <view class="scan-icon-container">
        <image src="/images/icons/qrcode.svg" class="scan-icon"></image>
      </view>
      <text class="scan-title">扫描访客二维码</text>
      <text class="scan-desc">请将访客出示的二维码放入框内，即可自动扫描</text>
      <button class="scan-button" bindtap="scanCode">
        <image src="/images/icons/camera.svg" class="button-icon"></image>
        <text>点击扫码</text>
      </button>
    </view>
  </view>

  <!-- 搜索核销内容 -->
  <view class="tab-content" wx:if="{{activeTab === 'search'}}">
    <view class="search-container">
      <view class="search-box">
        <image src="/images/icons/search.svg" class="search-icon"></image>
        <input 
          class="search-input" 
          placeholder="输入访客姓名或手机号" 
          value="{{searchKeyword}}"
          bindinput="inputKeyword"
          bindconfirm="handleSearch"
          confirm-type="search"
        />
        <view class="clear-icon" wx:if="{{searchKeyword}}" bindtap="clearKeyword">
          <image src="/images/icons/close.svg"></image>
        </view>
      </view>
      <button class="search-button" bindtap="handleSearch">搜索</button>
    </view>

    <!-- 搜索结果列表 -->
    <view class="search-results" wx:if="{{searchResults.length > 0}}">
      <view class="result-header">
        <text>搜索结果 ({{searchResults.length}})</text>
      </view>
      <view class="result-list">
        <view class="result-item" wx:for="{{searchResults}}" wx:key="id" bindtap="selectVisitor" data-id="{{item.id}}">
          <view class="visitor-info">
            <view class="visitor-name">{{item.name}}</view>
            <view class="visitor-phone">{{item.phone}}</view>
            <view class="visitor-time">
              <text>来访时间: {{item.date}} {{item.startTime}}</text>
            </view>
          </view>
          <view class="visitor-status {{item.status}}">
            <text>{{item.status === 'pending' ? '待到访' : 
                   item.status === 'visited' ? '已到访' : 
                   item.status === 'expired' ? '已过期' : '已作废'}}</text>
          </view>
          <view class="arrow-right">
            <image src="/images/icons/arrow-right.svg"></image>
          </view>
        </view>
      </view>
    </view>

    <!-- 无搜索结果 -->
    <view class="no-results" wx:elif="{{searchKeyword && searchResults.length === 0 && !isLoading}}">
      <image src="/images/icons/no-data.svg" class="no-data-icon"></image>
      <text>未找到相关访客</text>
    </view>

    <!-- 加载中 -->
    <view class="loading" wx:if="{{isLoading}}">
      <view class="loading-spinner"></view>
      <text>加载中...</text>
    </view>
  </view>

  <!-- 核验弹窗 -->
  <view class="verify-modal {{showVerifyModal ? 'show' : ''}}">
    <view class="modal-mask" bindtap="hideVerifyModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">核验访客</text>
        <view class="modal-close" bindtap="hideVerifyModal">
          <image src="/images/icons/close.svg" class="close-icon"></image>
        </view>
      </view>
      <view class="modal-body">
        <view class="verify-info">
          <text class="verify-title">确认核验以下访客？</text>
          <view class="verify-item">
            <text class="verify-label">访客姓名：</text>
            <text class="verify-value">{{currentVisitor.name}}</text>
          </view>
          <view class="verify-item">
            <text class="verify-label">手机号码：</text>
            <text class="verify-value">{{currentVisitor.phone}}</text>
          </view>
          <view class="verify-item" wx:if="{{currentVisitor.carNumber}}">
            <text class="verify-label">车牌号码：</text>
            <text class="verify-value">{{currentVisitor.carNumber}}</text>
          </view>
          <view class="verify-item">
            <text class="verify-label">来访目的：</text>
            <text class="verify-value">{{currentVisitor.purpose}}</text>
          </view>
          <view class="verify-item">
            <text class="verify-label">来访时间：</text>
            <text class="verify-value">{{currentVisitor.date}} {{currentVisitor.startTime}}</text>
          </view>
          <view class="verify-item">
            <text class="verify-label">被访人：</text>
            <text class="verify-value">{{currentVisitor.hostInfo.name}}</text>
          </view>
          <view class="verify-item">
            <text class="verify-label">被访地址：</text>
            <text class="verify-value">{{currentVisitor.hostInfo.address}}</text>
          </view>
        </view>
        <view class="verify-tip">
          <text>核验后将通知被访人，访客状态将变更为"已到访"</text>
        </view>
      </view>
      <view class="modal-footer">
        <button class="modal-btn modal-cancel-btn" bindtap="hideVerifyModal">取消</button>
        <button class="modal-btn modal-confirm-btn" bindtap="verifyVisitor">确认核验</button>
      </view>
    </view>
  </view>
</view>
