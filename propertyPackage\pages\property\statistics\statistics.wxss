/* 数据统计页面样式 */
.container {
  padding: 0;
  min-height: 100vh;
  background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
}

/* 标签页导航 */
.tab-nav {
  display: flex;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  position: sticky;
  top: 0;
  z-index: 10;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #ff8c00;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #ff8c00;
  border-radius: 2rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.error-icon {
  width: 80rpx;
  height: 80rpx;
  background-color: #f56565;
  border-radius: 50%;
  position: relative;
}

.error-icon::before,
.error-icon::after {
  content: '';
  position: absolute;
  width: 40rpx;
  height: 4rpx;
  background-color: #fff;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.error-icon::before {
  transform: translate(-50%, -50%) rotate(45deg);
}

.error-icon::after {
  transform: translate(-50%, -50%) rotate(-45deg);
}

.error-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}

.retry-button {
  margin-top: 30rpx;
  padding: 16rpx 40rpx;
  background-color: #ff8c00;
  color: #fff;
  font-size: 28rpx;
  border-radius: 30rpx;
}

/* 仪表盘样式 */
.dashboard-container {
  padding: 30rpx;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.dashboard-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
}

.dashboard-date {
  font-size: 24rpx;
  color: #999;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.dashboard-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
}

.card-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f0f7ff;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.work-order-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='32' height='32' fill='none' stroke='%23ff8c00' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z'%3E%3C/path%3E%3Cpolyline points='14 2 14 8 20 8'%3E%3C/polyline%3E%3Cline x1='16' y1='13' x2='8' y2='13'%3E%3C/line%3E%3Cline x1='16' y1='17' x2='8' y2='17'%3E%3C/line%3E%3Cpolyline points='10 9 9 9 8 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-size: 32rpx;
  background-repeat: no-repeat;
  background-position: center;
}

.visitor-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='32' height='32' fill='none' stroke='%23ff8c00' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2'%3E%3C/path%3E%3Ccircle cx='9' cy='7' r='4'%3E%3C/circle%3E%3Cpath d='M23 21v-2a4 4 0 0 0-3-3.87'%3E%3C/path%3E%3Cpath d='M16 3.13a4 4 0 0 1 0 7.75'%3E%3C/path%3E%3C/svg%3E");
  background-size: 32rpx;
  background-repeat: no-repeat;
  background-position: center;
}

.facility-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='32' height='32' fill='none' stroke='%23ff8c00' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='3'%3E%3C/circle%3E%3Cpath d='M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z'%3E%3C/path%3E%3C/svg%3E");
  background-size: 32rpx;
  background-repeat: no-repeat;
  background-position: center;
}

.resident-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='32' height='32' fill='none' stroke='%23ff8c00' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2'%3E%3C/path%3E%3Ccircle cx='9' cy='7' r='4'%3E%3C/circle%3E%3Cpath d='M23 21v-2a4 4 0 0 0-3-3.87'%3E%3C/path%3E%3Cpath d='M16 3.13a4 4 0 0 1 0 7.75'%3E%3C/path%3E%3C/svg%3E");
  background-size: 32rpx;
  background-repeat: no-repeat;
  background-position: center;
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  display: block;
}

.warning-text {
  color: #f59e0b;
}

.card-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-top: 4rpx;
}

.card-subtext {
  font-size: 22rpx;
  color: #999;
  display: block;
  margin-top: 8rpx;
}

/* 分类快照样式 */
.category-container {
  padding: 30rpx;
}

.category-header {
  margin-bottom: 30rpx;
}

.category-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.category-selector {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
  overflow: hidden;
}

.category-item {
  width: 33.33%;
  text-align: center;
  padding: 16rpx 0;
  font-size: 26rpx;
  color: #666;
  box-sizing: border-box;
  border-right: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
}

.category-item:nth-child(3n) {
  border-right: none;
}

.category-item:nth-child(4),
.category-item:nth-child(5),
.category-item:nth-child(6) {
  border-bottom: none;
}

.category-item.active {
  background-color: #ff8c00;
  color: #fff;
}

.time-filter {
  display: flex;
  margin-bottom: 20rpx;
}

.time-item {
  padding: 10rpx 24rpx;
  font-size: 24rpx;
  color: #666;
  background-color: #f0f0f0;
  border-radius: 30rpx;
  margin-right: 16rpx;
}

.time-item.active {
  background-color: #ff8c00;
  color: #fff;
}

/* 分类内容样式 */
.summary-row {
  display: flex;
  margin-bottom: 20rpx;
}

.summary-item {
  flex: 1;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-right: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.summary-item:last-child {
  margin-right: 0;
}

.summary-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
}

.summary-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-top: 8rpx;
}

/* 图表卡片样式 */
.chart-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.chart-header {
  margin-bottom: 20rpx;
}

.chart-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.chart-content {
  display: flex;
  align-items: center;
}

.pie-chart-container {
  width: 280rpx;
  height: 280rpx;
  position: relative;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: visible;
}

.chart-canvas {
  width: 100%;
  height: 100%;
}

.chart-legend-container {
  display: flex;
  flex-direction: column;
  padding: 20rpx 0;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  padding: 0 30rpx;
}

.legend-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: 4rpx;
  margin-right: 12rpx;
}

.legend-text {
  font-size: 24rpx;
  color: #666;
}

.bar-chart-container {
  width: 100%;
  height: 350rpx; /* 增加高度 */
  margin: 20rpx 0; /* 增加上下边距 */
}

.bar-chart {
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  height: 100%;
}

.bar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 80rpx;
}

.bar {
  width: 40rpx;
  border-radius: 4rpx 4rpx 0 0;
  transition: height 0.3s ease;
}

.bar-label {
  font-size: 22rpx;
  color: #666;
  margin-top: 8rpx;
}

/* 维保记录列表样式 */
.maintenance-list {
  width: 100%;
}

.maintenance-item {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.maintenance-item:last-child {
  border-bottom: none;
}

.maintenance-date {
  font-size: 26rpx;
  color: #333;
}

.maintenance-count {
  font-size: 26rpx;
  color: #ff8c00;
  font-weight: 500;
}

/* 暗黑模式样式 */
.darkMode {
  background-color: #1c1c1e;
}

.darkMode .tab-nav {
  background-color: #2c2c2e;
  border-bottom-color: #3a3a3c;
}

.darkMode .tab-item {
  color: #8e8e93;
}

.darkMode .tab-item.active {
  color: #ff8c00;
}

.darkMode .dashboard-title,
.darkMode .category-title {
  color: #f5f5f7;
}

.darkMode .dashboard-date {
  color: #8e8e93;
}

.darkMode .dashboard-card,
.darkMode .chart-card,
.darkMode .summary-item {
  background-color: #2c2c2e;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
}

.darkMode .card-value,
.darkMode .summary-value,
.darkMode .chart-title {
  color: #f5f5f7;
}

.darkMode .card-label,
.darkMode .summary-label,
.darkMode .legend-text,
.darkMode .bar-label,
.darkMode .maintenance-date {
  color: #8e8e93;
}

.darkMode .maintenance-item {
  border-bottom-color: #3a3a3c;
}

.darkMode .card-subtext {
  color: #6e6e73;
}

.darkMode .category-item {
  color: #8e8e93;
}

.darkMode .category-selector {
  background-color: #2c2c2e;
}

.darkMode .time-item {
  background-color: #3a3a3c;
  color: #8e8e93;
}

.darkMode .time-item.active {
  background-color: #ff8c00;
  color: #fff;
}

.darkMode .loading-text {
  color: #8e8e93;
}

.darkMode .error-text {
  color: #8e8e93;
}
