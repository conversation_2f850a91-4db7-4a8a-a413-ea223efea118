/* pages/property/workorder/process/index.wxss */

/* 容器 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 导航栏 */
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background: #ff8c00; /* 主品牌色 */
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  z-index: 100;
}

.nav-title {
  font-size: 18px;
  font-weight: 500;
}

.nav-back, .nav-action {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-icon {
  width: 24px;
  height: 24px;
}

/* 加载中提示 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 32px 0;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #eee;
  border-top-color: #ff8c00; /* 主品牌色 */
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #999;
  margin-top: 8px;
}

/* 主要内容 */
.content {
  flex: 1;
  margin-top: 88px; /* 导航栏高度(44px) + 状态栏高度(~44px) */
  padding: 16px;
  padding-bottom: 100px; /* 为底部按钮留出空间 */
}

/* 工单基本信息卡片 */
.order-info-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.order-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.order-meta {
  font-size: 14px;
  color: #666;
}

.order-id, .order-time {
  margin-bottom: 8px;
}

.order-status {
  margin-top: 12px;
}

.status-text {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 100px;
  font-size: 12px;
}

.status-pending {
  background-color: #fff3e0;
  color: #ff9800;
}

.status-processing {
  background-color: #e3f2fd;
  color: #2196f3;
}

.status-completed {
  background-color: #e8f5e9;
  color: #4caf50;
}

.status-cancelled {
  background-color: #f5f5f5;
  color: #9e9e9e;
}

/* 处理表单 */
.process-form {
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.required {
  color: #f44336;
}

.form-input {
  width: 100%;
  height: 44px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 0 12px;
  padding-right: 50px; /* 为模板按钮留出空间 */
  font-size: 14px;
  color: #333;
  box-sizing: border-box;
}

/* 带模板按钮的输入框 */
.textarea-with-template,
.input-with-template {
  position: relative;
  width: 100%;
}

/* 错误状态 */
.has-error .form-input,
.has-error .form-textarea,
.has-error {
  border-color: #f44336 !important;
}

/* 错误信息 */
.error-message {
  font-size: 12px;
  color: #f44336;
  margin-top: 4px;
}

.template-button {
  position: absolute;
  right: 12px;
  bottom: 12px;
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.template-icon {
  width: 20px;
  height: 20px;
}

.form-textarea {
  width: 100%;
  height: 120px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  padding-right: 50px; /* 为模板按钮留出空间 */
  font-size: 14px;
  color: #333;
  box-sizing: border-box;
}

.form-tips {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

/* 员工列表样式 */
.staff-list {
  max-height: 240px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.staff-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.staff-item:last-child {
  border-bottom: none;
}

.staff-item.selected {
  background-color: #fff7e6;
}

.staff-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.avatar-icon {
  width: 24px;
  height: 24px;
}

.staff-info {
  flex: 1;
}

.staff-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.staff-position {
  font-size: 12px;
  color: #999;
}

.staff-check {
  width: 24px;
  height: 24px;
}

.check-icon {
  width: 24px;
  height: 24px;
}

/* 上传图片样式 */
.upload-images {
  margin-top: 8px;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
}

.upload-image-item {
  position: relative;
  width: 80px;
  height: 80px;
  margin-right: 8px;
  margin-bottom: 8px;
}

.upload-image {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

.remove-image {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  border-radius: 10px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 16px;
  line-height: 20px;
  text-align: center;
}

.upload-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  background-color: #fafafa;
}

.upload-icon {
  font-size: 24px;
  color: #999;
  margin-bottom: 4px;
}

.upload-text {
  font-size: 12px;
  color: #999;
}

/* 底部按钮 */
.bottom-buttons {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  padding: 16px;
  background-color: #fff;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}

.btn-cancel, .btn-submit {
  flex: 1;
  height: 44px;
  line-height: 44px;
  text-align: center;
  border-radius: 22px;
  font-size: 16px;
}

.btn-cancel {
  background-color: #f5f5f5;
  color: #666;
  margin-right: 16px;
}

.btn-submit {
  background-color: #ff8c00;
  color: #fff;
}

.btn-cancel:active {
  background-color: #e0e0e0;
}

.btn-submit:active {
  background-color: #e67e00;
}

/* 模板选择器 */
.template-selector-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.template-selector {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-radius: 16px 16px 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  z-index: 1001;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
}

.template-selector.show {
  transform: translateY(0);
}

.template-selector-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.template-selector-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.template-selector-close {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #999;
}

.template-selector-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.empty-templates {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: #999;
  font-size: 14px;
}

.template-selector .template-item {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
}

.template-selector .template-item:last-child {
  margin-bottom: 0;
}

.template-selector .template-content {
  width: 100%;
}

.template-selector .template-text {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8px;
}

.template-selector .template-remark {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
  background-color: #f0f0f0;
  padding: 8px;
  border-radius: 4px;
}

.template-selector-footer {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
}

.template-selector-btn {
  width: 100%;
  height: 44px;
  line-height: 44px;
  text-align: center;
  border-radius: 22px;
  font-size: 16px;
  background-color: #f5f5f5;
  color: #666;
}

.template-selector-btn:active {
  background-color: #e0e0e0;
}

/* 暗黑模式样式 */
.darkMode {
  background-color: #1a1a1a;
}

.darkMode .order-info-card,
.darkMode .process-form,
.darkMode .bottom-buttons {
  background-color: #2a2a2a;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.darkMode .order-title {
  color: #fff;
}

.darkMode .order-meta,
.darkMode .form-label {
  color: #ccc;
}

.darkMode .form-input,
.darkMode .form-textarea,
.darkMode .staff-list {
  background-color: #333;
  border-color: #444;
  color: #fff;
}

.darkMode .staff-item {
  border-bottom-color: #444;
}

.darkMode .staff-name {
  color: #fff;
}

.darkMode .staff-position,
.darkMode .form-tips {
  color: #999;
}

.darkMode .upload-button {
  background-color: #333;
  border-color: #444;
}

.darkMode .btn-cancel {
  background-color: #333;
  color: #ccc;
}

.darkMode .btn-cancel:active {
  background-color: #444;
}

.darkMode .template-button {
  background-color: #333;
}

.darkMode .template-selector {
  background-color: #2a2a2a;
}

.darkMode .template-selector-header,
.darkMode .template-selector-footer {
  border-color: #333;
}

.darkMode .template-selector-title {
  color: #fff;
}

.darkMode .template-selector .template-item {
  background-color: #333;
}

.darkMode .template-selector .template-text {
  color: #fff;
}

.darkMode .template-selector .template-remark {
  color: #ccc;
  background-color: #444;
}

.darkMode .template-selector-btn {
  background-color: #333;
  color: #ccc;
}

.darkMode .template-selector-btn:active {
  background-color: #444;
}
