<view class="resident-stats-container">
  <!-- 标题栏 -->
  <view class="stats-header">
    <view class="stats-title">居民统计分析</view>
    <view class="stats-actions">
      <view class="stats-action" bindtap="exportStatistics">
        <text>导出</text>
      </view>
    </view>
  </view>

  <!-- 时间范围选择 -->
  <view class="time-range-selector">
    <view class="range-item {{timeRange === 'month' ? 'active' : ''}}" bindtap="switchTimeRange" data-range="month">月度</view>
    <view class="range-item {{timeRange === 'week' ? 'active' : ''}}" bindtap="switchTimeRange" data-range="week">周度</view>
  </view>

  <!-- 标签页切换 -->
  <view class="tab-header">
    <view class="tab-item {{activeTab === 'overview' ? 'active' : ''}}" bindtap="switchTab" data-tab="overview">
      <text>概览</text>
    </view>
    <view class="tab-item {{activeTab === 'trend' ? 'active' : ''}}" bindtap="switchTab" data-tab="trend">
      <text>趋势分析</text>
    </view>
    <view class="tab-item {{activeTab === 'age' ? 'active' : ''}}" bindtap="switchTab" data-tab="age">
      <text>年龄分布</text>
    </view>
    <view class="tab-item {{activeTab === 'house' ? 'active' : ''}}" bindtap="switchTab" data-tab="house">
      <text>房屋分析</text>
    </view>
  </view>

  <!-- 加载中 -->
  <view class="loading" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text>加载中...</text>
  </view>

  <!-- 概览标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'overview' && !isLoading}}">
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">居民总体情况</text>
        <text class="card-subtitle">{{timeRange === 'month' ? '近30天' : '近7天'}}</text>
      </view>
      <view class="card-body">
        <view class="summary-info">
          <view class="summary-item">
            <text class="summary-value">{{statistics.totalResidents}}</text>
            <text class="summary-label">总人数</text>
          </view>
          <view class="summary-item">
            <text class="summary-value">{{statistics.verifiedCount}}</text>
            <text class="summary-label">已认证</text>
          </view>
          <view class="summary-item">
            <text class="summary-value">{{statistics.unverifiedCount}}</text>
            <text class="summary-label">未认证</text>
          </view>
        </view>
      </view>
    </view>

    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">居民类型分布</text>
      </view>
      <view class="card-body">
        <view class="summary-info">
          <view class="summary-item">
            <text class="summary-value">{{statistics.ownerCount}}</text>
            <text class="summary-label">业主</text>
          </view>
          <view class="summary-item">
            <text class="summary-value">{{statistics.tenantCount}}</text>
            <text class="summary-label">租户</text>
          </view>
          <view class="summary-item">
            <text class="summary-value">{{statistics.familyCount}}</text>
            <text class="summary-label">家属</text>
          </view>
        </view>
        <view class="chart-container">
          <!-- 居民类型分布饼图 -->
          <view class="mock-chart pie-chart">
            <view class="pie-segments">
              <view wx:for="{{[0,1,2]}}" wx:key="index" class="pie-segment" style="transform: rotate({{index * 120}}deg); background-color: {{['#FF8C00', '#007AFF', '#34C759'][index]}};"></view>
            </view>
            <view class="pie-center"></view>
          </view>
          <view class="chart-legend">
            <view class="legend-item">
              <view class="legend-color" style="background-color: #FF8C00;"></view>
              <text class="legend-label">业主</text>
              <text class="legend-value">{{statistics.ownerCount}}人</text>
            </view>
            <view class="legend-item">
              <view class="legend-color" style="background-color: #007AFF;"></view>
              <text class="legend-label">租户</text>
              <text class="legend-value">{{statistics.tenantCount}}人</text>
            </view>
            <view class="legend-item">
              <view class="legend-color" style="background-color: #34C759;"></view>
              <text class="legend-label">家属</text>
              <text class="legend-value">{{statistics.familyCount}}人</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">性别分布</text>
      </view>
      <view class="card-body">
        <view class="summary-info">
          <view class="summary-item">
            <text class="summary-value">{{statistics.maleCount}}</text>
            <text class="summary-label">男性</text>
          </view>
          <view class="summary-item">
            <text class="summary-value">{{statistics.femaleCount}}</text>
            <text class="summary-label">女性</text>
          </view>
        </view>
        <view class="chart-container">
          <!-- 性别分布图表 -->
          <view class="gender-chart">
            <view class="gender-bar-container">
              <view class="gender-label">男性</view>
              <view class="gender-bar-wrapper">
                <view class="gender-bar male" style="width: {{statistics.maleCount * 100 / (statistics.maleCount + statistics.femaleCount)}}%;"></view>
                <text class="gender-value">{{statistics.maleCount}}人 ({{statistics.malePercent}}%)</text>
              </view>
            </view>
            <view class="gender-bar-container">
              <view class="gender-label">女性</view>
              <view class="gender-bar-wrapper">
                <view class="gender-bar female" style="width: {{statistics.femaleCount * 100 / (statistics.maleCount + statistics.femaleCount)}}%;"></view>
                <text class="gender-value">{{statistics.femaleCount}}人 ({{statistics.femalePercent}}%)</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 趋势标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'trend' && !isLoading}}">
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">居民增长趋势</text>
        <text class="card-subtitle">{{timeRange === 'month' ? '近12个月' : '近7天'}}</text>
      </view>
      <view class="card-body">
        <view class="summary-info">
          <view class="summary-item">
            <text class="summary-value">{{statistics.monthlyNewResidents[11]}}</text>
            <text class="summary-label">本月新增</text>
          </view>
          <view class="summary-item">
            <text class="summary-value">{{statistics.yearlyNewTotal}}</text>
            <text class="summary-label">年度新增</text>
          </view>
        </view>
        <view class="chart-container">
          <!-- 月度新增居民趋势图 -->
          <view class="mock-chart trend-chart">
            <view class="chart-y-axis">
              <text wx:for="{{[50,40,30,20,10,0]}}" wx:key="index">{{item}}</text>
            </view>
            <view class="chart-content">
              <view class="chart-bars">
                <view wx:for="{{statistics.monthlyNewResidents}}" wx:key="index" class="chart-bar" style="height: {{item * 2}}rpx;"></view>
              </view>
              <view class="chart-x-axis">
                <text wx:for="{{['1月','2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月']}}" wx:key="index" class="x-label">{{item}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 年龄标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'age' && !isLoading}}">
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">居民年龄分布</text>
      </view>
      <view class="card-body">
        <view class="chart-container">
          <!-- 年龄分布图表 -->
          <view class="age-chart">
            <view wx:for="{{statistics.ageDistribution}}" wx:key="index" class="age-item">
              <view class="age-bar-container">
                <view class="age-label">{{item.range}}</view>
                <view class="age-bar-wrapper">
                  <view class="age-bar" style="width: {{item.percent}}%; background-color: {{['#4f46e5', '#3b82f6', '#60a5fa', '#93c5fd', '#bfdbfe'][index]}};"></view>
                  <text class="age-value">{{item.count}}人 ({{item.percent}}%)</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 房屋标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'house' && !isLoading}}">
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">房屋类型分布</text>
      </view>
      <view class="card-body">
        <view class="chart-container">
          <!-- 房屋类型分布图表 -->
          <view class="mock-chart donut-chart">
            <view class="donut-segments">
              <view wx:for="{{statistics.houseTypeDistribution}}" wx:key="index" class="donut-segment" style="transform: rotate({{index * 72}}deg); background-color: {{['#FF8C00', '#007AFF', '#34C759', '#FF3B30', '#5856D6'][index]}};"></view>
            </view>
            <view class="donut-center"></view>
          </view>
          <view class="chart-legend">
            <view wx:for="{{statistics.houseTypeDistribution}}" wx:key="index" class="legend-item">
              <view class="legend-color" style="background-color: {{['#FF8C00', '#007AFF', '#34C759', '#FF3B30', '#5856D6'][index]}};"></view>
              <text class="legend-label">{{item.type}}</text>
              <text class="legend-value">{{item.count}}户</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">楼栋居民分布</text>
      </view>
      <view class="card-body">
        <view class="chart-container">
          <!-- 楼栋分布图表 -->
          <view class="mock-chart trend-chart">
            <view class="chart-y-axis">
              <text wx:for="{{[60,50,40,30,20,10,0]}}" wx:key="index">{{item}}</text>
            </view>
            <view class="chart-content">
              <view class="chart-bars">
                <view wx:for="{{statistics.buildingDistribution}}" wx:key="index" class="chart-bar" style="height: {{item.count * 2}}rpx; background-color: #34C759;"></view>
              </view>
              <view class="chart-x-axis">
                <text wx:for="{{statistics.buildingDistribution}}" wx:key="index" class="x-label">{{item.building}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>