/* 我的房屋页面样式 */
page {
  background-color: #f4f5f7;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
  color: #333;
  line-height: 1.5;
  height: 100%;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}



/* 内容区域 */
.content-area {
  flex: 1;
  padding: 16px 20px;
  box-sizing: border-box;
  height: 100%;
  padding-bottom: 20px; /* 添加底部内边距 */
  overflow: hidden; /* 隐藏滚动条 */
}

/* 房屋列表样式 */
.house-count {
  font-size: 14px;
  color: #8e8e93;
  margin-bottom: 16px;
  padding-left: 4px;
}

.house-list {
  margin-bottom: 20px;
}

.house-card {
  background: white;
  border-radius: 16px;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  position: relative;
  transition: transform 0.2s, box-shadow 0.2s;
}

.house-card:active {
  transform: scale(0.98);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.house-card.default {
  border: 2px solid #007AFF;
}

.house-content {
  padding: 16px;
}

.house-address {
  font-size: 17px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.house-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

.house-role {
  font-size: 14px;
  color: #666;
}

.house-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  height: 22px;
  min-width: 40px;
  box-sizing: border-box;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  color: black;
}

.tag-default {
  background-color: #E1F0FF;
  color: #007AFF;
  margin-right: 6px;
}

.tag-verified {
  background-color: #E3F9E5;
  color: #34C759;
}

.tag-unverified {
  background-color: #F2F2F7;
  color: #8E8E93;
}



/* 添加按钮 */
.add-button-container {
  margin-top: 24px;
  margin-bottom: 16px;
  padding: 0 20px;
}

.add-button {
  width: 100%;
  height: 48px;
  background: #FF9500;
  color: white;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(255, 149, 0, 0.2);
  border: none;
}

.add-button:active {
  transform: scale(0.98);
  box-shadow: 0 1px 4px rgba(255, 149, 0, 0.15);
}

.add-icon {
  margin-right: 4px;
  font-size: 20px;
  line-height: 1;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px 40px;
  text-align: center;
}

.empty-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.house-icon {
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='none' stroke='%238e8e93' stroke-width='1.5' d='M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2z'%3E%3C/path%3E%3Cpolyline fill='none' stroke='%238e8e93' stroke-width='1.5' points='9 22 9 12 15 12 15 22'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.empty-text {
  font-size: 16px;
  color: #8e8e93;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: #c7c7cc;
}

/* 悬浮添加按钮 */
.floating-add-button {
  position: fixed;
  right: 20px;
  bottom: 30px;
  width: 56px;
  height: 56px;
  background: #007AFF;
  border-radius: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3);
  z-index: 1000;
  transition: all 0.2s ease;
}

.floating-add-button:active {
  transform: scale(0.95);
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);
}

.floating-add-button .add-icon {
  color: white;
  font-size: 24px;
  font-weight: 300;
  line-height: 1;
  margin: 0;
}

/* 加载状态样式 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100px 20px;
}

.loading-text {
  font-size: 16px;
  color: #8e8e93;
  margin-top: 20px;
}

/* 加载更多状态样式 */
.load-more-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  margin-top: 10px;
}

.load-more-text {
  font-size: 14px;
  color: #8e8e93;
  text-align: center;
}

/* 底部弹窗样式 */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 2000;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.25s, visibility 0.25s;
}

.popup-overlay.active {
  opacity: 1;
  visibility: visible;
}

.popup-container {
  width: 100%;
  background-color: #ffffff;
  border-radius: 16px 16px 0 0;
  overflow: hidden;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2001;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.popup-overlay.active .popup-container {
  transform: translateY(0);
}

.popup-header {
  padding: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.popup-title {
  font-size: 17px;
  font-weight: 600;
  color: #000;
}

.popup-close {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24px;
  color: #8e8e93;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 15px;
}

.popup-close:active {
  background-color: rgba(0, 0, 0, 0.05);
}

.popup-content {
  padding: 16px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 表单样式 */
.form-description {
  font-size: 14px;
  color: #8e8e93;
  line-height: 1.5;
  margin-bottom: 16px;
}

.form-container {
  background: white;
  border-radius: 16px;
  margin-bottom: 20px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.form-group {
  padding: 16px;
  border-bottom: 0.5px solid rgba(0, 0, 0, 0.05);
}

.form-group:last-child {
  border-bottom: none;
}

.form-label {
  display: block;
  font-size: 14px;
  color: #3a3a3c;
  margin-bottom: 8px;
  font-weight: 500;
}

/* 选择器样式 */
.picker-wrapper {
  position: relative;
}

.picker-content {
  width: 92%;
  height: 44px;
  padding: 0 12px;
  border: 0.5px solid rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  font-size: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: rgba(255, 255, 255, 0.8);
  transition: all 0.2s ease;
}

.picker-content:active {
  background-color: rgba(0, 0, 0, 0.02);
}

.picker-content.placeholder {
  color: #999;
}

.picker-arrow {
  color: #8e8e93;
  font-size: 16px;
}

/* 级联选择器样式 */
.cascade-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 2000;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.25s, visibility 0.25s;
}

.cascade-picker-overlay.active {
  opacity: 1;
  visibility: visible;
}

.cascade-picker-container {
  width: 100%;
  background-color: rgba(248, 248, 248, 0.95);
  border-radius: 12px 12px 0 0;
  overflow: hidden;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2001;
}

.cascade-picker-overlay.active .cascade-picker-container {
  transform: translateY(0);
}

.cascade-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 0.5px solid rgba(0, 0, 0, 0.1);
  background-color: rgba(248, 248, 248, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  flex-wrap: nowrap; /* 防止内容换行 */
}

.cascade-picker-title {
  font-size: 16px;
  font-weight: 600;
  color: #000;
  white-space: nowrap; /* 防止文字换行 */
  min-width: 80px; /* 确保标题有足够的宽度 */
  text-align: center; /* 文字居中 */
}

.cascade-picker-cancel,
.cascade-picker-confirm {
  font-size: 16px;
  color: #007AFF;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  font-weight: 400;
  line-height: 1.5;
}

.cascade-picker-cancel {
  opacity: 0.8;
}

.cascade-picker-content {
  display: flex;
  height: 220px;
  position: relative;
  background-color: rgba(248, 248, 248, 0.95);
}

.cascade-picker-column {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  position: relative;
  scroll-snap-type: y mandatory;
}

.cascade-picker-column:not(:last-child) {
  border-right: 0.5px solid rgba(0, 0, 0, 0.05);
}

.cascade-picker-item {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  font-size: 16px;
  color: #333;
  transition: all 0.15s;
  scroll-snap-align: center;
  position: relative;
}

.cascade-picker-item.active {
  color: #007AFF;
  font-weight: 500;
}

.cascade-picker-item.active::after {
  content: '';
  position: absolute;
  right: 10px;
  width: 18px;
  height: 18px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23007AFF'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  opacity: 0.9;
}

.cascade-picker-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  background: linear-gradient(
      to bottom,
      rgba(248, 248, 248, 0.95) 0%,
      rgba(248, 248, 248, 0.7) 15%,
      rgba(248, 248, 248, 0) 30%,
      rgba(248, 248, 248, 0) 70%,
      rgba(248, 248, 248, 0.7) 85%,
      rgba(248, 248, 248, 0.95) 100%
  );
}

.cascade-picker-indicator {
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  height: 44px;
  transform: translateY(-50%);
  border-top: 0.5px solid rgba(0, 0, 0, 0.1);
  border-bottom: 0.5px solid rgba(0, 0, 0, 0.1);
  pointer-events: none;
  background-color: rgba(0, 122, 255, 0.05);
}

/* 单选按钮组 */
.radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 8px;
}

.radio-option {
  flex: 1;
  min-width: 80px;
  height: 44px;
  border: 0.5px solid rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #3a3a3c;
  background-color: rgba(255, 255, 255, 0.8);
  transition: all 0.2s ease;
}

.radio-option:active {
  transform: scale(0.98);
  background-color: rgba(0, 0, 0, 0.02);
}

.radio-option.selected {
  border-color: #007AFF;
  background-color: rgba(0, 122, 255, 0.08);
  color: #007AFF;
  font-weight: 500;
}

/* 提交按钮样式 */
.submit-button-container {
  margin-top: 24px;
  margin-bottom: 16px;
  padding: 0;
  display: flex;
  justify-content: center;
  width: 100%;
}

/* 在弹窗中的提交按钮容器 */
.popup-content .submit-button-container {
  padding: 0 16px;
}

.submit-button {
  width: 90%;
  max-width: 320px;
  height: 50px;
  background: #007AFF;
  color: white;
  border-radius: 12px;
  border: none;
  font-size: 17px;
  font-weight: 600;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.2);
  letter-spacing: 0.5px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.submit-button:active {
  transform: scale(0.98);
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);
  background: #0071eb;
}

.submit-button.disabled {
  background: rgba(0, 122, 255, 0.3);
  cursor: default;
  box-shadow: none;
}

/* 加载指示器 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.loading-overlay.active {
  opacity: 1;
  visibility: visible;
}

.loading-spinner {
  width: 80px;
  height: 80px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(0, 122, 255, 0.2);
  border-top-color: #007AFF;
  border-radius: 50%;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 8px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 12px;
  color: #8e8e93;
  margin-top: 4px;
}

/* 确认弹窗样式 */
.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.confirm-dialog-overlay.active {
  opacity: 1;
  visibility: visible;
}

.confirm-dialog {
  width: 280px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 14px;
  overflow: hidden;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: scale(0.9);
  transition: transform 0.3s;
}

.confirm-dialog-overlay.active .confirm-dialog {
  transform: scale(1);
}

.confirm-dialog-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.confirm-dialog-icon {
  width: 56px;
  height: 56px;
  background-color: rgba(52, 199, 89, 0.1);
  border-radius: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 12px;
}

.confirm-dialog-title {
  font-size: 17px;
  font-weight: 600;
  color: #000;
  margin-bottom: 8px;
}

.confirm-dialog-message {
  font-size: 14px;
  color: #8e8e93;
  margin-bottom: 16px;
  line-height: 1.4;
}

.confirm-dialog-buttons {
  width: 100%;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
}

.confirm-dialog-button {
  flex: 1;
  padding: 14px 0;
  font-size: 17px;
  font-weight: 500;
  text-align: center;
  transition: background-color 0.2s;
}

.confirm-dialog-button.primary {
  color: #007AFF;
}

.confirm-dialog-button:active {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 新增房屋弹窗样式 */
.add-house-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.add-house-modal.show {
  opacity: 1;
  visibility: visible;
}

.add-house-modal .modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
}

.add-house-modal .modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 16px 16px 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
}

.add-house-modal.show .modal-content {
  transform: translateY(0);
}

.add-house-modal .modal-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.add-house-modal .modal-title {
  font-size: 17px;
  font-weight: 600;
  color: #333;
}

.add-house-modal .model-selected-view{
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
display: flex;
align-items: center;
}

.select-building-view{
  padding:5rpx 15px;
  margin-left: 15rpx;
  border: 1rpx solid #f0f0f0;
}

.add-house-modal .modal-close {
  position: absolute;
  right: 20px;
  font-size: 24px;
  color: #999;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 15px;
}

.add-house-modal .modal-close:active {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 已选择信息显示区域 */
.selection-info {
  padding: 12px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.selection-item-info {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  position: relative;
}

.selection-item-info:last-child {
  margin-bottom: 0;
}

.selection-label {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
  min-width: 70px;
}

.selection-value {
  font-size: 14px;
  color: #007AFF;
  font-weight: 500;
  flex: 1;
}

.back-button {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  background: #007AFF;
  border-radius: 12px;
  margin-left: 12px;
  transition: all 0.2s ease;
}

.back-button:active {
  background: #0056b3;
  transform: scale(0.95);
}

.back-text {
  font-size: 12px;
  color: white;
}

.add-house-modal .modal-body {
  flex: 1;
  padding: 20px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 搜索框样式 */
.search-container {
  margin-bottom: 20px;
}

.search-input {
  width: 100%;
  height: 44px;
  padding: 0 16px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  font-size: 16px;
  box-sizing: border-box;
}

.search-input:focus {
  border-color: #007AFF;
}

/* 内容滚动区域 */
.content-scroll {
  flex: 1;
  margin-bottom: 20px;
  max-height: 200px;
}

/* 小型空状态样式 */
.empty-state-small {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-text-small {
  font-size: 14px;
  color: #999;
}

/* 选择网格布局 */
.selection-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.selection-item {
  flex: 0 0 auto;
  min-width: 80px;
  padding: 12px 16px;
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  text-align: center;
  font-size: 14px;
  color: #333;
  transition: all 0.2s ease;
}

.selection-item:active {
  transform: scale(0.98);
}

.selection-item.selected {
  border-color: #007AFF;
  background: rgba(0, 122, 255, 0.1);
  color: #007AFF;
  font-weight: 500;
}



/* 住户身份选择区域 */
.resident-type-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.resident-type-grid {
  display: flex;
  gap: 12px;
}

.resident-type-item {
  flex: 1;
  padding: 12px 16px;
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  text-align: center;
  font-size: 14px;
  color: #333;
  transition: all 0.2s ease;
}

.resident-type-item:active {
  transform: scale(0.98);
}

.resident-type-item.selected {
  border-color: #007AFF;
  background: rgba(0, 122, 255, 0.1);
  color: #007AFF;
  font-weight: 500;
}

/* 弹窗底部操作栏 */
.add-house-modal .modal-footer {
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 12px;
  background: white;
}

.btn-cancel,
.btn-confirm {
  flex: 1;
  height: 44px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
}

.btn-cancel {
  background: #f8f9fa;
  color: #666;
}

.btn-cancel:active {
  background: #e9ecef;
}

.btn-confirm {
  background: #007AFF;
  color: white;
}

.btn-confirm.enabled:active {
  background: #0056b3;
}

.btn-confirm.disabled {
  background: #ccc;
  color: #999;
  opacity: 0.6;
}
