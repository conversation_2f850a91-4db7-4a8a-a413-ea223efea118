/* 公告列表页样式 */

.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 120rpx; /* 为底部添加按钮预留空间 */
}

/* 导航栏样式已移除，使用微信小程序自带的导航栏 */

/* 搜索框 */
.search-bar {
  padding: 24rpx 32rpx;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-top: 0;
}

.search-input-wrap {
  display: flex;
  align-items: center;
  height: 72rpx;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 24rpx;
}

.search-icon {
  width: 36rpx;
  height: 36rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E") no-repeat center;
  background-size: contain;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
}

.clear-icon {
  width: 36rpx;
  height: 36rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='15' y1='9' x2='9' y2='15'%3E%3C/line%3E%3Cline x1='9' y1='9' x2='15' y2='15'%3E%3C/line%3E%3C/svg%3E") no-repeat center;
  background-size: contain;
}

/* 分类筛选区 */
.filter-tabs {
  display: flex;
  white-space: nowrap;
  background-color: #fff;
  padding: 0 16rpx;
  border-bottom: 1rpx solid #eee;
}

.tab-item {
  display: inline-block;
  padding: 24rpx 16rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #ff8c00;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 16rpx;
  right: 16rpx;
  height: 4rpx;
  background-color: #ff8c00;
  border-radius: 2rpx;
}

/* 公告列表 */
.announcement-list {
  padding: 24rpx 32rpx;
}

.announcement-card {
  display: flex;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.announcement-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 24rpx;
  background-size: 48rpx;
  background-position: center;
  background-repeat: no-repeat;
}

.announcement-icon.property_notice {
  background-color: rgba(64, 128, 255, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9'%3E%3C/path%3E%3Cpath d='M13.73 21a2 2 0 0 1-3.46 0'%3E%3C/path%3E%3C/svg%3E");
}

.announcement-icon.activity_notice {
  background-color: rgba(82, 196, 26, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolygon points='11 5 6 9 2 9 2 15 6 15 11 19 11 5'%3E%3C/polygon%3E%3Cpath d='M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07'%3E%3C/path%3E%3C/svg%3E");
}

.announcement-icon.emergency_notice {
  background-color: rgba(245, 34, 45, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z'%3E%3C/path%3E%3Cline x1='12' y1='9' x2='12' y2='13'%3E%3C/line%3E%3Cline x1='12' y1='17' x2='12.01' y2='17'%3E%3C/line%3E%3C/svg%3E");
}

.announcement-content {
  flex: 1;
}

.announcement-title-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8rpx;
}

.announcement-title {
  flex: 1;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.announcement-tags {
  display: flex;
  flex-wrap: wrap;
  margin-left: 16rpx;
}

.tag {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  margin-left: 8rpx;
  margin-bottom: 8rpx;
  color: #fff;
}

.tag.pin {
  background-color: #ff8c00;
}

.tag.new {
  background-color: #52c41a;
}

.tag.draft {
  background-color: #999;
}

.announcement-preview {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.announcement-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24rpx;
  color: #999;
}

.info-left {
  display: flex;
  align-items: center;
}

.type-text {
  margin-right: 16rpx;
}

.info-right {
  display: flex;
  align-items: center;
}

.read-count {
  margin-right: 16rpx;
}

.more-icon {
  width: 40rpx;
  height: 40rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='1'%3E%3C/circle%3E%3Ccircle cx='19' cy='12' r='1'%3E%3C/circle%3E%3Ccircle cx='5' cy='12' r='1'%3E%3C/circle%3E%3C/svg%3E") no-repeat center;
  background-size: contain;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23CCCCCC' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='3' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='9' y1='9' x2='15' y2='9'%3E%3C/line%3E%3Cline x1='9' y1='13' x2='15' y2='13'%3E%3C/line%3E%3Cline x1='9' y1='17' x2='15' y2='17'%3E%3C/line%3E%3C/svg%3E") no-repeat center;
  background-size: contain;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16rpx;
}

.loading-spinner.small {
  width: 40rpx;
  height: 40rpx;
  border-width: 2rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载更多 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx 0;
}

/* 没有更多数据 */
.no-more {
  text-align: center;
  padding: 32rpx 0;
}

.no-more-text {
  font-size: 24rpx;
  color: #999;
}

/* 添加按钮 */
.add-button {
  position: fixed;
  right: 32rpx;
  bottom: 32rpx;
  width: 112rpx; /* 56px */
  height: 112rpx; /* 56px */
  background-color: #ff8c00;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(255, 140, 0, 0.3);
  z-index: 100;
}

.add-icon {
  font-size: 60rpx;
  color: #fff;
  line-height: 1;
}

/* 操作菜单 */
.action-menu-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.action-menu {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  z-index: 1001;
  padding-bottom: env(safe-area-inset-bottom);
}

.action-menu-title {
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #eee;
}

.action-menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  font-size: 32rpx;
  color: #333;
}

.action-menu-item.delete {
  color: #f5222d;
}

.action-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 24rpx;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.edit-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23333333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7'%3E%3C/path%3E%3Cpath d='M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z'%3E%3C/path%3E%3C/svg%3E");
}

.pin-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23333333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 8a4 4 0 1 1 8 0c0 1.098-.564 2.025-1.159 2.815L12 20 5.159 10.815C4.564 10.025 4 9.098 4 8a4 4 0 1 1 8 0z'%3E%3C/path%3E%3C/svg%3E");
}

.unpin-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23333333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 8a4 4 0 1 1 8 0c0 1.098-.564 2.025-1.159 2.815L12 20 5.159 10.815C4.564 10.025 4 9.098 4 8a4 4 0 1 1 8 0z'%3E%3C/path%3E%3Cline x1='4' y1='4' x2='20' y2='20'%3E%3C/line%3E%3C/svg%3E");
}

.retract-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23333333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='1 4 1 10 7 10'%3E%3C/polyline%3E%3Cpath d='M3.51 15a9 9 0 1 0 2.13-9.36L1 10'%3E%3C/path%3E%3C/svg%3E");
}

.delete-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23F5222D' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='3 6 5 6 21 6'%3E%3C/polyline%3E%3Cpath d='M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2'%3E%3C/path%3E%3Cline x1='10' y1='11' x2='10' y2='17'%3E%3C/line%3E%3Cline x1='14' y1='11' x2='14' y2='17'%3E%3C/line%3E%3C/svg%3E");
}

.action-menu-cancel {
  text-align: center;
  font-size: 32rpx;
  color: #333;
  padding: 32rpx 0;
  margin-top: 16rpx;
  background-color: #f8f8f8;
}

/* 暗黑模式 */
.dark-mode {
  background-color: #1f1f1f;
  color: #fff;
}

.dark-mode .search-bar,
.dark-mode .filter-tabs,
.dark-mode .announcement-card,
.dark-mode .action-menu,
.dark-mode .action-menu-cancel {
  background-color: #2d2d2d;
}

.dark-mode .search-input-wrap {
  background-color: #3d3d3d;
}

.dark-mode .announcement-title {
  color: #fff;
}

.dark-mode .announcement-preview {
  color: #aaa;
}

.dark-mode .tab-item {
  color: #aaa;
}

.dark-mode .filter-tabs {
  border-bottom-color: #3d3d3d;
}

.dark-mode .action-menu-item {
  color: #fff;
}

.dark-mode .action-menu-title {
  border-bottom-color: #3d3d3d;
}
