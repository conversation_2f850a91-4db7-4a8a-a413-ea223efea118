/* 引入访客公共样式 */
@import "/styles/visitor-common.wxss";

/* 我的访客列表页特定样式 */
.visitor-search-bar {
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #eee;
}

.visitor-search-input-container {
  display: flex;
  align-items: center;
  height: 36px;
  padding: 0 12px;
  background-color: #f0f0f0;
  border-radius: 18px;
}

.visitor-search-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.visitor-search-input {
  flex: 1;
  height: 36px;
  font-size: 14px;
  background-color: transparent;
  border: none;
}

.visitor-clear-btn {
  padding: 4px;
}

.visitor-clear-icon {
  width: 16px;
  height: 16px;
}

.visitor-filter-tabs {
  display: flex;
  white-space: nowrap;
  background-color: #fff;
  border-bottom: 1px solid #eee;
}

.visitor-filter-tab {
  display: inline-block;
  padding: 12px 16px;
  font-size: 14px;
  color: #666;
  position: relative;
}

.visitor-filter-tab.active {
  color: #4f46e5;
  font-weight: 500;
}

.visitor-filter-tab.active::after {
  content: '';
  position: absolute;
  left: 16px;
  right: 16px;
  bottom: 0;
  height: 2px;
  background-color: #4f46e5;
}

.visitor-group {
  margin-bottom: 16px;
}

.visitor-group-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: #f9fafb;
}

.visitor-group-title {
  font-size: 16px;
  font-weight: 500;
}

.visitor-group-count {
  font-size: 12px;
  color: #666;
}

.visitor-items {
  background-color: #fff;
}

.visitor-item {
  display: flex;
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.visitor-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: 500;
  color: #fff;
  margin-right: 12px;
}

.visitor-avatar.pending {
  background-color: #4f46e5;
}

.visitor-avatar.visited {
  background-color: #10b981;
}

.visitor-avatar.expired {
  background-color: #9ca3af;
}

.visitor-avatar.canceled {
  background-color: #ef4444;
}

.visitor-info {
  flex: 1;
}

.visitor-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.visitor-name {
  font-size: 16px;
  font-weight: 500;
  margin-right: 8px;
}

.visitor-tag {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  color: #fff;
}

.visitor-tag.pending {
  background-color: #4f46e5;
}

.visitor-tag.visited {
  background-color: #10b981;
}

.visitor-tag.expired {
  background-color: #9ca3af;
}

.visitor-tag.canceled {
  background-color: #ef4444;
}

.visitor-time {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.visitor-purpose {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.visitor-actions {
  display: flex;
  flex-wrap: wrap;
  margin-top: 8px;
}

.visitor-action-btn {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 8px;
  font-size: 12px;
  color: #4f46e5;
}

.visitor-action-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.visitor-load-more, .visitor-load-all {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  font-size: 14px;
  color: #666;
}

.visitor-loading-indicator {
  width: 16px;
  height: 16px;
  border: 2px solid #ddd;
  border-top-color: #4f46e5;
  border-radius: 50%;
  margin-right: 8px;
  animation: visitor-spin 1s linear infinite;
}

@keyframes visitor-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 延期选项样式 */
.visitor-extend-option {
  padding: 16px;
  border-bottom: 1px solid #eee;
  font-size: 16px;
}

.visitor-extend-option:last-child {
  border-bottom: none;
}
