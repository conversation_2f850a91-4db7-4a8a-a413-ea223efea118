// pages/property/resident/detail/index.js
const util = require('../../../../utils/util.js')

Page({
  data: {
    id: null, // 居民ID
    residentData: {}, // 居民数据
    activeTab: 'basic', // 当前激活的标签：basic, house, vehicle, record
    isEditing: false, // 是否处于编辑模式
    editData: {}, // 编辑数据
    showContactModal: false, // 是否显示联系方式弹窗
    showNotifyModal: false, // 是否显示发送通知弹窗
    notifyContent: '', // 通知内容
    submitting: false // 是否正在提交
  },

  onLoad: function(options) {
    const { id } = options;

    this.setData({
      id: id
    });

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '居民详情'
    });

    // 加载居民详情
    this.loadResidentDetail();
  },

  // 加载居民详情
  loadResidentDetail: function() {
    // 模拟加载数据
    setTimeout(() => {
      // 模拟居民数据
      const residentData = {
        id: this.data.id,
        name: '张三',
        phone: '138****1234',
        idNumber: '410******1234',
        gender: '男',
        birthDate: '1988-05-15',
        age: 35,
        status: 'verified',
        statusText: '已认证',
        type: 'owner',
        typeText: '业主',
        registerTime: '2023-05-15',
        lastUpdateTime: '2023-10-15',
        email: '<EMAIL>',
        emergencyContact: '李四',
        emergencyPhone: '139****5678',
        houses: [
          {
            id: 1,
            address: '3栋2单元502室',
            type: '三室两厅',
            area: '120平方米',
            relation: '业主',
            startDate: '2020-01-15'
          }
        ],
        vehicles: [
          {
            id: 1,
            plateNumber: '京A·12345',
            brand: '丰田',
            model: '卡罗拉',
            color: '白色',
            parkingSpace: 'A-123'
          }
        ],
        records: [
          {
            id: 1,
            type: 'identity',
            title: '提交实名认证',
            time: '2023-05-15 10:30',
            operator: '张三'
          },
          {
            id: 2,
            type: 'identity',
            title: '实名认证通过',
            time: '2023-05-16 14:20',
            operator: '管理员'
          },
          {
            id: 3,
            type: 'house',
            title: '关联房屋',
            time: '2023-05-17 09:45',
            operator: '张三'
          },
          {
            id: 4,
            type: 'vehicle',
            title: '添加车辆',
            time: '2023-05-20 16:30',
            operator: '张三'
          },
          {
            id: 5,
            type: 'contact',
            title: '更新联系方式',
            time: '2023-10-15 11:25',
            operator: '张三'
          }
        ]
      };

      this.setData({
        residentData: residentData,
        editData: JSON.parse(JSON.stringify(residentData)) // 深拷贝用于编辑
      });
    }, 500);
  },

  // 切换标签
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab
    });
  },

  // 进入编辑模式
  enterEditMode: function() {
    this.setData({
      isEditing: true
    });
  },

  // 取消编辑
  cancelEdit: function() {
    this.setData({
      isEditing: false,
      editData: JSON.parse(JSON.stringify(this.data.residentData)) // 重置编辑数据
    });
  },

  // 保存编辑
  saveEdit: function() {
    this.setData({
      submitting: true
    });

    // 模拟保存操作
    setTimeout(() => {
      // 更新居民数据
      this.setData({
        residentData: JSON.parse(JSON.stringify(this.data.editData)),
        isEditing: false,
        submitting: false
      });

      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });
    }, 1000);
  },

  // 编辑数据输入
  onEditInput: function(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    const editData = this.data.editData;

    // 更新对应字段
    editData[field] = value;

    this.setData({
      editData: editData
    });
  },

  // 显示联系方式弹窗
  showContactModal: function() {
    this.setData({
      showContactModal: true
    });
  },

  // 隐藏联系方式弹窗
  hideContactModal: function() {
    this.setData({
      showContactModal: false
    });
  },

  // 拨打电话
  makePhoneCall: function() {
    const phone = this.data.residentData.phone.replace(/\*/g, ''); // 假设这里能获取到完整手机号

    wx.makePhoneCall({
      phoneNumber: phone,
      success: () => {
        this.hideContactModal();
      },
      fail: (err) => {
        console.error('拨打电话失败:', err);
        wx.showToast({
          title: '拨打电话失败',
          icon: 'none'
        });
      }
    });
  },

  // 发送短信
  sendSMS: function() {
    const phone = this.data.residentData.phone.replace(/\*/g, ''); // 假设这里能获取到完整手机号

    wx.showToast({
      title: '发送短信功能开发中',
      icon: 'none'
    });

    this.hideContactModal();
  },

  // 显示发送通知弹窗
  showNotifyModal: function() {
    this.setData({
      showNotifyModal: true,
      notifyContent: ''
    });
  },

  // 隐藏发送通知弹窗
  hideNotifyModal: function() {
    this.setData({
      showNotifyModal: false
    });
  },

  // 输入通知内容
  inputNotifyContent: function(e) {
    this.setData({
      notifyContent: e.detail.value
    });
  },

  // 发送通知
  sendNotification: function() {
    if (!this.data.notifyContent.trim()) {
      wx.showToast({
        title: '请输入通知内容',
        icon: 'none'
      });
      return;
    }

    this.setData({
      submitting: true
    });

    // 模拟发送通知
    setTimeout(() => {
      this.setData({
        showNotifyModal: false,
        submitting: false
      });

      wx.showToast({
        title: '通知发送成功',
        icon: 'success'
      });
    }, 1000);
  },

  // 查看房屋详情
  viewHouseDetail: function(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/property/house/detail/index?id=${id}`
    });
  },

  // 查看车辆详情
  viewVehicleDetail: function(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/propertyPackage/pages/property/vehicle/detail/index?id=${id}`
    });
  },

  // 添加房屋
  addHouse: function() {
    wx.navigateTo({
      url: `/propertyPackage/pages/property/house/add/index?residentId=${this.data.id}`
    });
  },

  // 添加车辆
  addVehicle: function() {
    wx.navigateTo({
      url: `/propertyPackage/pages/property/vehicle/add/index?residentId=${this.data.id}`
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    // 重新加载居民详情
    this.loadResidentDetail();

    // 停止下拉刷新
    wx.stopPullDownRefresh();
  },

  // 阻止滑动穿透
  preventTouchMove: function() {
    return false;
  }
})