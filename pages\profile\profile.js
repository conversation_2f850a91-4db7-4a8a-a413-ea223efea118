// profile.js
const util = require('../../utils/util.js')
const PointsUtil = require('../../utils/points.js')
const app = getApp()
const TabbarManager = require('../../utils/tabbar-manager.js')
const houseApi = require('@/api/houseApi.js')
const vehicleApi = require('@/api/vehicleApi.js')
const familyApi = require('@/api/familyApi.js')

Page({
  data: {
    apiUrl: wx.getStorageSync('apiUrl') + '/common-api/v1/file/',
    isAuthenticated: false,
    userName: '',

    avatarUrl: '/images/default-avatar.svg',

    houseCount: 0,
    vehicleCount: 0,
    familyCount: 0,
    tenantCount: 0,
    points: 0,
    communityName: '',
    authStatusText: '未认证',
    authStatusClass: '',
    authBadgeText: '未认证',
    authBadgeClass: '',
    showAuthReminder: false,
    pendingUrl: '',
    darkMode: false
  },

  onLoad: function () {
    // 初始化页面数据
  },

  onShow: function () {

    console.log('my show')
    console.log('userInfo', wx.getStorageSync('userInfo'))
    this.checkAuthStatus()
    this.loadAssetCounts()
    this.loadCommunityInfo()
    this.loadUserPoints()

    // 检查是否有积分变动事件
    if (app.globalData && app.globalData.pointsChangeEvent) {
      console.log('个人中心页面检测到积分变动事件:', app.globalData.pointsChangeEvent);
      // 重新加载用户积分
      this.loadUserPoints();
    }

    // 更新底部tabbar选中状态
    TabbarManager.setTabbarSelected(this, 4)
  },

  // 加载资产数量
  loadAssetCounts: function () {
    // 检查是否满足加载条件：已登录 + 已选择小区 + 已认证
    const checkResult = util.checkLoginAndCommunity();
    const authResult = util.checkUserAuthenticated();

    if (!checkResult.canProceed || !authResult.isAuthenticated) {
      console.log('不满足加载条件，跳过资产数量加载');
      this.setData({
        houseCount: 0,
        vehicleCount: 0,
        familyCount: 0,
        tenantCount: 0
      });
      return;
    }

    // 满足条件时从API获取数据
    this.loadHouseData();
    this.loadVehicleData();
    this.loadFamilyData();
    this.loadTenantData();
  },

  // 加载车辆数据
  loadVehicleData: function () {
    const selectedCommunity = wx.getStorageSync('selectedCommunity');
    if (!selectedCommunity || !selectedCommunity.id) {
      console.log('未选择小区，跳过车辆数据加载');
      return;
    }

    const params = {
      pageNum: 1,
      pageSize: 100,
      communityId: selectedCommunity.id
    };

    vehicleApi.getVehicleList(params)
      .then(res => {
        console.log('车辆数据：', res);
        if (res.code === 0 && res.data && res.data.list) {
          this.setData({
            vehicleCount: res.data.list.length
          });
        } else {
          this.setData({
            vehicleCount: 0
          });
        }
      })
      .catch(err => {
        console.error('获取车辆列表异常：', err);
        this.setData({
          vehicleCount: 0
        });
      });
  },


  // 加载房屋数据
  loadHouseData: function () {
    const params = {
      pageNum: 1,
      pageSize: 100
    };

    houseApi.getHouseList(params)
      .then(res => {
        console.log('房屋数据：', res);
        if (res.code === 0 && res.data && res.data.list) {
          this.setData({
            houseCount: res.data.list.length
          });
        } else {
          this.setData({
            houseCount: 0
          });
        }
      })
      .catch(err => {
        console.error('获取房屋列表异常：', err);
        this.setData({
          houseCount: 0
        });
      });
  },

  // 加载家人数据
  loadFamilyData: function () {
    const params = {
      pageNum: 1,
      pageSize: 100,
      residentType: "family"
    };

    familyApi.getFamilyList(params)
      .then(res => {
        console.log('家人数据：', res);
        if (res.code === 0 && res.data && res.data.list) {
          this.setData({
            familyCount: res.data.list.length
          });
        } else {
          this.setData({
            familyCount: 0
          });
        }
      })
      .catch(err => {
        console.error('获取家人列表异常：', err);
        this.setData({
          familyCount: 0
        });
      });
  },

  // 加载租客数据
  loadTenantData: function () {
    const params = {
      pageNum: 1,
      pageSize: 100,
      residentType: "tenant"
    };

    familyApi.getFamilyList(params)
      .then(res => {
        console.log('家人数据：', res);
        if (res.code === 0 && res.data && res.data.list) {
          this.setData({
            tenantCount: res.data.list.length
          });
        } else {
          this.setData({
            tenantCount: 0
          });
        }
      })
      .catch(err => {
        console.error('获取租客列表异常：', err);
        this.setData({
          tenantCount: 0
        });
      });
  },


  // 加载小区信息
  loadCommunityInfo: function () {
    const communityInfo = wx.getStorageSync('selectedCommunity') || {}
    if (communityInfo.name) {
      this.setData({
        communityName: communityInfo.name
      })
    }
  },

  // 加载用户积分
  loadUserPoints: function () {
    // 检查是否已认证
    if (!this.data.isAuthenticated) {
      return;
    }

    // 使用积分工具获取用户积分
    PointsUtil.getUserPoints().then(points => {
      this.setData({
        points: points
      });
      console.log('个人中心页面加载积分:', points);
    }).catch(err => {
      console.error('获取用户积分失败:', err);
      // 设置默认积分为0
      this.setData({
        points: 0
      });
    });
  },

  // 检查认证状态
  checkAuthStatus: function () {
    const authResult = util.checkUserAuthenticated();
    const userInfo = authResult.userInfo;

    let userName = '';
    let avatarUrl = '/images/default-avatar.svg';

    if (userInfo) {
      userName = userInfo.userName || '';
      // 头像显示逻辑：显示时需要加上apiUrl前缀
      if (userInfo.avatarUrl) {
        avatarUrl = this.data.apiUrl + userInfo.avatarUrl;
      }
    }

    // 设置认证状态文本和样式
    let authStatusText = '未认证';
    let authStatusClass = '';
    let authBadgeText = '未认证';
    let authBadgeClass = '';

    if (authResult.isAuthenticated) {
      authStatusText = '已认证';
      authStatusClass = 'resident';
      authBadgeText = '已认证';
      authBadgeClass = 'verified';
    }

    this.setData({
      isAuthenticated: authResult.isAuthenticated,
      userName,
      avatarUrl,
      authStatusText,
      authStatusClass,
      authBadgeText,
      authBadgeClass
    });
  },

  // 头像点击
  changeAvatar: function () {
    // 检查用户是否已认证
    if (!this.data.isAuthenticated) {
      // 未认证，显示认证提醒
      this.showAuthReminder('/pages/auth/real-name/real-name')
      return
    }

    // 已认证，显示头像选择选项
    wx.showActionSheet({
      itemList: ['从相册选择', '拍照'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 从相册选择
          wx.chooseImage({
            count: 1,
            sizeType: ['compressed'],
            sourceType: ['album'],
            success: (res) => {
              this.setData({
                avatarUrl: res.tempFilePaths[0]
              })

              // 实际应上传到服务器
              wx.showToast({
                title: '头像已更新',
                icon: 'success'
              })
            }
          })
        } else if (res.tapIndex === 1) {
          // 拍照
          wx.chooseImage({
            count: 1,
            sizeType: ['compressed'],
            sourceType: ['camera'],
            success: (res) => {
              this.setData({
                avatarUrl: res.tempFilePaths[0]
              })

              // 实际应上传到服务器
              wx.showToast({
                title: '头像已更新',
                icon: 'success'
              })
            }
          })
        }
      }
    })
  },

  // 跳转到积分页面
  navigateToPoints: function () {
    // 检查用户是否已认证
    if (!this.data.isAuthenticated) {
      // 未认证，显示认证提醒
      this.showAuthReminder('/pages/points/points')
      return
    }

    // 已认证，直接跳转到积分页面
    wx.switchTab({
      url: '/pages/points/points'
    })
  },


  // 跳转到认证页面
  navigateToAuth: function () {
    const isAuthenticated = util.checkAuthentication()
    wx.navigateTo({
      url: '/pages/auth/real-name/real-name?hasAuth=' + isAuthenticated
    })
  },

  // 普通页面跳转
  navigateToPage: function (e) {
    const url = e.currentTarget.dataset.url;

    // 检查是否是需要特殊处理的页面
    if (url.includes('/house/') || url.includes('/vehicle/') ||
      url.includes('/family/') || url.includes('/tenant/')) {
      // 资产管理相关页面需要检测：已登录 + 已选择小区 + 已认证
      util.checkStatusAndNavigate(url, {
        requireAuth: true,
        requireCommunity: true
      });
    } else {
      // 普通页面直接跳转
      wx.navigateTo({
        url: url
      });
    }
  },

  // 需要认证的页面跳转（服务记录相关）
  navigateWithAuth: function (e) {
    const url = e.currentTarget.dataset.url;

    // 服务记录功能需要检测：已登录 + 已选择小区
    util.checkStatusAndNavigate(url, {
      requireCommunity: true
    });
  },

  // 显示认证提醒弹窗
  showAuthReminder: function (url) {
    this.setData({
      showAuthReminder: true,
      pendingUrl: url
    })
  },

  // 关闭认证提醒弹窗
  closeAuthReminder: function () {
    this.setData({
      showAuthReminder: false,
      pendingUrl: ''
    })
  },

  // 前往认证
  goToAuth: function () {
    this.setData({
      showAuthReminder: false
    })

    wx.navigateTo({
      url: '/pages/auth/real-name/real-name'
    })
  },

  // 意见反馈
  showFeedback: function () {
    wx.showModal({
      title: '意见反馈',
      content: '感谢您的反馈，请联系客服电话：400-123-4567',
      showCancel: false
    })
  },

  // 退出登录
  logout: function () {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除认证信息
          wx.removeStorageSync('isAuthenticated')
          wx.removeStorageSync('userName')

          // 清除积分变动事件
          if (app.globalData) {
            app.globalData.pointsChangeEvent = null;
          }

          // 更新页面状态
          this.setData({
            isAuthenticated: false,
            userName: '',
            authStatusText: '未认证',
            authStatusClass: '',
            authBadgeText: '未认证',
            authBadgeClass: '',
            points: 0
          })

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })
        }
      }
    })
  }
})
