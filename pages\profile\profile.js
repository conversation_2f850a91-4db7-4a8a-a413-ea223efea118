// profile.js
const util = require('../../utils/util.js')
const PointsUtil = require('../../utils/points.js')
const app = getApp()
const TabbarManager = require('../../utils/tabbar-manager.js')
const houseApi = require('@/api/houseApi.js')

Page({
  data: {
    apiUrl: wx.getStorageSync('apiUrl') + '/common-api/v1/file/',
    isAuthenticated: false,
    userName: '',

    avatarUrl: '/images/default-avatar.svg',

    houseCount: 0,
    vehicleCount: 0,
    familyCount: 0,
    points: 0,
    communityName: '',
    authStatusText: '未认证',
    authStatusClass: '',
    authBadgeText: '未认证',
    authBadgeClass: '',
    showAuthReminder: false,
    pendingUrl: '',
    darkMode: false
  },

  onLoad: function () {
    // 初始化页面数据
  },

  onShow: function () {

    console.log('my show')
    this.checkAuthStatus()
    this.loadAssetCounts()
    this.loadCommunityInfo()
    this.loadUserPoints()

    // 检查是否有积分变动事件
    if (app.globalData && app.globalData.pointsChangeEvent) {
      console.log('个人中心页面检测到积分变动事件:', app.globalData.pointsChangeEvent);
      // 重新加载用户积分
      this.loadUserPoints();
    }

    // 更新底部tabbar选中状态
    TabbarManager.setTabbarSelected(this, 4)
  },

  // 加载资产数量
  loadAssetCounts: function () {
    // 获取房屋数量
    // const houses = wx.getStorageSync('my_houses') || []
    this.loadHouseData()

    // 获取车辆数量
    const vehicles = wx.getStorageSync('my_vehicles') || []

    // 获取家人数量
    const familyMembers = wx.getStorageSync('family_members') || []

    this.setData({
      vehicleCount: vehicles.length,
      familyCount: familyMembers.length
    })
  },

  loadHouseData()
  {
    houseApi.getHouseList({
      pageNum: 1,
      pageSize: 100
    }).then(res => {
      if (res.code === 0 && res.data && res.data.list) {
        this.setData({
          houseCount: res.data.list.length
        
        })
        

      }

    }).catch(err => {
      console.error('获取房屋列表异常：', err)
      // 如果API异常，尝试从本地存储获取
      this.loadHousesFromLocal()
    })
  },


  // 加载小区信息
  loadCommunityInfo: function () {
    const communityInfo = wx.getStorageSync('selectedCommunity') || {}
    if (communityInfo.name) {
      this.setData({
        communityName: communityInfo.name
      })
    }
  },

  // 加载用户积分
  loadUserPoints: function () {
    // 检查是否已认证
    if (!this.data.isAuthenticated) {
      return;
    }

    // 使用积分工具获取用户积分
    PointsUtil.getUserPoints().then(points => {
      this.setData({
        points: points
      });
      console.log('个人中心页面加载积分:', points);
    }).catch(err => {
      console.error('获取用户积分失败:', err);
      // 设置默认积分为0
      this.setData({
        points: 0
      });
    });
  },

  // 检查认证状态
  checkAuthStatus: function () {
    const isAuthenticated = util.checkAuthentication()
     
    var userInfo = wx.getStorageSync('userInfo')

    var userName = ''
    var avatarUrl = ""
    if (userInfo) {
      userName = userInfo.userName || ''
      avatarUrl = (this.data.apiUrl+ userInfo.avatarUrl) || ''
    }

    // 设置认证状态文本和样式
    let authStatusText = '未认证'
    let authStatusClass = ''
    let authBadgeText = '未认证'
    let authBadgeClass = ''

    if (isAuthenticated) {
      authStatusText = '已认证'
      authStatusClass = 'resident'
      authBadgeText = '已认证'
      authBadgeClass = 'verified'
    }

    this.setData({
      isAuthenticated,
      userName,
      avatarUrl,
      authStatusText,
      authStatusClass,
      authBadgeText,
      authBadgeClass
    })
  },

  // 头像点击
  changeAvatar: function () {
    // 检查用户是否已认证
    if (!this.data.isAuthenticated) {
      // 未认证，显示认证提醒
      this.showAuthReminder('/pages/auth/real-name/real-name')
      return
    }

    // 已认证，显示头像选择选项
    wx.showActionSheet({
      itemList: ['从相册选择', '拍照'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 从相册选择
          wx.chooseImage({
            count: 1,
            sizeType: ['compressed'],
            sourceType: ['album'],
            success: (res) => {
              this.setData({
                avatarUrl: res.tempFilePaths[0]
              })

              // 实际应上传到服务器
              wx.showToast({
                title: '头像已更新',
                icon: 'success'
              })
            }
          })
        } else if (res.tapIndex === 1) {
          // 拍照
          wx.chooseImage({
            count: 1,
            sizeType: ['compressed'],
            sourceType: ['camera'],
            success: (res) => {
              this.setData({
                avatarUrl: res.tempFilePaths[0]
              })

              // 实际应上传到服务器
              wx.showToast({
                title: '头像已更新',
                icon: 'success'
              })
            }
          })
        }
      }
    })
  },

  // 跳转到积分页面
  navigateToPoints: function () {
    // 检查用户是否已认证
    if (!this.data.isAuthenticated) {
      // 未认证，显示认证提醒
      this.showAuthReminder('/pages/points/points')
      return
    }

    // 已认证，直接跳转到积分页面
    wx.switchTab({
      url: '/pages/points/points'
    })
  },


  // 跳转到认证页面
  navigateToAuth: function () {
    const isAuthenticated = util.checkAuthentication()
    wx.navigateTo({
      url: '/pages/auth/real-name/real-name?hasAuth=' + isAuthenticated
    })
  },

  // 普通页面跳转
  navigateToPage: function (e) {
    const url = e.currentTarget.dataset.url
    wx.navigateTo({
      url: url
    })
  },

  // 需要认证的页面跳转
  navigateWithAuth: function (e) {
    const url = e.currentTarget.dataset.url

    // 检查用户是否已认证
    if (!this.data.isAuthenticated) {
      // 未认证，显示认证提醒
      this.showAuthReminder(url)
      return
    }

    // 直接跳转到指定页面
    wx.navigateTo({
      url: url
    });
  },

  // 显示认证提醒弹窗
  showAuthReminder: function (url) {
    this.setData({
      showAuthReminder: true,
      pendingUrl: url
    })
  },

  // 关闭认证提醒弹窗
  closeAuthReminder: function () {
    this.setData({
      showAuthReminder: false,
      pendingUrl: ''
    })
  },

  // 前往认证
  goToAuth: function () {
    this.setData({
      showAuthReminder: false
    })

    wx.navigateTo({
      url: '/pages/auth/real-name/real-name'
    })
  },

  // 意见反馈
  showFeedback: function () {
    wx.showModal({
      title: '意见反馈',
      content: '感谢您的反馈，请联系客服电话：400-123-4567',
      showCancel: false
    })
  },

  // 退出登录
  logout: function () {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除认证信息
          wx.removeStorageSync('isAuthenticated')
          wx.removeStorageSync('userName')

          // 清除积分变动事件
          if (app.globalData) {
            app.globalData.pointsChangeEvent = null;
          }

          // 更新页面状态
          this.setData({
            isAuthenticated: false,
            userName: '',
            authStatusText: '未认证',
            authStatusClass: '',
            authBadgeText: '未认证',
            authBadgeClass: '',
            points: 0
          })

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })
        }
      }
    })
  }
})
