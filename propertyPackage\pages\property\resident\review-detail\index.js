// pages/property/resident/review-detail/index.js
const dateUtil = require('../../../../../utils/dateUtil.js')

Page({
  data: {
    id: null, // 审核ID
    type: '', // 审核类型：identity, house, vehicle
    reviewData: {}, // 审核数据
    reviewComment: '', // 审核意见
    rejectReason: '', // 拒绝原因
    showRejectModal: false, // 是否显示拒绝原因弹窗
    showApproveModal: false, // 是否显示通过确认弹窗
    submitting: false, // 是否正在提交
    statusText: '' // 状态文本
  },

  onLoad: function(options) {
    const { id, type } = options;

    this.setData({
      id: id,
      type: type
    });

    // 设置导航栏标题
    let title = '审核详情';
    switch(type) {
      case 'identity':
        title = '实名认证审核';
        break;
      case 'house':
        title = '房屋信息审核';
        break;
      case 'vehicle':
        title = '车辆信息审核';
        break;
    }

    wx.setNavigationBarTitle({
      title: title
    });

    // 加载审核详情
    this.loadReviewDetail();
  },

  // 加载审核详情
  loadReviewDetail: function() {
    // 模拟加载数据
    setTimeout(() => {
      // 根据审核类型生成不同的模拟数据
      let reviewData = {};

      switch(this.data.type) {
        case 'identity':
          reviewData = {
            id: this.data.id,
            type: 'identity',
            applicant: '张三',
            phone: '138****1234',
            idNumber: '410******1234',
            gender: '男',
            birthDate: '1988-05-15',
            submitTime: '2023-10-15 14:30',
            status: 'pending',
            idCardFront: 'https://example.com/id-front.jpg', // 模拟图片URL
            idCardBack: 'https://example.com/id-back.jpg', // 模拟图片URL
            relatedHouse: '阳光小区 3栋2单元502室'
          };
          break;
        case 'house':
          reviewData = {
            id: this.data.id,
            type: 'house',
            applicant: '李四',
            phone: '139****5678',
            address: '3栋2单元501室',
            houseType: '三室两厅',
            propertyNumber: 'PROPERTY123456',
            submitTime: '2023-10-14 10:15',
            status: 'pending',
            propertyPhoto: 'https://example.com/property.jpg', // 模拟图片URL
            relatedResident: '李四 (业主)'
          };
          break;
        case 'vehicle':
          reviewData = {
            id: this.data.id,
            type: 'vehicle',
            applicant: '王五',
            phone: '137****9012',
            plateNumber: '京A·12345',
            vehicleType: '小型汽车',
            brand: '丰田',
            model: '卡罗拉',
            color: '白色',
            submitTime: '2023-10-13 16:45',
            status: 'pending',
            vehiclePhoto: 'https://example.com/vehicle.jpg', // 模拟图片URL
            licensePlatePhoto: 'https://example.com/license.jpg', // 模拟图片URL
            relatedResident: '王五 (业主)'
          };
          break;
      }

      // 设置状态文本
      let statusText = '';
      switch(reviewData.status) {
        case 'pending':
          statusText = '待审核';
          break;
        case 'approved':
          statusText = '已通过';
          break;
        case 'rejected':
          statusText = '已拒绝';
          break;
      }

      this.setData({
        reviewData: reviewData,
        statusText: statusText
      });
    }, 500);
  },

  // 预览图片
  previewImage: function(e) {
    const url = e.currentTarget.dataset.url;
    wx.previewImage({
      current: url,
      urls: [url]
    });
  },

  // 输入审核意见
  inputReviewComment: function(e) {
    this.setData({
      reviewComment: e.detail.value
    });
  },

  // 显示拒绝原因弹窗
  showRejectModal: function() {
    this.setData({
      showRejectModal: true,
      rejectReason: ''
    });
  },

  // 显示通过确认弹窗
  showApproveModal: function() {
    this.setData({
      showApproveModal: true
    });
  },

  // 隐藏弹窗
  hideModal: function() {
    this.setData({
      showRejectModal: false,
      showApproveModal: false
    });
  },

  // 输入拒绝原因
  inputRejectReason: function(e) {
    this.setData({
      rejectReason: e.detail.value
    });
  },

  // 确认拒绝
  confirmReject: function() {
    if (!this.data.rejectReason.trim()) {
      wx.showToast({
        title: '请输入拒绝原因',
        icon: 'none'
      });
      return;
    }

    this.setData({
      submitting: true
    });

    // 模拟提交
    setTimeout(() => {
      // 更新审核状态
      const reviewData = this.data.reviewData;
      reviewData.status = 'rejected';
      reviewData.rejectReason = this.data.rejectReason;
      reviewData.reviewer = '管理员';
      reviewData.reviewTime = dateUtil.formatTime(new Date());

      this.setData({
        reviewData: reviewData,
        statusText: '已拒绝',
        showRejectModal: false,
        submitting: false
      });

      wx.showToast({
        title: '已拒绝审核',
        icon: 'success'
      });
    }, 1000);
  },

  // 确认通过
  confirmApprove: function() {
    this.setData({
      submitting: true
    });

    // 模拟提交
    setTimeout(() => {
      // 更新审核状态
      const reviewData = this.data.reviewData;
      reviewData.status = 'approved';
      reviewData.reviewComment = this.data.reviewComment;
      reviewData.reviewer = '管理员';
      reviewData.reviewTime = dateUtil.formatTime(new Date());

      this.setData({
        reviewData: reviewData,
        statusText: '已通过',
        showApproveModal: false,
        submitting: false
      });

      wx.showToast({
        title: '已通过审核',
        icon: 'success'
      });
    }, 1000);
  },

  // 阻止滑动穿透
  preventTouchMove: function() {
    return false;
  }
})
