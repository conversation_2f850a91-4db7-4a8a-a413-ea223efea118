// settings.js
const util = require('../../utils/util.js')

Page({
  data: {
    notificationEnabled: true,
    soundEnabled: false,
    darkModeEnabled: false,
    cacheSize: '2.5MB'
  },

  toggleNotification: function(e) {
    this.setData({
      notificationEnabled: e.detail.value
    })

    // 保存设置
    wx.setStorageSync('notificationEnabled', e.detail.value)

    wx.showToast({
      title: e.detail.value ? '已开启消息通知' : '已关闭消息通知',
      icon: 'none'
    })
  },

  toggleSound: function(e) {
    this.setData({
      soundEnabled: e.detail.value
    })

    // 保存设置
    wx.setStorageSync('soundEnabled', e.detail.value)

    wx.showToast({
      title: e.detail.value ? '已开启声音提醒' : '已关闭声音提醒',
      icon: 'none'
    })
  },

  toggleDarkMode: function(e) {
    const isDarkMode = e.detail.value

    this.setData({
      darkModeEnabled: isDarkMode
    })

    try {
      // 使用工具类中的函数切换暗黑模式
      util.toggleDarkMode(isDarkMode)

      wx.showToast({
        title: isDarkMode ? '已开启暗黑模式' : '已关闭暗黑模式',
        icon: 'none'
      })
    } catch (error) {
      console.log('切换暗黑模式失败：', error)
      wx.showToast({
        title: '切换模式失败，请重试',
        icon: 'none'
      })
    }
  },



  clearCache: function() {
    wx.showModal({
      title: '清除缓存',
      content: '确定要清除缓存吗？',
      success: (res) => {
        if (res.confirm) {
          // 模拟清除缓存
          setTimeout(() => {
            this.setData({
              cacheSize: '0KB'
            })

            wx.showToast({
              title: '缓存已清除',
              icon: 'success'
            })
          }, 1000)
        }
      }
    })
  },

  checkUpdate: function() {
    wx.showLoading({
      title: '检查更新中...',
      mask: true
    })

    // 模拟检查更新
    setTimeout(() => {
      wx.hideLoading()

      wx.showModal({
        title: '检查更新',
        content: '当前已是最新版本',
        showCancel: false
      })
    }, 1500)
  },

  navigateToPage: function(e) {
    const url = e.currentTarget.dataset.url
    wx.navigateTo({
      url: url
    })
  },

  showPrivacyPolicy: function() {
    wx.showModal({
      title: '隐私政策',
      content: '这是隐私政策内容...',
      showCancel: false
    })
  },

  showUserAgreement: function() {
    wx.showModal({
      title: '用户协议',
      content: '这是用户协议内容...',
      showCancel: false
    })
  },

  onLoad: function() {
    // 加载保存的设置
    const notificationEnabled = wx.getStorageSync('notificationEnabled')
    const soundEnabled = wx.getStorageSync('soundEnabled')
    const darkModeEnabled = util.isDarkMode()

    if (notificationEnabled !== '') {
      this.setData({
        notificationEnabled
      })
    }

    if (soundEnabled !== '') {
      this.setData({
        soundEnabled
      })
    }

    // 设置暗黑模式状态
    this.setData({
      darkModeEnabled
    })

    // 应用当前的暗黑模式设置
    util.applyDarkMode()
  },

  onShow: function() {
    // 当页面显示时，检查并应用当前的暗黑模式设置
    const darkModeEnabled = util.isDarkMode()

    // 更新当前页面的暗黑模式状态
    if (this.data.darkModeEnabled !== darkModeEnabled) {
      this.setData({
        darkModeEnabled
      })
    }
  },
  
  // 跳转到完善信息页面
  navigateToCompleteInfo: function () {
    wx.navigateTo({
      url: '/profilePackage/pages/profile/complete-info/complete-info'
    })
  },
})
