// 格式化时间
const formatTime = (date, format = 'default') => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  if (format === 'YYYYMMDDHHmmss') {
    return `${year}${formatNumber(month)}${formatNumber(day)}${formatNumber(hour)}${formatNumber(minute)}${formatNumber(second)}`
  }

  return `${[year, month, day].map(formatNumber).join('/')} ${[hour, minute, second].map(formatNumber).join(':')}`
}


// 格式化日期
const formatDate = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()

  return `${year}-${formatNumber(month)}-${formatNumber(day)}`
}

function addHoursToTime(visitTime, hoursToAdd) {
  // 将字符串转换为Date对象
  const visitDate = new Date(visitTime);

  // 创建一个新的Date对象，其时间与visitDate相同，但小时数增加了hoursToAdd
  const resultDate = new Date(visitDate);
  resultDate.setHours(visitDate.getHours() + hoursToAdd);

  // 格式化结果为"YYYY-MM-DD HH:MM:SS"格式
  const year = resultDate.getFullYear();
  const month = String(resultDate.getMonth() + 1).padStart(2, '0'); // 月份是从0开始的
  const day = String(resultDate.getDate()).padStart(2, '0');
  const hours = String(resultDate.getHours()).padStart(2, '0');
  const minutes = String(resultDate.getMinutes()).padStart(2, '0');
  const seconds = String(resultDate.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}


// 获取当前时间并格式化
function getCurrentTime() {
  var date = new Date()
  var month = date.getMonth() + 1
  var day = date.getDate()

  var hours = date.getHours()
  var minutes = date.getMinutes()
  var seconds = date.getSeconds()

  if (month <= 9) {
    month = '0' + month
  }
  if (day <= 9) {
    day = '0' + day
  }

  if (hours <= 9) {
    hours = '0' + hours
  }

  if (minutes <= 9) {
    minutes = '0' + minutes
  }

  if (seconds <= 9) {
    seconds = '0' + seconds
  }


  return date.getFullYear() + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds

}

// 格式化ISO时间字符串为 YYYY-MM-DD HH:MM:SS 格式
function formatISOToDateTime(isoString) {
  if (!isoString) return ''

  try {
    const date = new Date(isoString)

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return isoString // 如果无法解析，返回原字符串
    }

    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } catch (error) {
    console.error('格式化ISO时间失败：', error)
    return isoString // 出错时返回原字符串
  }
}

// 格式化数字，补零
const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : `0${n}`
}

// 格式化时间为 HH:MM 格式
const formatTimeHM = (date) => {
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  return `${hours}:${minutes}`;
}

// 格式化日期时间为 YYYY-MM-DD HH:MM:SS 格式
const formatDateTime = (date) => {
  const dateStr = formatDate(date);
  const timeStr = formatTimeHM(date);
  const seconds = date.getSeconds().toString().padStart(2, '0');
  return `${dateStr} ${timeStr}:${seconds}`;
}

// 解析日期时间字符串为Date对象
const parseDateTime = (dateStr, timeStr) => {
  return new Date(`${dateStr} ${timeStr}`.replace(/-/g, '/'));
}

// 计算两个日期之间的小时差
const getHoursDiff = (startDate, endDate) => {
  const diffMs = endDate - startDate;
  return Math.floor(diffMs / (1000 * 60 * 60));
}

// 获取未来N天的日期数组
const getFutureDates = (days) => {
  const dates = [];
  const today = new Date();

  for (let i = 0; i < days; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() + i);
    dates.push({
      date: formatDate(date),
      label: i === 0 ? '今天' : i === 1 ? '明天' : formatDate(date)
    });
  }

  return dates;
}

// 生成小时数组
const getHours = () => {
  const hours = [];
  for (let i = 0; i < 24; i++) {
    hours.push(i.toString().padStart(2, '0'));
  }
  return hours;
}

// 生成分钟数组
const getMinutes = (step = 5) => {
  const minutes = [];
  for (let i = 0; i < 60; i += step) {
    minutes.push(i.toString().padStart(2, '0'));
  }
  return minutes;
}

module.exports = {
  formatTime,
  formatDate,
  addHoursToTime,
  getCurrentTime,
  formatISOToDateTime,
  formatNumber,
  formatTimeHM,
  formatDateTime,
  parseDateTime,
  getHoursDiff,
  getFutureDates,
  getHours,
  getMinutes
}
