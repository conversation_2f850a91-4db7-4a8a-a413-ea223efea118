<!--profile.wxml-->
<view class="container page-bottom-safe-area {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}">
  <!-- 个人信息卡片 -->
  <view class="profile-section">
    <view class="profile-header">
      <view class="avatar-section">
        <view class="avatar" bindtap="changeAvatar">
          <image src="{{isAuthenticated ? avatarUrl : '/images/default-avatar.svg'}}" mode="aspectFill"></image>
        </view>
   
      </view>
      <view class="user-info">
        <view class="user-name-container">
          <!-- 认证状态标签 -->
          <view class="auth-status-tag {{authStatusClass}}">
            <text class="auth-status-icon"></text>
            <text>{{authStatusText}}</text>
          </view>
          <view class="user-points" bindtap="navigateToPoints">
            <view class="points-icon"></view>
            <text>{{points || '暂无积分'}}</text>
          </view>
        </view>
        <view class="user-location">{{houseCount>0?'已绑定房产':'尚未绑定房产'}}</view>
      </view>
    </view>
  </view>

  <!-- 账户相关功能 -->
  <view class="group-title">账户管理</view>
  <view class="function-group">
    <view class="function-item" bindtap="navigateToAuth">
      <view class="function-icon auth-icon"></view>
      <view class="function-title">实名认证</view>
      <view class="auth-badge {{authBadgeClass}}">{{authBadgeText}}</view>
      <view class="function-arrow">›</view>
    </view>


    <view class="function-item" bindtap="navigateToPage" data-url="/profilePackage/pages/profile/house/house">
      <view class="function-icon house-icon"></view>
      <view class="function-title">我的房产</view>
      <view class="function-badge" wx:if="{{houseCount > 0}}">{{houseCount}}</view>
      <view class="function-arrow">›</view>
    </view>
    <view class="function-item" bindtap="navigateToPage" data-url="/profilePackage/pages/profile/vehicle/vehicle">
      <view class="function-icon vehicle-icon"></view>
      <view class="function-title">我的车辆</view>
      <view class="function-badge" wx:if="{{vehicleCount > 0}}">{{vehicleCount}}</view>
      <view class="function-arrow">›</view>
    </view>
    <view class="function-item" bindtap="navigateToPage" data-url="/profilePackage/pages/profile/family/family">
      <view class="function-icon family-icon"></view>
      <view class="function-title">我的家人</view>
      <view class="function-badge" wx:if="{{familyCount > 0}}">{{familyCount}}</view>
      <view class="function-arrow">›</view>
    </view>

    <view class="function-item" bindtap="navigateToPage" data-url="/profilePackage/pages/profile/tenant/tenant">
      <view class="function-icon family-icon"></view>
      <view class="function-title">我的租客</view>
      <view class="function-badge" wx:if="{{familyCount > 0}}">{{familyCount}}</view>
      <view class="function-arrow">›</view>
    </view>

  </view>

  <!-- 服务记录 -->
  <view class="group-title">服务记录</view>
  <view class="function-group">
    <view class="function-item" bindtap="navigateWithAuth" data-url="/servicePackage/pages/workorder/list/index">
      <view class="function-icon repair-icon"></view>
      <view class="function-title">我的工单</view>
      <view class="function-arrow">›</view>
    </view>
    <view class="function-item" bindtap="navigateWithAuth" data-url="/servicePackage/pages/payment/history/history">
      <view class="function-icon payment-icon"></view>
      <view class="function-title">我的缴费</view>
      <view class="function-arrow">›</view>
    </view>
    <view class="function-item" bindtap="navigateWithAuth" data-url="/profilePackage/pages/goods/my/my">
      <view class="function-icon goods-icon"></view>
      <view class="function-title">我的好物</view>
      <view class="function-arrow">›</view>
    </view>
    <view class="function-item" bindtap="navigateWithAuth" data-url="/profilePackage/pages/profile/my-activities/my-activities">
      <view class="function-icon activity-icon"></view>
      <view class="function-title">我的活动</view>
      <view class="function-arrow">›</view>
    </view>
  </view>

  <!-- 设置 -->
  <view class="group-title">设置</view>
  <view class="function-group">
    <view class="function-item" bindtap="navigateToPage" data-url="/pages/settings/settings">
      <view class="function-icon settings-icon"></view>
      <view class="function-title">通用设置</view>
      <view class="function-arrow">›</view>
    </view>
    <view class="function-item" bindtap="navigateToPage" data-url="/pages/about/about">
      <view class="function-icon about-icon"></view>
      <view class="function-title">关于我们</view>
      <view class="function-arrow">›</view>
    </view>
    <view class="function-item" bindtap="showFeedback">
      <view class="function-icon feedback-icon"></view>
      <view class="function-title">意见反馈</view>
      <view class="function-arrow">›</view>
    </view>
  </view>

  <!-- 认证提醒弹窗 -->
  <view class="auth-reminder-modal {{showAuthReminder ? 'show' : ''}}">
    <view class="auth-reminder-modal-content">
      <view class="auth-reminder-modal-icon">
        <view class="auth-reminder-modal-icon-circle">
          <text class="icon-text"></text>
        </view>
      </view>
      <view class="auth-reminder-modal-header">
        <view class="auth-reminder-modal-title">需要认证</view>
        <view class="auth-reminder-modal-subtitle">该功能需要实名认证后才能使用</view>
      </view>
      <view class="auth-reminder-modal-footer">
        <button class="auth-reminder-modal-btn cancel" bindtap="closeAuthReminder">取消</button>
        <button class="auth-reminder-modal-btn confirm" bindtap="goToAuth">立即认证</button>
      </view>
    </view>
  </view>

  <!-- <view class="logout-btn" bindtap="logout" wx:if="{{isAuthenticated}}">退出登录</view> -->
</view>