/* 添加车辆页面样式 */
page {
  background-color: #F2F2F7;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
  color: #333;
  line-height: 1.5;
  height: 100%;
  overflow: hidden;

}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  margin-bottom: 400rpx;
}



/* 内容区域 */
.content-area {
  flex: 1;
  padding: 16px 20px;
  margin-bottom: 80px;
  box-sizing: border-box;
  height: calc(100vh - 80px);
}

/* 表单样式 */
.form-group {
  background-color: white;
  border-radius: 16px;
  margin-bottom: 20px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.form-group-title {
  font-size: 13px;
  font-weight: 600;
  color: #8E8E93;
  margin: 0 0 8px 16px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 14px 16px;
  border-bottom: 1px solid rgba(60, 60, 67, 0.1);
  position: relative;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  width: 100px;
  font-size: 16px;
  color: #1c1c1e;
}

.form-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 16px;
  color: #1c1c1e;
  padding: 8px 0;
  outline: none;
}

.form-input::placeholder {
  color: #C7C7CC;
}

.required-mark {
  color: #FF3B30;
  margin-left: 4px;
}

.form-hint {
  font-size: 12px;
  color: #8E8E93;
  margin-top: 4px;
}

/* 车牌输入样式 */
.plate-item {
  display: block;
  padding-top: 0;
}

.plate-box-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 10px 0;
  margin: 0 auto;
}

/* 新能源车牌样式 */
.plate-box-container.new-energy {
  background: linear-gradient(135deg, #E8F5E8 0%, #F0FFF0 100%);
  border-radius: 8px;
  padding: 12px 8px;
}

.plate-box-container.new-energy .plate-char-box {
  background-color: #F0FFF0;
  border-color: #90EE90;
}

.plate-box-container.new-energy .plate-char-box:first-child,
.plate-box-container.new-energy .plate-char-box:nth-child(2) {
  background-color: #32CD32;
  color: white;
  border-color: #32CD32;
}

.plate-hint {
  font-size: 12px;
  color: #32CD32;
  text-align: center;
  margin-top: 8px;
  font-weight: 500;
}

.plate-char-box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 50px;
  background-color: #F2F2F7;
  border: 1px solid #D1D1D6;
  margin: 0 4px;
  font-size: 22px;
  font-weight: 600;
  color: #1c1c1e;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  transition: all 0.2s ease;
  position: relative;
}

.plate-char-box:active {
  transform: scale(0.95);
  background-color: #E5E5EA;
}

.plate-char-box:first-child {
  background-color: #007AFF;
  color: white;
  border-color: #007AFF;
}

.plate-char-box:nth-child(2) {
  background-color: #007AFF;
  color: white;
  border-color: #007AFF;
}

.plate-char-box.active {
  border: 2px solid #FF9500;
  box-shadow: 0 0 0 2px rgba(255, 149, 0, 0.2);
}

.plate-char-box.empty {
  color: #C7C7CC;
}

/* 颜色选择样式 */
.color-display {
  flex: 1;
  font-size: 16px;
  color: #1c1c1e;
  padding: 8px 0;
  display: flex;
  align-items: center;
}

.color-display.empty {
  color: #C7C7CC;
}

.color-dot {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 8px;
  vertical-align: middle;
  border: 1px solid rgba(60, 60, 67, 0.1);
}

.select-arrow {
  position: absolute;
  right: 16px;
  color: #C7C7CC;
  pointer-events: none;
}

/* 车位类型选择器样式 */
.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  color: #333;
  font-size: 16px;
  width: 220rpx;
}

.picker-display:active {
  background-color: #e9ecef;
}

.picker-display .select-arrow {
  position: static;
  color: #999;
  font-size: 18px;
  font-weight: bold;
  transform: rotate(90deg);
}

/* 上传区域样式 */
.upload-item {
  display: block;
}

.upload-container {
  margin-top: 12px;
}

.upload-area-empty {
  border: 2px dashed #E0E0E0;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  background-color: #FAFAFA;
}

.upload-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
}

.upload-placeholder {
  margin-bottom: 12px;
}

.upload-options {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0;
}

.upload-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 20px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.upload-option:active {
  transform: scale(0.95);
  opacity: 0.7;
}

.upload-option-divider {
  width: 1px;
  height: 40px;
  background-color: #E0E0E0;
  margin: 0 8px;
}

.upload-option-icon {
  width: 32px;
  height: 32px;
  margin-bottom: 8px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.album-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23007AFF'%3E%3Cpath d='M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z'/%3E%3C/svg%3E");
}

.camera-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23007AFF'%3E%3Cpath d='M12 15c1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3 1.34 3 3 3z'/%3E%3Cpath d='M9 2L7.17 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2h-3.17L15 2H9zm3 15c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5z'/%3E%3C/svg%3E");
}

.delete-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B30'%3E%3Cpath d='M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z'/%3E%3C/svg%3E");
}

.upload-option-text {
  font-size: 14px;
  color: #007AFF;
  font-weight: 500;
}

.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

.upload-area-with-image {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  background-color: #F8F8F8;
}

.uploaded-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  display: block;
}

.upload-actions {
  display: flex;
  background-color: white;
  border-top: 1px solid #E0E0E0;
}

.upload-action {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-right: 1px solid #E0E0E0;
}

.upload-action:last-child {
  border-right: none;
}

.upload-action:active {
  background-color: #F0F0F0;
  transform: scale(0.95);
}

.upload-action-icon {
  width: 20px;
  height: 20px;
  margin-bottom: 4px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.upload-action-text {
  font-size: 12px;
  color: #007AFF;
  font-weight: 500;
}

/* 提交按钮 */
.submit-button-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px 20px;
  background-color: #ffffff;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  z-index: 99;
  height: 50rpx;
  padding-bottom: calc(16px + env(safe-area-inset-bottom));
}

.submit-button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 48px;
  background-color: #007AFF;
  color: white;
  font-size: 17px;
  font-weight: 600;
  border-radius: 12px;
  border: none;
  transition: all 0.2s ease;
}

.submit-button:active {
  transform: scale(0.98);
  background-color: #0062CC;
}

.submit-button.disabled {
  background-color: rgba(0, 122, 255, 0.3);
  color: rgba(255, 255, 255, 0.8);
}

/* 车牌选择器弹窗 */
.plate-selector-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3); /* 降低背景透明度 */
  z-index: 300;
  backdrop-filter: blur(2px); /* 减少模糊程度 */
  -webkit-backdrop-filter: blur(2px);
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.plate-selector-modal.show {
  opacity: 1;
  visibility: visible;
}

.plate-selector-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-radius: 20px 20px 0 0;
  padding: 20px;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  max-height: 60vh; /* 降低高度，确保不会完全遮挡车牌输入框 */
  overflow-y: auto;
  z-index: 301;
  box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.15);
}

.plate-selector-modal.show .plate-selector-content {
  transform: translateY(0);
}

.plate-selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(60, 60, 67, 0.1);
  position: relative;
}

.plate-selector-title {
  font-size: 18px;
  font-weight: 600;
  color: #1c1c1e;
  flex: 1;
  text-align: center;
}

.confirm-button {
  color: #007AFF;
  font-size: 16px;
  font-weight: 600;
  padding: 6px 12px;
  border-radius: 6px;
  background-color: transparent;
  transition: all 0.2s ease;
}

.confirm-button:active {
  background-color: rgba(0, 122, 255, 0.1);
}

.back-button {
  display: flex;
  align-items: center;
  color: #007AFF;
  font-size: 17px;
}

.back-icon {
  margin-right: 5px;
}

.close-button {
  color: #8E8E93;
  font-size: 24px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.plate-step-indicator {
  display: flex;
  justify-content: center;
  margin-bottom: 15px;
}

.step-dot {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background-color: #D1D1D6;
  margin: 0 4px;
  transition: all 0.3s ease;
}

.step-dot.active {
  width: 20px;
  background-color: #007AFF;
}

.plate-selector-section {
  margin-bottom: 20px;
}

.plate-selector-section.hidden {
  display: none;
}

.plate-selector-section-title {
  font-size: 14px;
  font-weight: 600;
  color: #8E8E93;
  margin-bottom: 10px;
}

.current-plate-display {
  display: flex;
  justify-content: center;
  margin-bottom: 15px;
}

.plate-char-display {
  padding: 8px 16px;
  background-color: #E1F0FF;
  color: #007AFF;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
}

.plate-char-display.empty {
  background-color: #F2F2F7;
  color: #8E8E93;
}

/* 车牌预览 */
.current-plate-preview {
  display: flex;
  justify-content: center;
  margin-bottom: 15px;
  background-color: #F2F2F7;
  border-radius: 8px;
  padding: 10px;
}

.current-plate-preview.new-energy {
  background: linear-gradient(135deg, #E8F5E8 0%, #F0FFF0 100%);
}

.plate-preview-box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 36px;
  background-color: #FFFFFF;
  border: 1px solid #D1D1D6;
  margin: 0 2px;
  font-size: 16px;
  font-weight: 600;
  color: #1c1c1e;
  border-radius: 4px;
}

.plate-preview-box:first-child,
.plate-preview-box:nth-child(2) {
  background-color: #007AFF;
  color: white;
  border-color: #007AFF;
}

.current-plate-preview.new-energy .plate-preview-box {
  background-color: #F0FFF0;
  border-color: #90EE90;
}

.current-plate-preview.new-energy .plate-preview-box:first-child,
.current-plate-preview.new-energy .plate-preview-box:nth-child(2) {
  background-color: #32CD32;
  color: white;
  border-color: #32CD32;
}

.plate-preview-box.active {
  border: 2px solid #FF9500;
  box-shadow: 0 0 0 2px rgba(255, 149, 0, 0.2);
}

.plate-preview-box.empty {
  color: #C7C7CC;
}

.plate-options {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
}

.plate-option {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  background-color: #F2F2F7;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  color: #1c1c1e;
  transition: all 0.2s ease;
}

.plate-option:active {
  background-color: #D1D1D6;
  transform: scale(0.95);
}

.plate-option.selected {
  background-color: #007AFF;
  color: white;
}

.plate-option.function {
  grid-column: span 2;
  background-color: #E9E9EA;
}

.plate-confirm {
  display: block;
  width: 100%;
  padding: 14px;
  background-color: #007AFF;
  color: white;
  font-size: 17px;
  font-weight: 600;
  text-align: center;
  border-radius: 12px;
  border: none;
  transition: all 0.2s ease;
  margin-top: 20px;
}

.plate-keyboard-hint {
  text-align: center;
  color: #8E8E93;
  font-size: 14px;
  margin-top: 12px;
  margin-bottom: 8px;
}

.plate-confirm:active {
  transform: scale(0.98);
  background-color: #0062CC;
}

/* 颜色选择器弹窗 */
.color-selector-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 300;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.color-selector-modal.show {
  opacity: 1;
  visibility: visible;
}

.color-selector-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-radius: 20px 20px 0 0;
  padding: 20px;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  max-height: 80vh;
  overflow-y: auto;
  z-index: 301;
  box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.15);
}

.color-selector-modal.show .color-selector-content {
  transform: translateY(0);
}

.color-selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(60, 60, 67, 0.1);
}

.color-selector-title {
  font-size: 18px;
  font-weight: 600;
  color: #1c1c1e;
}

.color-options {
  display: flex;
  flex-direction: column;
}

.color-option {
  display: flex;
  align-items: center;
  padding: 14px 16px;
  font-size: 16px;
  color: #1c1c1e;
  border-bottom: 1px solid rgba(60, 60, 67, 0.1);
  transition: background-color 0.2s ease;
}

.color-option:last-child {
  border-bottom: none;
}

.color-option:active {
  background-color: #F2F2F7;
}

.color-option.selected {
  color: #007AFF;
}

.color-option.selected::after {
  content: "✓";
  margin-left: auto;
  color: #007AFF;
  font-weight: bold;
}


