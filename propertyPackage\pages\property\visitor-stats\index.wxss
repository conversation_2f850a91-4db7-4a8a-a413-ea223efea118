/* 访客统计页面样式 */
.visitor-stats-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 30rpx;
}

/* 标题栏样式 */
.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.stats-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.stats-actions {
  display: flex;
}

.stats-action {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
  font-size: 28rpx;
  color: #666;
}

.action-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 8rpx;
}

/* 时间范围选择器样式 */
.time-range-selector {
  display: flex;
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.range-item {
  padding: 10rpx 30rpx;
  font-size: 28rpx;
  color: #666;
  border-radius: 30rpx;
  margin-right: 20rpx;
}

.range-item.active {
  background-color: #ff8c00;
  color: #fff;
}

/* 标签页样式 */
.tab-header {
  display: flex;
  background-color: #fff;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #eee;
  position: sticky;
  top: 0;
  z-index: 10;
}

.tab-item {
  flex: 1;
  display: flex;
  justify-content: center;
  padding: 20rpx 0;
  position: relative;
  color: #666;
  font-size: 28rpx;
}

.tab-item.active {
  color: #ff8c00;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background-color: #ff8c00;
  border-radius: 3rpx;
}

/* 加载中样式 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading text {
  font-size: 28rpx;
  color: #999;
}

/* 标签页内容样式 */
.tab-content {
  padding: 30rpx;
}

/* 统计卡片样式 */
.stats-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.card-subtitle {
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}

.card-body {
  padding: 30rpx;
}

/* 摘要信息样式 */
.summary-info {
  display: flex;
  margin-bottom: 30rpx;
}

.summary-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-value {
  font-size: 48rpx;
  font-weight: 600;
  color: #ff8c00;
  margin-bottom: 10rpx;
}

.summary-label {
  font-size: 24rpx;
  color: #999;
}

/* 图表容器样式 */
.chart-container {
  width: 100%;
  height: 500rpx;
  position: relative;
}

/* 模拟图表样式 */
.mock-chart {
  width: 100%;
  height: 100%;
  display: flex;
}

/* 趋势图表样式 */
.trend-chart {
  display: flex;
}

.chart-y-axis {
  width: 60rpx;
  height: 100%;
  display: flex;
  flex-direction: column-reverse;
  justify-content: space-between;
  padding-right: 10rpx;
}

.chart-y-axis text {
  font-size: 20rpx;
  color: #999;
  text-align: right;
}

.chart-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

.chart-bars {
  flex: 1;
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  padding-bottom: 40rpx;
}

.chart-bar {
  width: 20rpx;
  background-color: #ff8c00;
  border-radius: 10rpx 10rpx 0 0;
  margin: 0 5rpx;
}

.chart-x-axis {
  height: 40rpx;
  display: flex;
  justify-content: space-around;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

.x-label {
  font-size: 20rpx;
  color: #999;
  text-align: center;
}

/* 饼图样式 */
.pie-chart {
  position: relative;
  width: 300rpx;
  height: 300rpx;
  margin: 0 auto;
}

.pie-segments {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 50%;
  overflow: hidden;
}

.pie-segment {
  position: absolute;
  width: 50%;
  height: 50%;
  transform-origin: 100% 100%;
  left: 0;
  top: 0;
}

.pie-segment::before {
  content: '';
  position: absolute;
  width: 200%;
  height: 200%;
  border-radius: 50%;
  top: 0;
  left: 0;
}

.pie-center {
  position: absolute;
  width: 60%;
  height: 60%;
  background-color: #fff;
  border-radius: 50%;
  top: 20%;
  left: 20%;
}

/* 甜甜圈图样式 */
.donut-chart {
  position: relative;
  width: 300rpx;
  height: 300rpx;
  margin: 0 auto;
}

.donut-segments {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 50%;
  overflow: hidden;
}

.donut-segment {
  position: absolute;
  width: 50%;
  height: 50%;
  transform-origin: 100% 100%;
  left: 0;
  top: 0;
}

.donut-center {
  position: absolute;
  width: 60%;
  height: 60%;
  background-color: #fff;
  border-radius: 50%;
  top: 20%;
  left: 20%;
}

/* 图例样式 */
.chart-legend {
  display: flex;
  flex-wrap: wrap;
  margin-top: 30rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  margin-bottom: 10rpx;
  width: 45%;
}

.legend-color {
  width: 20rpx;
  height: 20rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
}

.legend-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 10rpx;
}

.legend-value {
  font-size: 24rpx;
  color: #999;
}

/* 热力图样式 */
.heatmap-chart {
  display: flex;
}

.heatmap-y-axis {
  width: 60rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-right: 10rpx;
}

.heatmap-y-axis text {
  font-size: 20rpx;
  color: #999;
  text-align: right;
  height: 20rpx;
  line-height: 20rpx;
}

.heatmap-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

.heatmap-grid {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 40rpx;
}

.heatmap-cell {
  width: calc(100% / 7);
  height: calc(100% / 24);
  box-sizing: border-box;
  border: 1rpx solid #fff;
}

.heatmap-x-axis {
  height: 40rpx;
  display: flex;
  justify-content: space-around;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

.heatmap-x-axis text {
  font-size: 20rpx;
  color: #999;
  text-align: center;
}

/* 来访目的图表样式 */
.purpose-chart {
  width: 100%;
}

.purpose-item {
  margin-bottom: 20rpx;
}

.purpose-bar-container {
  display: flex;
  align-items: center;
}

.purpose-label {
  width: 150rpx;
  font-size: 24rpx;
  color: #666;
  text-align: right;
  padding-right: 20rpx;
}

.purpose-bar-wrapper {
  flex: 1;
  position: relative;
  height: 40rpx;
  background-color: #f5f5f5;
  border-radius: 20rpx;
  overflow: hidden;
}

.purpose-bar {
  height: 100%;
  background-color: #4f46e5;
  border-radius: 20rpx;
}

.purpose-value {
  position: absolute;
  right: 20rpx;
  top: 0;
  height: 40rpx;
  line-height: 40rpx;
  font-size: 24rpx;
  color: #fff;
}

/* 折线图样式 */
.line-chart {
  display: flex;
}

.chart-line {
  position: absolute;
  top: 0;
  left: 60rpx;
  right: 0;
  bottom: 40rpx;
  border-bottom: 1rpx solid #eee;
  border-left: 1rpx solid #eee;
}

.line-point {
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  background-color: #ff8c00;
  border-radius: 50%;
  transform: translate(-50%, 50%);
}

.line-point::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 6rpx;
  height: 6rpx;
  background-color: #fff;
  border-radius: 50%;
  transform: translate(-50%, -50%);
}
