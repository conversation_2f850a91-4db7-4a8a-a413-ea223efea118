/* 工单列表页面样式 */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 120rpx; /* 为批量操作工具栏留出空间 */
}

/* 搜索栏样式 */
.search-bar {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 10;
}

.search-input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  height: 72rpx;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 24rpx;
  position: relative;
}

.search-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.search-input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
  color: #333;
}

.clear-icon {
  width: 36rpx;
  height: 36rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='15' y1='9' x2='9' y2='15'%3E%3C/line%3E%3Cline x1='9' y1='9' x2='15' y2='15'%3E%3C/line%3E%3C/svg%3E");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.filter-button {
  display: none; /* 隐藏原有的筛选按钮 */
}

.filter-button-inline {
  display: flex;
  align-items: center;
  padding: 0 16rpx;
  height: 100%;
  border-left: 1rpx solid #e0e0e0;
  margin-left: 16rpx;
}

.filter-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='4' y1='21' x2='4' y2='14'%3E%3C/line%3E%3Cline x1='4' y1='10' x2='4' y2='3'%3E%3C/line%3E%3Cline x1='12' y1='21' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='8' x2='12' y2='3'%3E%3C/line%3E%3Cline x1='20' y1='21' x2='20' y2='16'%3E%3C/line%3E%3Cline x1='20' y1='12' x2='20' y2='3'%3E%3C/line%3E%3Cline x1='1' y1='14' x2='7' y2='14'%3E%3C/line%3E%3Cline x1='9' y1='8' x2='15' y2='8'%3E%3C/line%3E%3Cline x1='17' y1='16' x2='23' y2='16'%3E%3C/line%3E%3C/svg%3E");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.filter-button-inline text {
  font-size: 28rpx;
  color: #666;
}

/* 状态标签栏样式 */
.status-tabs {
  display: flex;
  overflow-x: auto;
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  box-sizing: border-box;
  width: 100%;
  /* 隐藏滚动条但保留滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.status-tabs::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.status-tab {
  display: inline-flex; /* 使用inline-flex确保宽度自适应内容 */
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  padding: 0 24rpx; /* 水平内边距 */
  margin-right: 16rpx;
  font-size: 28rpx;
  color: #333; /* 提高对比度 */
  background-color: #f5f5f5;
  border-radius: 100rpx; /* 完全圆角，药丸形状 */
  transition: all 0.3s;
  flex-shrink: 0; /* 防止标签被压缩 */
  height: 56rpx; /* 统一高度 */
  min-width: 56rpx; /* 最小宽度等于高度，确保短文本也是药丸形 */
  box-sizing: border-box;
  white-space: nowrap; /* 防止文字换行 */
}

.status-tab.active {
  color: #fff;
  background-color: #ff8c00;
  font-weight: 500; /* 选中时加粗 */
  /* 保持与未选中状态相同的形状和尺寸 */
  height: 56rpx;
  min-width: 56rpx;
  border-radius: 100rpx;
  padding: 0 24rpx;
}

/* 添加触摸反馈 */
.status-tab-hover {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 工具栏样式 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-count {
  font-size: 24rpx;
  color: #999;
}

.toolbar-actions {
  display: flex;
}

.action-button {
  padding: 8rpx 20rpx;
  font-size: 24rpx;
  color: #ff8c00;
  border: 1rpx solid #ff8c00;
  border-radius: 100rpx;
  margin-left: 16rpx;
  position: relative;
  display: flex;
  align-items: center;
  transition: all 0.3s;
}

.action-button.active {
  background-color: #ff8c00;
  color: #fff;
  font-weight: 500;
}

.action-button.active text {
  color: #fff;
}

.selected-count {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36rpx;
  height: 36rpx;
  background-color: #fff;
  color: #ff8c00;
  border-radius: 18rpx;
  font-size: 20rpx;
  margin-left: 8rpx;
  padding: 0 8rpx;
  font-weight: bold;
}

/* 加载中样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-spinner.small {
  width: 32rpx;
  height: 32rpx;
  border-width: 2rpx;
  margin-bottom: 8rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.loading-more {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 0;
}

.no-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 24rpx;
  color: #999;
}

/* 工单列表样式 */
.order-list {
  padding: 0 30rpx;
  height: calc(100vh - 360rpx); /* 减去顶部搜索栏、状态标签栏和工具栏的高度 */
  overflow: hidden;
  position: relative;
}

/* 滚动视图样式 */
.order-scroll-view {
  height: 100%;
  width: 100%;
}

.order-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.order-item.selected {
  background-color: #fff9f0;
}

.select-box {
  margin-right: 20rpx;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  position: relative;
}

.checkbox.checked {
  background-color: #ff8c00;
  border-color: #ff8c00;
}

.checkbox.checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20rpx;
  height: 20rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='white' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.order-type-tag {
  min-width: 80rpx;
  height: 48rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  font-size: 24rpx;
  color: #fff;
  padding: 0 12rpx;
}

/* 不同类型工单的标签颜色 */
.order-type-tag.repair {
  background-color: #ff8c00; /* 维修工单 - 橙色 */
}

.order-type-tag.complaint {
  background-color: #f44336; /* 投诉工单 - 红色 */
}

.order-type-tag.suggestion {
  background-color: #2196f3; /* 建议工单 - 蓝色 */
}

.order-type-tag.other {
  background-color: #9e9e9e; /* 其他工单 - 灰色 */
}

.order-info {
  flex: 1;
  margin-right: 20rpx;
}

.order-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.order-address {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.order-meta {
  font-size: 24rpx;
  color: #999;
}

.order-id {
  margin-right: 16rpx;
}

.order-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.priority-badge {
  display: flex;
  align-items: center;
  background-color: #fff3e0;
  border-radius: 16rpx;
  padding: 4rpx 12rpx;
  margin-bottom: 8rpx;
}

.priority-indicator {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

.priority-text {
  font-size: 20rpx;
  color: #ff8c00;
  font-weight: 500;
}

.priority-1 {
  background-color: #4caf50;
}

.priority-2 {
  background-color: #2196f3;
}

.priority-3 {
  background-color: #ff9800;
}

.priority-4 {
  background-color: #f44336;
}

.new-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  background-color: #f44336;
  color: #fff;
  border-radius: 50%;
  font-size: 20rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  position: relative;
}

/* 添加小红点动画效果 */
.new-badge::after {
  content: '';
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  width: 16rpx;
  height: 16rpx;
  background-color: #f44336;
  border-radius: 50%;
  border: 2rpx solid #fff;
}

.status-text {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 100rpx;
  background-color: #f5f5f5;
  color: #666;
}

/* 工单状态样式 */
.status-pending .status-text {
  background-color: #fff3e0;
  color: #ff9800;
}

.status-processing .status-text {
  background-color: #e3f2fd;
  color: #2196f3;
}

.status-completed .status-text {
  background-color: #e8f5e9;
  color: #4caf50;
}

.status-cancelled .status-text {
  background-color: #f5f5f5;
  color: #9e9e9e;
}

/* 空列表样式 */
.empty-list,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.empty-icon-default {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23cccccc' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z'%3E%3C/path%3E%3Cpolyline points='13 2 13 9 20 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23cccccc' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z'%3E%3C/path%3E%3Cpolyline points='13 2 13 9 20 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 筛选面板样式 */
.filter-panel-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 100;
}

.filter-panel {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 80%;
  background-color: #fff;
  z-index: 101;
  transform: translateX(100%);
  transition: transform 0.3s;
  padding: 30rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  box-shadow: -4rpx 0 10rpx rgba(0, 0, 0, 0.1);
}

.filter-panel.show {
  transform: translateX(0);
}

.filter-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 30rpx;
  position: relative;
}

.filter-panel-header text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-icon {
  min-width: 120rpx;
  height: 60rpx;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 0 20rpx;
}

/* 添加触摸反馈 */
.close-icon:active,
.close-icon-hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.close-text {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  display: block;
}

.filter-section {
  margin-bottom: 30rpx;
}

.filter-section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
}

.filter-option {
  padding: 12rpx 24rpx;
  margin: 0 20rpx 20rpx 0;
  font-size: 28rpx;
  color: #666;
  background-color: #f5f5f5;
  border-radius: 100rpx;
}

.filter-option.active {
  color: #fff;
  background-color: #ff8c00;
}

/* 批量操作工具栏样式 */
.batch-toolbar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  z-index: 99;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding-bottom: env(safe-area-inset-bottom, 0);
}

.batch-toolbar.show {
  transform: translateY(0);
}

/* 顶部区域：全选和已选数量 */
.batch-toolbar-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.batch-select {
  display: flex;
  align-items: center;
}

.batch-select text {
  font-size: 28rpx;
  color: #333;
  margin-left: 16rpx;
}

.selected-count-display {
  font-size: 26rpx;
  color: #666;
  margin-left: 20rpx;
}

.selected-count-display.empty {
  color: #999;
}

.count-number {
  color: #ff8c00;
  font-weight: bold;
  font-size: 28rpx;
}

/* 底部区域：批量操作按钮 */
.batch-actions-container {
  padding: 16rpx 30rpx;
}

.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.batch-action {
  flex: 1;
  padding: 16rpx 0;
  margin: 0 8rpx;
  font-size: 28rpx;
  color: #fff;
  background-color: #ff8c00;
  border-radius: 100rpx;
  transition: all 0.3s;
  text-align: center;
}

.batch-action:first-child {
  margin-left: 0;
}

.batch-action:last-child {
  margin-right: 0;
}

.batch-action.danger {
  background-color: #f44336;
}

.batch-action.disabled {
  background-color: #cccccc;
  color: #ffffff;
  opacity: 0.6;
  pointer-events: none;
}

/* 暗黑模式样式 */
.darkMode {
  background-color: #1a1a1a;
}

.darkMode .search-bar,
.darkMode .status-tabs,
.darkMode .toolbar,
.darkMode .filter-panel,
.darkMode .batch-toolbar,
.darkMode .batch-toolbar-top,
.darkMode .batch-actions-container {
  background-color: #2a2a2a;
  border-color: #333;
}

.darkMode .search-input-wrapper,
.darkMode .filter-button,
.darkMode .status-tab,
.darkMode .order-icon-container,
.darkMode .filter-option {
  background-color: #333;
}

.darkMode .search-input,
.darkMode .filter-panel-header text,
.darkMode .filter-section-title,
.darkMode .batch-select text,
.darkMode .order-title {
  color: #fff;
}

.darkMode .filter-button text,
.darkMode .status-tab,
.darkMode .filter-option,
.darkMode .order-address {
  color: #ccc;
}

.darkMode .order-item {
  border-color: #333;
}

.darkMode .order-item.selected {
  background-color: #3a3a3a;
}

.darkMode .checkbox {
  border-color: #666;
}

.darkMode .order-meta,
.darkMode .loading-text,
.darkMode .no-more,
.darkMode .empty-text,
.darkMode .order-count {
  color: #888;
}
