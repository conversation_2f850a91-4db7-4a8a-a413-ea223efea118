// complete-info.js
const util = require('@/utils/util.js')
const commApi = require('@/api/commApi.js')
const userApi = require('@/api/userApi.js')

Page({
  data: {
    // 基本信息（从userInfo获取）
    name: '',
    phone: '',

    // 可编辑信息
    realName: '',
    avatarUrl: '/images/default-avatar.svg',
    avatarServerPath: '', // 服务器返回的头像路径（不带apiUrl）
    gender: '',
    genderIndex: -1,
    birthday: '',
    certificateType: '',
    certificateTypeIndex: -1,
    idCardNumber: '',

    // 选项数据（从字典获取）
    genderOptions: [],
    certificateTypes: [],

    // 表单验证状态
    realNameValid: false,
    realNameError: false,
    birthdayValid: false,
    birthdayError: false,
    certificateNumberValid: false,
    certificateNumberError: false,

    // 提交状态
    submitting: false
  },

  onLoad: function (options) {
    this.loadDictionaries()
    this.loadUserInfo()
  },

  // 加载字典数据
  loadDictionaries: function () {
    try {
      // 获取性别字典
      const genderDict = util.getDictByNameEn('gender')
      if (genderDict && genderDict[0] && genderDict[0].children) {
        this.setData({
          genderOptions: genderDict[0].children
        })
      } else {
        console.warn('性别字典数据为空')
        this.setData({
          genderOptions: []
        })
      }

      // 获取证件类型字典
      const certificateDict = util.getDictByNameEn('certificate_type')
      if (certificateDict && certificateDict[0] && certificateDict[0].children) {
        this.setData({
          certificateTypes: certificateDict[0].children
        })
      } else {
        console.warn('证件类型字典数据为空')
        this.setData({
          certificateTypes: []
        })
      }
    } catch (error) {
      console.error('加载字典数据失败:', error)
      this.setData({
        genderOptions: [],
        certificateTypes: []
      })
    }
  },

  // 加载用户信息
  loadUserInfo: function () {
    const userInfo = wx.getStorageSync('userInfo') || {}

    // 处理头像显示路径
    let displayAvatarUrl = '/images/default-avatar.svg'
    if (userInfo.avatarUrl) {
      // 如果有头像路径，显示时需要加上apiUrl前缀
      const apiUrl = wx.getStorageSync('apiUrl')
      displayAvatarUrl = apiUrl + '/common-api/v1/file/' + userInfo.avatarUrl
    }

    this.setData({
      name: userInfo.realName || '',
      phone: userInfo.phone || '',
      realName: userInfo.realName || '',
      avatarUrl: displayAvatarUrl,
      avatarServerPath: userInfo.avatarUrl || '', // 保存服务器路径（不带apiUrl）
      gender: userInfo.gender || '',
      birthday: userInfo.birthday ? this.formatDate(userInfo.birthday) : '',
      certificateType: userInfo.certificateType || '',
      idCardNumber: userInfo.idCardNumber || ''
    })

    // 设置性别选择器索引
    if (userInfo.gender && this.data.genderOptions.length > 0) {
      const genderIndex = this.data.genderOptions.findIndex(item => item.nameEn === userInfo.gender)
      this.setData({ genderIndex: genderIndex >= 0 ? genderIndex : -1 })
    }

    // 设置证件类型选择器索引
    if (userInfo.certificateType && this.data.certificateTypes.length > 0) {
      const certificateTypeIndex = this.data.certificateTypes.findIndex(item => item.nameEn === userInfo.certificateType)
      this.setData({ certificateTypeIndex: certificateTypeIndex >= 0 ? certificateTypeIndex : -1 })
    }
  },

  // 格式化日期
  formatDate: function (dateString) {
    if (!dateString) return ''
    const date = new Date(dateString)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  // 用户名输入
  onrealNameInput: function (e) {
    const realName = e.detail.value
    this.setData({ realName })
    this.validaterealName()
  },

  // 验证用户名
  validaterealName: function () {
    const realName = this.data.realName.trim()
    const isValid = realName.length >= 2 && realName.length <= 20
    this.setData({
      realNameValid: isValid,
      realNameError: realName.length > 0 && !isValid
    })
    return isValid
  },

  // 选择头像
  chooseAvatar: function () {
    wx.showActionSheet({
      itemList: ['从相册选择', '拍照'],
      success: (res) => {
        const sourceType = res.tapIndex === 0 ? ['album'] : ['camera']
        
        wx.chooseImage({
          count: 1,
          sizeType: ['compressed'],
          sourceType: sourceType,
          success: (res) => {
            this.setData({
              avatarUrl: res.tempFilePaths[0]
            })
            
            // 这里可以上传到服务器
            this.uploadAvatar(res.tempFilePaths[0])
          }
        })
      }
    })
  },

  // 上传头像
  uploadAvatar: function (filePath) {
    wx.showLoading({
      title: '上传中...',
      mask: true
    })

    commApi.upLoadFile(filePath)
      .then((result) => {
        wx.hideLoading()
        console.log('头像上传成功：', result)

        if (result.code === 0 && result.data) {
          wx.showToast({
            title: '头像上传成功',
            icon: 'success'
          })

          // 保存服务器返回的路径（不带apiUrl前缀）
          const serverPath = result.data
          this.setData({
            avatarServerPath: serverPath
          })

          // 更新显示的头像URL（需要加上apiUrl前缀）
          const apiUrl = wx.getStorageSync('apiUrl')
          this.setData({
            avatarUrl: apiUrl + '/common-api/v1/file/' + serverPath
          })
        } else {
          wx.showToast({
            title: result.errorMessage || '头像上传失败',
            icon: 'none'
          })
        }
      })
      .catch((error) => {
        wx.hideLoading()
        console.error('头像上传失败:', error)
        wx.showToast({
          title: '头像上传失败',
          icon: 'none'
        })
      })
  },

  // 性别选择
  onGenderChange: function (e) {
    const index = e.detail.value
    const selectedGender = this.data.genderOptions[index]
    this.setData({
      genderIndex: index,
      gender: selectedGender ? selectedGender.nameEn : ''
    })
  },

  // 生日选择
  onBirthdayChange: function (e) {
    this.setData({
      birthday: e.detail.value
    })
    this.validateBirthday()
  },

  // 验证生日
  validateBirthday: function () {
    const birthday = this.data.birthday
    if (!birthday) {
      this.setData({
        birthdayValid: false,
        birthdayError: false
      })
      return false
    }

    const birthDate = new Date(birthday)
    const today = new Date()
    const age = today.getFullYear() - birthDate.getFullYear()
    
    const isValid = age >= 0 && age <= 150
    this.setData({
      birthdayValid: isValid,
      birthdayError: !isValid
    })
    return isValid
  },

  // 证件类型选择
  onCertificateTypeChange: function (e) {
    const index = e.detail.value
    const selectedType = this.data.certificateTypes[index]
    this.setData({
      certificateTypeIndex: index,
      certificateType: selectedType ? selectedType.nameEn : ''
    })
  },

  // 证件号码输入
  onIdCardNumberInput: function (e) {
    const idCardNumber = e.detail.value
    this.setData({ idCardNumber })
    this.validateCertificateNumber()
  },

  // 验证证件号码
  validateCertificateNumber: function () {
    const idCardNumber = this.data.idCardNumber.trim()
    const certificateType = this.data.certificateType

    let isValid = false

    if (certificateType === 'id_card') {
      isValid = /^\d{17}[\dXx]$/.test(idCardNumber)
    } else if (certificateType === 'passport') {
      isValid = /^[A-Za-z0-9]{6,20}$/.test(idCardNumber)
    } else {
      isValid = idCardNumber.length >= 6
    }

    this.setData({
      certificateNumberValid: isValid,
      certificateNumberError: idCardNumber.length > 0 && !isValid
    })
    return isValid
  },

  // 保存信息
  saveInfo: function () {
    // 验证表单
    let isValid = true

    if (this.data.realName && !this.validaterealName()) {
      isValid = false
    }

    if (this.data.birthday && !this.validateBirthday()) {
      isValid = false
    }

    if (this.data.idCardNumber && !this.validateCertificateNumber()) {
      isValid = false
    }

    if (!isValid) {
      wx.showToast({
        title: '请检查输入信息',
        icon: 'none'
      })
      return
    }

    this.setData({ submitting: true })

    // 准备提交的数据
    const submitData = {
      realName: this.data.realName,
      avatarUrl: this.data.avatarServerPath, // 提交时使用服务器路径（不带apiUrl）
      gender: this.data.gender,
      birthday: this.data.birthday,
      certificateType: this.data.certificateType,
      idCardNumber: this.data.idCardNumber
    }

    console.log('提交完善信息数据：', submitData)

    // 调用完善信息接口
    userApi.supplementUserInfo(submitData)
      .then(res => {
        console.log('完善信息成功：', res)

        if (res.code === 0) {
          // 更新本地存储的用户信息
          const userInfo = wx.getStorageSync('userInfo') || {}
          const updatedUserInfo = {
            ...userInfo,
            realName: this.data.realName,
            avatarUrl: this.data.avatarServerPath, // 本地存储也保存服务器路径
            gender: this.data.gender,
            birthday: this.data.birthday,
            certificateType: this.data.certificateType,
            idCardNumber: this.data.idCardNumber
          }

          wx.setStorageSync('userInfo', updatedUserInfo)

          wx.showToast({
            title: '保存成功',
            icon: 'success'
          })

          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        } else {
          wx.showToast({
            title: res.errorMessage || '保存失败',
            icon: 'none'
          })
        }
      })
      .catch(err => {
        console.error('完善信息异常：', err)
        wx.showToast({
          title: '网络异常，请重试',
          icon: 'none'
        })
      })
      .finally(() => {
        this.setData({ submitting: false })
      })
  }
})
