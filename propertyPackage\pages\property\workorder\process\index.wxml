<!-- pages/property/workorder/process/index.wxml -->
<view class="container {{darkMode ? 'darkMode' : ''}}">
  <!-- 顶部导航栏 -->
  <view class="nav-bar" style="padding-top: {{statusBarHeight}}px;">
    <view class="nav-back" bindtap="navigateBack">
      <image src="/images/icons/back.svg" class="nav-icon" />
    </view>
    <view class="nav-title">
      <block wx:if="{{processType === 'process'}}">接单处理</block>
      <block wx:elif="{{processType === 'assign'}}">分配工单</block>
      <block wx:elif="{{processType === 'complete'}}">完成工单</block>
      <block wx:elif="{{processType === 'record'}}">添加记录</block>
    </view>
    <view class="nav-action"></view>
  </view>

  <!-- 加载中提示 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view class="content" wx:else>
    <!-- 工单基本信息 -->
    <view class="order-info-card">
      <view class="order-title">{{workOrder.content.title}}</view>
      <view class="order-meta">
        <view class="order-id">工单号：{{workOrder.id}}</view>
        <view class="order-time">创建时间：{{workOrder.createTime}}</view>
        <view class="order-status">
          <text class="status-text status-{{workOrder.status}}">{{workOrder.statusName}}</text>
        </view>
      </view>
    </view>

    <!-- 处理表单 -->
    <view class="process-form">
      <!-- 接单处理表单 -->
      <block wx:if="{{processType === 'process'}}">
        <view class="form-group">
          <view class="form-label">处理备注 <text class="required">*</text></view>
          <view class="textarea-with-template {{errors.processRemark ? 'has-error' : ''}}">
            <textarea
              class="form-textarea"
              placeholder="请输入处理备注"
              value="{{processRemark}}"
              bindinput="inputProcessRemark"
            ></textarea>
            <view class="template-button" bindtap="showTemplateSelector">
              <image src="/images/icons/template.svg" class="template-icon" />
            </view>
          </view>
          <view class="error-message" wx:if="{{errors.processRemark}}">{{errors.processRemark}}</view>
          <view class="form-tips">请填写接单后的处理计划或备注信息</view>
        </view>
      </block>

      <!-- 分配工单表单 -->
      <block wx:elif="{{processType === 'assign'}}">
        <view class="form-group">
          <view class="form-label">选择员工 <text class="required">*</text></view>
          <view class="staff-list {{errors.selectedStaffId ? 'has-error' : ''}}">
            <view
              wx:for="{{staffList}}"
              wx:key="id"
              class="staff-item {{selectedStaffId === item.id ? 'selected' : ''}}"
              bindtap="selectStaff"
              data-id="{{item.id}}"
            >
              <view class="staff-avatar">
                <image src="/images/icons/user.svg" class="avatar-icon" />
              </view>
              <view class="staff-info">
                <view class="staff-name">{{item.name}}</view>
                <view class="staff-position">{{item.department}} - {{item.position}}</view>
              </view>
              <view class="staff-check" wx:if="{{selectedStaffId === item.id}}">
                <image src="/images/icons/check.svg" class="check-icon" />
              </view>
            </view>
          </view>
          <view class="error-message" wx:if="{{errors.selectedStaffId}}">{{errors.selectedStaffId}}</view>
        </view>

        <view class="form-group">
          <view class="form-label">分配备注</view>
          <view class="textarea-with-template {{errors.assignRemark ? 'has-error' : ''}}">
            <textarea
              class="form-textarea"
              placeholder="请输入分配备注"
              value="{{assignRemark}}"
              bindinput="inputAssignRemark"
            ></textarea>
            <view class="template-button" bindtap="showTemplateSelector">
              <image src="/images/icons/template.svg" class="template-icon" />
            </view>
          </view>
          <view class="error-message" wx:if="{{errors.assignRemark}}">{{errors.assignRemark}}</view>
          <view class="form-tips">请填写分配工单的原因或注意事项</view>
        </view>
      </block>

      <!-- 完成工单表单 -->
      <block wx:elif="{{processType === 'complete'}}">
        <view class="form-group">
          <view class="form-label">处理结果 <text class="required">*</text></view>
          <view class="textarea-with-template {{errors.completeResult ? 'has-error' : ''}}">
            <textarea
              class="form-textarea"
              placeholder="请输入处理结果"
              value="{{completeResult}}"
              bindinput="inputCompleteResult"
            ></textarea>
            <view class="template-button" bindtap="showTemplateSelector">
              <image src="/images/icons/template.svg" class="template-icon" />
            </view>
          </view>
          <view class="error-message" wx:if="{{errors.completeResult}}">{{errors.completeResult}}</view>
          <view class="form-tips">请详细描述工单的处理过程和结果</view>
        </view>

        <view class="form-group">
          <view class="form-label">上传图片</view>
          <view class="upload-images">
            <view class="image-list">
              <view
                wx:for="{{completeImages}}"
                wx:key="index"
                class="upload-image-item"
              >
                <image src="{{item}}" class="upload-image" mode="aspectFill" />
                <view class="remove-image" bindtap="removeCompleteImage" data-index="{{index}}">×</view>
              </view>
              <view class="upload-button" bindtap="uploadCompleteImages" wx:if="{{completeImages.length < 9}}">
                <view class="upload-icon">+</view>
                <view class="upload-text">上传图片</view>
              </view>
            </view>
          </view>
          <view class="form-tips">最多上传9张图片，建议上传处理前后的对比图</view>
        </view>

        <view class="form-group">
          <view class="form-label">完成备注</view>
          <textarea
            class="form-textarea {{errors.completeRemark ? 'has-error' : ''}}"
            placeholder="请输入完成备注"
            value="{{completeRemark}}"
            bindinput="inputCompleteRemark"
          ></textarea>
          <view class="error-message" wx:if="{{errors.completeRemark}}">{{errors.completeRemark}}</view>
          <view class="form-tips">请填写完成工单的补充说明或注意事项</view>
        </view>
      </block>

      <!-- 添加记录表单 -->
      <block wx:elif="{{processType === 'record'}}">
        <view class="form-group">
          <view class="form-label">处理动作 <text class="required">*</text></view>
          <view class="input-with-template {{errors.recordAction ? 'has-error' : ''}}">
            <input
              class="form-input"
              placeholder="请输入处理动作"
              value="{{recordAction}}"
              bindinput="inputRecordAction"
            />
            <view class="template-button" bindtap="showTemplateSelector">
              <image src="/images/icons/template.svg" class="template-icon" />
            </view>
          </view>
          <view class="error-message" wx:if="{{errors.recordAction}}">{{errors.recordAction}}</view>
          <view class="form-tips">例如：现场勘查、购买材料、联系业主等</view>
        </view>

        <view class="form-group">
          <view class="form-label">处理备注 <text class="required">*</text></view>
          <textarea
            class="form-textarea {{errors.recordRemark ? 'has-error' : ''}}"
            placeholder="请输入处理备注"
            value="{{recordRemark}}"
            bindinput="inputRecordRemark"
          ></textarea>
          <view class="error-message" wx:if="{{errors.recordRemark}}">{{errors.recordRemark}}</view>
          <view class="form-tips">请详细描述处理动作的具体内容</view>
        </view>
      </block>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-buttons">
      <button class="btn-cancel" bindtap="cancelProcess" disabled="{{submitting}}">取消</button>
      <button class="btn-submit" bindtap="submitProcess" loading="{{submitting}}" disabled="{{submitting}}">提交</button>
    </view>
  </view>

  <!-- 模板选择器 -->
  <view class="template-selector-mask" wx:if="{{showTemplateSelector}}" bindtap="hideTemplateSelector"></view>
  <view class="template-selector {{showTemplateSelector ? 'show' : ''}}">
    <view class="template-selector-header">
      <text class="template-selector-title">选择模板</text>
      <view class="template-selector-close" bindtap="hideTemplateSelector">×</view>
    </view>

    <view class="template-selector-content">
      <view wx:if="{{templates.length === 0}}" class="empty-templates">
        <text>暂无模板</text>
      </view>

      <view
        wx:for="{{templates}}"
        wx:key="id"
        class="template-item"
        bindtap="selectTemplate"
        data-index="{{index}}"
      >
        <view class="template-content">
          <view class="template-text">{{item.content}}</view>
          <view class="template-remark" wx:if="{{item.remark}}">{{item.remark}}</view>
        </view>
      </view>
    </view>

    <view class="template-selector-footer">
      <button class="template-selector-btn" bindtap="navigateToTemplates">管理模板</button>
    </view>
  </view>
</view>
