<!--待核销订单列表页-->
<view class="container {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}" wx:if="{{goods}}">
  <!-- 商品信息 -->
  <view class="goods-section">
    <view class="goods-card">
      <image class="goods-image" src="{{goods.image}}" mode="aspectFill"></image>
      <view class="goods-info">
        <view class="goods-title">{{goods.title}}</view>
        <view class="goods-price">¥{{goods.price}}</view>
        <view class="goods-stats">
          <view class="goods-stat">库存: {{goods.stock}}</view>
          <view class="goods-stat">已售: {{goods.sold}}</view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 待核销订单列表 -->
  <view class="orders-section">
    <view class="section-header">
      <view class="section-title">待核销订单 ({{pendingOrders.length}})</view>
      <view class="scan-btn" bindtap="navigateToScan">扫码核销</view>
    </view>
    
    <view class="order-list" wx:if="{{pendingOrders.length > 0}}">
      <view class="order-item" wx:for="{{pendingOrders}}" wx:key="id" bindtap="navigateToOrderDetail" data-id="{{item.id}}">
        <view class="order-header">
          <view class="order-id">订单号: {{item.orderSn}}</view>
          <view class="order-time">{{item.createTime}}</view>
        </view>
        <view class="order-content">
          <image class="buyer-avatar" src="{{item.buyerAvatar}}" mode="aspectFill"></image>
          <view class="order-info">
            <view class="buyer-name">{{item.buyerName}}</view>
            <view class="order-quantity">数量: {{item.quantity}}</view>
            <view class="order-amount">金额: ¥{{item.totalAmount}}</view>
          </view>
          <view class="order-actions">
            <view class="action-btn contact" catchtap="contactBuyer" data-id="{{item.id}}" data-name="{{item.buyerName}}">联系</view>
          </view>
        </view>
      </view>
    </view>
    
    <view class="no-orders" wx:else>
      <icon class="no-orders-icon" type="info" size="40" color="#ccc"></icon>
      <view class="no-orders-text">暂无待核销订单</view>
    </view>
  </view>
  
  <!-- 底部扫码按钮 -->
  <view class="footer">
    <view class="footer-btn scan" bindtap="navigateToScan">
      <view class="scan-icon"></view>
      <view class="scan-text">扫码核销</view>
    </view>
  </view>
</view>

<!-- 加载中 -->
<view class="loading-container" wx:else>
  <view class="loading-icon"></view>
  <view class="loading-text">加载中...</view>
</view>
