// pages/property/statistics/statistics.js
const dateUtil = require('../../../../utils/dateUtil.js')
const statisticsManager = require('../../../../utils/statistics-manager.js')
const wxCharts = require('../../../../utils/wxcharts/wxcharts.js')

Page({
  data: {
    darkMode: false,
    isLoading: true,
    activeTab: 'dashboard', // 当前活动标签页：dashboard, category

    // 页面状态
    pageState: 'loading', // loading, success, error
    errorMsg: '',

    // 仪表盘数据
    dashboardData: {},
    currentDate: '',

    // 分类数据
    currentCategory: 'resident', // 当前选中的分类
    timeRange: 'week', // 时间范围：today, week, month
    categoryData: {
      resident: {},
      house: {},
      parking: {},
      facility: {},
      workorder: {},
      visitor: {}
    }
  },

  onLoad: function() {
    // 检查暗黑模式
    const app = getApp()
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      })
    }

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '数据统计'
    });

    // 加载仪表盘数据
    this.loadDashboardData();
  },

  // 页面显示时触发
  onShow: function() {
    // 如果是从其他页面返回，可能需要刷新数据
    if (this.data.pageState === 'success') {
      // 根据当前标签页加载对应数据
      this.loadTabData(this.data.activeTab);
    }
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    // 根据当前标签页刷新对应数据
    this.loadTabData(this.data.activeTab, true);

    // 停止下拉刷新
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 加载标签页数据
  loadTabData: function(tab, forceRefresh = false) {
    switch (tab) {
      case 'dashboard':
        this.loadDashboardData(forceRefresh);
        break;
      case 'category':
        this.loadCategoryData(this.data.currentCategory, this.data.timeRange, forceRefresh);
        break;
    }
  },

  // 切换标签页
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    if (tab !== this.data.activeTab) {
      this.setData({ activeTab: tab });
      this.loadTabData(tab);
    }
  },

  // 加载仪表盘数据
  loadDashboardData: function(forceRefresh = false) {
    this.setData({
      isLoading: true,
      pageState: 'loading'
    });

    statisticsManager.getDashboardData(forceRefresh)
      .then(data => {
        this.setData({
          dashboardData: data,
          currentDate: dateUtil.formatDate(new Date()),
          isLoading: false,
          pageState: 'success'
        });
      })
      .catch(error => {
        console.error('加载仪表盘数据失败:', error);
        this.setData({
          isLoading: false,
          pageState: 'error',
          errorMsg: '加载数据失败，请稍后重试'
        });
      });
  },

  // 切换分类
  switchCategory: function(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({ currentCategory: category });
    this.loadCategoryData(category, this.data.timeRange);
  },

  // 切换时间范围
  switchTimeRange: function(e) {
    const range = e.currentTarget.dataset.range;
    this.setData({ timeRange: range });
    this.loadCategoryData(this.data.currentCategory, range);
  },

  // 加载分类数据
  loadCategoryData: function(category, timeRange = 'week', forceRefresh = false) {
    this.setData({ isLoading: true });

    statisticsManager.getCategoryData(category, timeRange, forceRefresh)
      .then(data => {
        // 更新对应分类的数据
        const categoryDataKey = `categoryData.${category}`;
        const updateData = {};
        updateData[categoryDataKey] = data;
        updateData.isLoading = false;

        this.setData(updateData, () => {
          // 数据更新后，初始化图表
          if (category === 'resident') {
            this.initResidentPieChart();
          } else if (category === 'workorder') {
            this.initWorkorderTypeChart();
            this.initWorkorderStatusChart();
          } else if (category === 'house') {
            this.initHouseTypeChart();
            this.initHouseBuildingChart();
          } else if (category === 'parking') {
            this.initParkingTypeChart();
            this.initVehicleTypeChart();
          } else if (category === 'facility') {
            this.initFacilityTypeChart();
          } else if (category === 'visitor') {
            this.initVisitorPurposeChart();
            this.initVisitorTimeChart();
          }
        });
      })
      .catch(error => {
        console.error(`加载${category}分类数据失败:`, error);
        this.setData({
          isLoading: false,
          pageState: 'error',
          errorMsg: '加载数据失败，请稍后重试'
        });
      });
  },

  // 初始化居民类型分布饼图
  initResidentPieChart: function() {
    try {
      const residentData = this.data.categoryData.resident;
      if (!residentData || !residentData.ownerCount) {
        console.log('居民数据不完整，无法绘制饼图');
        return;
      }

      // 准备饼图数据
      const pieData = [
        { name: '业主', data: residentData.ownerCount || 0 },
        { name: '租户', data: residentData.tenantCount || 0 },
        { name: '家属', data: residentData.familyCount || 0 }
      ];

      // 获取系统信息
      const systemInfo = wx.getSystemInfoSync();
      const windowWidth = systemInfo.windowWidth;

      // 计算饼图尺寸
      const size = 280; // rpx
      const canvasWidth = Math.floor(size * systemInfo.windowWidth / 750);

      // 创建饼图
      const ctx = wx.createCanvasContext('residentPieChart');
      if (!ctx) {
        console.error('无法获取饼图Canvas上下文');
        return;
      }

      // 创建图表
      try {
        // 清空画布
        ctx.clearRect(0, 0, canvasWidth, canvasWidth);

        wxCharts.create({
          type: 'pie',
          canvas: ctx,
          series: pieData,
          width: canvasWidth,
          height: canvasWidth,
          padding: 20, // 增加内边距，确保饼图完全显示
          colors: ['#4f46e5', '#3b82f6', '#60a5fa'],
          showLegend: false // 不显示图例，因为我们已经有自定义图例
        });

        // 绘制到画布
        ctx.draw();
      } catch (chartError) {
        console.error('创建图表失败:', chartError);
      }
    } catch (e) {
      console.error('初始化居民类型分布饼图失败:', e);
    }
  },

  // 初始化工单类型分布饼图
  initWorkorderTypeChart: function() {
    try {
      const workorderData = this.data.categoryData.workorder;
      if (!workorderData || !workorderData.typeDistribution || workorderData.typeDistribution.length === 0) {
        console.log('工单类型数据不完整，无法绘制饼图');
        return;
      }

      // 准备饼图数据
      const pieData = workorderData.typeDistribution.map(item => {
        return {
          name: item.type,
          data: item.count
        };
      });

      // 获取系统信息
      const systemInfo = wx.getSystemInfoSync();

      // 计算饼图尺寸 (将rpx转为px)
      const canvasSize = 280 * systemInfo.windowWidth / 750;

      // 创建饼图
      const ctx = wx.createCanvasContext('workorderTypeChart');
      if (!ctx) {
        console.error('无法获取饼图Canvas上下文');
        return;
      }

      // 创建图表
      try {
        // 清空画布
        ctx.clearRect(0, 0, canvasSize, canvasSize);

        // 确保颜色与图例一致
        const colors = {
          '维修': '#4f46e5', // 深蓝色
          '投诉': '#3b82f6', // 蓝色
          '建议': '#60a5fa', // 浅蓝色
          '其他': '#93c5fd'  // 更浅的蓝色
        };

        // 根据类型名称分配颜色
        const chartColors = pieData.map(item => colors[item.name] || '#93c5fd');

        wxCharts.create({
          type: 'pie',
          canvas: ctx,
          series: pieData,
          width: canvasSize,
          height: canvasSize,
          padding: 20,
          colors: chartColors,
          showLegend: false // 不显示图例，因为我们已经有自定义图例
        });

        // 绘制到画布
        ctx.draw();
      } catch (chartError) {
        console.error('创建工单类型分布图表失败:', chartError);
      }
    } catch (e) {
      console.error('初始化工单类型分布饼图失败:', e);
    }
  },

  // 初始化工单状态分布柱状图 - 修改版
  initWorkorderStatusChart: function() {
    try {
      const workorderData = this.data.categoryData.workorder;
      if (!workorderData || !workorderData.statusDistribution || workorderData.statusDistribution.length === 0) {
        console.log('工单状态数据不完整，无法绘制柱状图');
        return;
      }

      // 准备柱状图数据
      const categories = workorderData.statusDistribution.map(item => item.status);
      const series = [{
        name: '工单数量',
        data: workorderData.statusDistribution.map(item => item.count),
        format: function(val) {
          return val.toFixed(0); // 显示整数
        }
      }];

      // 获取系统信息
      const systemInfo = wx.getSystemInfoSync();
      const windowWidth = systemInfo.windowWidth;

      // 创建柱状图
      const ctx = wx.createCanvasContext('workorderStatusChart');
      if (!ctx) {
        console.error('无法获取柱状图Canvas上下文');
        return;
      }

      // 清空画布
      ctx.clearRect(0, 0, windowWidth, 350);

      // 使用简单的原生绘图方式绘制柱状图
      const canvasWidth = windowWidth - 40;
      const canvasHeight = 300;
      const bottomPadding = 30; // 减少底部留白，因为我们将在柱子上方显示标签
      const leftPadding = 40; // 左侧留出空间显示Y轴

      // 动态计算柱子宽度和间距
      const dataCount = series[0].data.length;
      const maxBarWidth = 60; // 最大柱子宽度
      const minBarWidth = 20; // 最小柱子宽度
      const barGapRatio = 0.5; // 柱子间距与宽度的比例
      const availableWidth = canvasWidth - leftPadding - 20; // 可用宽度（减去左边距和右边距）
      const barWidth = Math.min(maxBarWidth, Math.max(minBarWidth, availableWidth / (dataCount * (1 + barGapRatio))));
      const barGap = barWidth * barGapRatio;

      // 找出最大值，用于计算高度比例
      const maxValue = Math.max(...series[0].data);
      const heightRatio = (canvasHeight - bottomPadding) / (maxValue * 1.2); // 留出20%的顶部空间

      // 绘制Y轴
      ctx.beginPath();
      ctx.setStrokeStyle('#dddddd'); // 使用更淡的颜色
      ctx.setLineWidth(1);
      ctx.moveTo(leftPadding, 10);
      ctx.lineTo(leftPadding, canvasHeight - bottomPadding);
      ctx.stroke();

      // 绘制X轴
      ctx.beginPath();
      ctx.moveTo(leftPadding, canvasHeight - bottomPadding);
      ctx.lineTo(canvasWidth, canvasHeight - bottomPadding);
      ctx.stroke();

      // 绘制Y轴刻度
      const yAxisSteps = 5; // Y轴分5个刻度
      const yAxisStepValue = Math.ceil(maxValue * 1.2 / yAxisSteps); // 每个刻度的值

      ctx.setFontSize(10);
      ctx.setFillStyle('#999999');
      ctx.setTextAlign('right');

      for (let i = 0; i <= yAxisSteps; i++) {
        const yValue = i * yAxisStepValue;
        const y = canvasHeight - bottomPadding - (yValue * heightRatio);

        // 绘制刻度线
        ctx.beginPath();
        ctx.moveTo(leftPadding - 5, y);
        ctx.lineTo(leftPadding, y);
        ctx.stroke();

        // 绘制刻度值
        ctx.fillText(yValue.toString(), leftPadding - 8, y + 3);

        // 绘制网格线（虚线）
        if (i > 0) {
          ctx.beginPath();
          ctx.setLineDash([2, 2]); // 设置虚线样式
          ctx.moveTo(leftPadding, y);
          ctx.lineTo(canvasWidth, y);
          ctx.setStrokeStyle('#eeeeee'); // 使用更淡的颜色
          ctx.stroke();
          ctx.setLineDash([]); // 恢复实线
          ctx.setStrokeStyle('#dddddd'); // 恢复轴线颜色
        }
      }

      // 绘制Y轴标题（垂直文字）
      ctx.save();
      ctx.translate(15, canvasHeight / 2);
      ctx.rotate(-Math.PI / 2);
      ctx.setTextAlign('center');
      ctx.setFillStyle('#666666');
      ctx.setFontSize(11);
      ctx.fillText('工单数量', 0, 0);
      ctx.restore();

      // 绘制柱状图
      // 使用更鲜明的颜色
      const colors = ['#f97316', '#2563eb', '#059669']; // 更鲜明的橙色、蓝色、绿色

      for (let i = 0; i < series[0].data.length; i++) {
        const value = series[0].data[i];
        const barHeight = value * heightRatio;
        const x = leftPadding + (i * (barWidth + barGap)) + barGap;
        const y = canvasHeight - bottomPadding - barHeight;

        // 绘制柱子
        ctx.beginPath();
        ctx.setFillStyle(colors[i % colors.length]);

        // 添加圆角效果
        const radius = 4; // 圆角半径
        ctx.moveTo(x, y + radius);
        ctx.arcTo(x, y, x + radius, y, radius);
        ctx.lineTo(x + barWidth - radius, y);
        ctx.arcTo(x + barWidth, y, x + barWidth, y + radius, radius);
        ctx.lineTo(x + barWidth, y + barHeight);
        ctx.lineTo(x, y + barHeight);
        ctx.closePath();
        ctx.fill();

        // 绘制类别标签 - 直接在柱子上方显示
        ctx.setFontSize(12);
        ctx.setFillStyle('#000000'); // 使用黑色以增加可见性
        ctx.setTextAlign('center');

        // 处理长文本
        let label = categories[i];
        if (label.length > 6) {
          label = label.substring(0, 6) + '..';
        }

        // 在柱子上方显示标签
        ctx.fillText(label, x + barWidth / 2, y - 20);

        // 绘制数值
        ctx.setFontSize(12);
        ctx.setFillStyle('#333333');
        ctx.setTextAlign('center');
        ctx.fillText(value.toString(), x + barWidth / 2, y - 5);

        // 绘制X轴刻度线
        ctx.beginPath();
        ctx.setStrokeStyle('#dddddd');
        ctx.moveTo(x + barWidth / 2, canvasHeight - bottomPadding);
        ctx.lineTo(x + barWidth / 2, canvasHeight - bottomPadding + 5);
        ctx.stroke();
      }

      // 绘制到画布
      ctx.draw();
    } catch (e) {
      console.error('初始化工单状态分布柱状图失败:', e);
    }
  },

  // 初始化房屋类型分布饼图
  initHouseTypeChart: function() {
    try {
      const houseData = this.data.categoryData.house;
      if (!houseData || !houseData.typeDistribution || houseData.typeDistribution.length === 0) {
        console.log('房屋类型数据不完整，无法绘制饼图');
        return;
      }

      // 准备饼图数据
      const pieData = houseData.typeDistribution.map(item => {
        return {
          name: item.type,
          data: item.count
        };
      });

      // 获取系统信息
      const systemInfo = wx.getSystemInfoSync();

      // 计算饼图尺寸 (将rpx转为px)
      const canvasSize = 280 * systemInfo.windowWidth / 750;

      // 创建饼图
      const ctx = wx.createCanvasContext('houseTypeChart');
      if (!ctx) {
        console.error('无法获取饼图Canvas上下文');
        return;
      }

      // 创建图表
      try {
        // 清空画布
        ctx.clearRect(0, 0, canvasSize, canvasSize);

        // 确保颜色与图例一致
        const colors = ['#4f46e5', '#3b82f6', '#60a5fa', '#93c5fd'];

        wxCharts.create({
          type: 'pie',
          canvas: ctx,
          series: pieData,
          width: canvasSize,
          height: canvasSize,
          padding: 20,
          colors: colors,
          showLegend: false // 不显示图例，因为我们已经有自定义图例
        });

        // 绘制到画布
        ctx.draw();
      } catch (chartError) {
        console.error('创建房屋类型分布图表失败:', chartError);
      }
    } catch (e) {
      console.error('初始化房屋类型分布饼图失败:', e);
    }
  },

  // 初始化楼栋分布柱状图 - 修改版
  initHouseBuildingChart: function() {
    try {
      const houseData = this.data.categoryData.house;
      if (!houseData || !houseData.buildingDistribution || houseData.buildingDistribution.length === 0) {
        console.log('楼栋分布数据不完整，无法绘制柱状图');
        return;
      }

      // 准备柱状图数据
      const categories = houseData.buildingDistribution.map(item => item.building);
      const series = [{
        name: '房屋数量',
        data: houseData.buildingDistribution.map(item => item.count),
        format: function(val) {
          return val.toFixed(0); // 显示整数
        }
      }];

      // 获取系统信息
      const systemInfo = wx.getSystemInfoSync();
      const windowWidth = systemInfo.windowWidth;

      // 创建柱状图
      const ctx = wx.createCanvasContext('houseBuildingChart');
      if (!ctx) {
        console.error('无法获取柱状图Canvas上下文');
        return;
      }

      // 清空画布
      ctx.clearRect(0, 0, windowWidth, 350);

      // 使用简单的原生绘图方式绘制柱状图
      const canvasWidth = windowWidth - 40;
      const canvasHeight = 300;
      const bottomPadding = 30; // 减少底部留白，因为我们将在柱子上方显示标签
      const leftPadding = 40; // 左侧留出空间显示Y轴

      // 动态计算柱子宽度和间距
      const dataCount = series[0].data.length;
      const maxBarWidth = 60; // 最大柱子宽度
      const minBarWidth = 20; // 最小柱子宽度
      const barGapRatio = 0.5; // 柱子间距与宽度的比例
      const availableWidth = canvasWidth - leftPadding - 20; // 可用宽度（减去左边距和右边距）
      const barWidth = Math.min(maxBarWidth, Math.max(minBarWidth, availableWidth / (dataCount * (1 + barGapRatio))));
      const barGap = barWidth * barGapRatio;

      // 找出最大值，用于计算高度比例
      const maxValue = Math.max(...series[0].data);
      const heightRatio = (canvasHeight - bottomPadding) / (maxValue * 1.2); // 留出20%的顶部空间

      // 绘制Y轴
      ctx.beginPath();
      ctx.setStrokeStyle('#dddddd'); // 使用更淡的颜色
      ctx.setLineWidth(1);
      ctx.moveTo(leftPadding, 10);
      ctx.lineTo(leftPadding, canvasHeight - bottomPadding);
      ctx.stroke();

      // 绘制X轴
      ctx.beginPath();
      ctx.moveTo(leftPadding, canvasHeight - bottomPadding);
      ctx.lineTo(canvasWidth, canvasHeight - bottomPadding);
      ctx.stroke();

      // 绘制Y轴刻度
      const yAxisSteps = 5; // Y轴分5个刻度
      const yAxisStepValue = Math.ceil(maxValue * 1.2 / yAxisSteps); // 每个刻度的值

      ctx.setFontSize(10);
      ctx.setFillStyle('#999999');
      ctx.setTextAlign('right');

      for (let i = 0; i <= yAxisSteps; i++) {
        const yValue = i * yAxisStepValue;
        const y = canvasHeight - bottomPadding - (yValue * heightRatio);

        // 绘制刻度线
        ctx.beginPath();
        ctx.moveTo(leftPadding - 5, y);
        ctx.lineTo(leftPadding, y);
        ctx.stroke();

        // 绘制刻度值
        ctx.fillText(yValue.toString(), leftPadding - 8, y + 3);

        // 绘制网格线（虚线）
        if (i > 0) {
          ctx.beginPath();
          ctx.setLineDash([2, 2]); // 设置虚线样式
          ctx.moveTo(leftPadding, y);
          ctx.lineTo(canvasWidth, y);
          ctx.setStrokeStyle('#eeeeee'); // 使用更淡的颜色
          ctx.stroke();
          ctx.setLineDash([]); // 恢复实线
          ctx.setStrokeStyle('#dddddd'); // 恢复轴线颜色
        }
      }

      // 绘制Y轴标题（垂直文字）
      ctx.save();
      ctx.translate(15, canvasHeight / 2);
      ctx.rotate(-Math.PI / 2);
      ctx.setTextAlign('center');
      ctx.setFillStyle('#666666');
      ctx.setFontSize(11);
      ctx.fillText('房屋数量', 0, 0);
      ctx.restore();

      // 绘制柱状图
      // 使用更鲜明的蓝色系列
      const colors = ['#4338ca', '#3b82f6', '#60a5fa', '#93c5fd']; // 更鲜明的蓝色系列

      for (let i = 0; i < series[0].data.length; i++) {
        const value = series[0].data[i];
        const barHeight = value * heightRatio;
        const x = leftPadding + (i * (barWidth + barGap)) + barGap;
        const y = canvasHeight - bottomPadding - barHeight;

        // 绘制柱子
        ctx.beginPath();
        ctx.setFillStyle(colors[i % colors.length]);

        // 添加圆角效果
        const radius = 4; // 圆角半径
        ctx.moveTo(x, y + radius);
        ctx.arcTo(x, y, x + radius, y, radius);
        ctx.lineTo(x + barWidth - radius, y);
        ctx.arcTo(x + barWidth, y, x + barWidth, y + radius, radius);
        ctx.lineTo(x + barWidth, y + barHeight);
        ctx.lineTo(x, y + barHeight);
        ctx.closePath();
        ctx.fill();

        // 绘制类别标签 - 直接在柱子上方显示
        ctx.setFontSize(12);
        ctx.setFillStyle('#000000'); // 使用黑色以增加可见性
        ctx.setTextAlign('center');

        // 处理长文本
        let label = categories[i];
        if (label.length > 6) {
          label = label.substring(0, 6) + '..';
        }

        // 在柱子上方显示标签
        ctx.fillText(label, x + barWidth / 2, y - 20);

        // 绘制数值
        ctx.setFontSize(12);
        ctx.setFillStyle('#333333');
        ctx.setTextAlign('center');
        ctx.fillText(value.toString(), x + barWidth / 2, y - 5);

        // 绘制X轴刻度线
        ctx.beginPath();
        ctx.setStrokeStyle('#dddddd');
        ctx.moveTo(x + barWidth / 2, canvasHeight - bottomPadding);
        ctx.lineTo(x + barWidth / 2, canvasHeight - bottomPadding + 5);
        ctx.stroke();
      }

      // 绘制到画布
      ctx.draw();
    } catch (e) {
      console.error('初始化楼栋分布柱状图失败:', e);
    }
  },

  // 初始化车位类型分布饼图
  initParkingTypeChart: function() {
    try {
      const parkingData = this.data.categoryData.parking;
      if (!parkingData || !parkingData.typeDistribution || parkingData.typeDistribution.length === 0) {
        console.log('车位类型数据不完整，无法绘制饼图');
        return;
      }

      // 准备饼图数据
      const pieData = parkingData.typeDistribution.map(item => {
        return {
          name: item.type,
          data: item.count
        };
      });

      // 获取系统信息
      const systemInfo = wx.getSystemInfoSync();

      // 计算饼图尺寸 (将rpx转为px)
      const canvasSize = 280 * systemInfo.windowWidth / 750;

      // 创建饼图
      const ctx = wx.createCanvasContext('parkingTypeChart');
      if (!ctx) {
        console.error('无法获取饼图Canvas上下文');
        return;
      }

      // 创建图表
      try {
        // 清空画布
        ctx.clearRect(0, 0, canvasSize, canvasSize);

        // 确保颜色与图例一致
        const colors = ['#4f46e5', '#3b82f6'];

        wxCharts.create({
          type: 'pie',
          canvas: ctx,
          series: pieData,
          width: canvasSize,
          height: canvasSize,
          padding: 20,
          colors: colors,
          showLegend: false // 不显示图例，因为我们已经有自定义图例
        });

        // 绘制到画布
        ctx.draw();
      } catch (chartError) {
        console.error('创建车位类型分布图表失败:', chartError);
      }
    } catch (e) {
      console.error('初始化车位类型分布饼图失败:', e);
    }
  },

  // 初始化车辆类型分布饼图
  initVehicleTypeChart: function() {
    try {
      const parkingData = this.data.categoryData.parking;
      if (!parkingData || !parkingData.vehicleTypeDistribution || parkingData.vehicleTypeDistribution.length === 0) {
        console.log('车辆类型数据不完整，无法绘制饼图');
        return;
      }

      // 准备饼图数据
      const pieData = parkingData.vehicleTypeDistribution.map(item => {
        return {
          name: item.type,
          data: item.count
        };
      });

      // 获取系统信息
      const systemInfo = wx.getSystemInfoSync();

      // 计算饼图尺寸 (将rpx转为px)
      const canvasSize = 280 * systemInfo.windowWidth / 750;

      // 创建饼图
      const ctx = wx.createCanvasContext('vehicleTypeChart');
      if (!ctx) {
        console.error('无法获取饼图Canvas上下文');
        return;
      }

      // 创建图表
      try {
        // 清空画布
        ctx.clearRect(0, 0, canvasSize, canvasSize);

        // 确保颜色与图例一致
        const colors = ['#4f46e5', '#3b82f6', '#60a5fa'];

        wxCharts.create({
          type: 'pie',
          canvas: ctx,
          series: pieData,
          width: canvasSize,
          height: canvasSize,
          padding: 20,
          colors: colors,
          showLegend: false // 不显示图例，因为我们已经有自定义图例
        });

        // 绘制到画布
        ctx.draw();
      } catch (chartError) {
        console.error('创建车辆类型分布图表失败:', chartError);
      }
    } catch (e) {
      console.error('初始化车辆类型分布饼图失败:', e);
    }
  },

  // 初始化设施类型分布饼图
  initFacilityTypeChart: function() {
    try {
      const facilityData = this.data.categoryData.facility;
      if (!facilityData || !facilityData.typeDistribution || facilityData.typeDistribution.length === 0) {
        console.log('设施类型数据不完整，无法绘制饼图');
        return;
      }

      // 准备饼图数据
      const pieData = facilityData.typeDistribution.map(item => {
        return {
          name: item.type,
          data: item.count
        };
      });

      // 获取系统信息
      const systemInfo = wx.getSystemInfoSync();

      // 计算饼图尺寸 (将rpx转为px)
      const canvasSize = 280 * systemInfo.windowWidth / 750;

      // 创建饼图
      const ctx = wx.createCanvasContext('facilityTypeChart');
      if (!ctx) {
        console.error('无法获取饼图Canvas上下文');
        return;
      }

      // 创建图表
      try {
        // 清空画布
        ctx.clearRect(0, 0, canvasSize, canvasSize);

        // 确保颜色与图例一致
        const colors = ['#4f46e5', '#3b82f6', '#60a5fa', '#93c5fd', '#bfdbfe'];

        wxCharts.create({
          type: 'pie',
          canvas: ctx,
          series: pieData,
          width: canvasSize,
          height: canvasSize,
          padding: 20,
          colors: colors,
          showLegend: false // 不显示图例，因为我们已经有自定义图例
        });

        // 绘制到画布
        ctx.draw();
      } catch (chartError) {
        console.error('创建设施类型分布图表失败:', chartError);
      }
    } catch (e) {
      console.error('初始化设施类型分布饼图失败:', e);
    }
  },

  // 初始化访客目的分布饼图
  initVisitorPurposeChart: function() {
    try {
      const visitorData = this.data.categoryData.visitor;
      if (!visitorData || !visitorData.purposeDistribution || visitorData.purposeDistribution.length === 0) {
        console.log('访客目的数据不完整，无法绘制饼图');
        return;
      }

      // 准备饼图数据
      const pieData = visitorData.purposeDistribution.map(item => {
        return {
          name: item.purpose,
          data: item.count
        };
      });

      // 获取系统信息
      const systemInfo = wx.getSystemInfoSync();

      // 计算饼图尺寸 (将rpx转为px)
      const canvasSize = 280 * systemInfo.windowWidth / 750;

      // 创建饼图
      const ctx = wx.createCanvasContext('visitorPurposeChart');
      if (!ctx) {
        console.error('无法获取饼图Canvas上下文');
        return;
      }

      // 创建图表
      try {
        // 清空画布
        ctx.clearRect(0, 0, canvasSize, canvasSize);

        // 确保颜色与图例一致
        const colors = ['#4f46e5', '#3b82f6', '#60a5fa', '#93c5fd'];

        wxCharts.create({
          type: 'pie',
          canvas: ctx,
          series: pieData,
          width: canvasSize,
          height: canvasSize,
          padding: 20,
          colors: colors,
          showLegend: false // 不显示图例，因为我们已经有自定义图例
        });

        // 绘制到画布
        ctx.draw();
      } catch (chartError) {
        console.error('创建访客目的分布图表失败:', chartError);
      }
    } catch (e) {
      console.error('初始化访客目的分布饼图失败:', e);
    }
  },

  // 初始化访客时段分布柱状图 - 修改版
  initVisitorTimeChart: function() {
    try {
      const visitorData = this.data.categoryData.visitor;
      if (!visitorData || !visitorData.timeDistribution || visitorData.timeDistribution.length === 0) {
        console.log('访客时段数据不完整，无法绘制柱状图');
        return;
      }

      // 准备柱状图数据
      const categories = visitorData.timeDistribution.map(item => item.time);
      const series = [{
        name: '访客数量',
        data: visitorData.timeDistribution.map(item => item.count),
        format: function(val) {
          return val.toFixed(0); // 显示整数
        }
      }];

      // 获取系统信息
      const systemInfo = wx.getSystemInfoSync();
      const windowWidth = systemInfo.windowWidth;

      // 创建柱状图
      const ctx = wx.createCanvasContext('visitorTimeChart');
      if (!ctx) {
        console.error('无法获取柱状图Canvas上下文');
        return;
      }

      // 清空画布
      ctx.clearRect(0, 0, windowWidth, 350);

      // 使用简单的原生绘图方式绘制柱状图
      const canvasWidth = windowWidth - 40;
      const canvasHeight = 300;
      const bottomPadding = 30; // 减少底部留白，因为我们将在柱子上方显示标签
      const leftPadding = 40; // 左侧留出空间显示Y轴

      // 动态计算柱子宽度和间距
      const dataCount = series[0].data.length;
      const maxBarWidth = 60; // 最大柱子宽度
      const minBarWidth = 20; // 最小柱子宽度
      const barGapRatio = 0.5; // 柱子间距与宽度的比例
      const availableWidth = canvasWidth - leftPadding - 20; // 可用宽度（减去左边距和右边距）
      const barWidth = Math.min(maxBarWidth, Math.max(minBarWidth, availableWidth / (dataCount * (1 + barGapRatio))));
      const barGap = barWidth * barGapRatio;

      // 找出最大值，用于计算高度比例
      const maxValue = Math.max(...series[0].data);
      const heightRatio = (canvasHeight - bottomPadding) / (maxValue * 1.2); // 留出20%的顶部空间

      // 绘制Y轴
      ctx.beginPath();
      ctx.setStrokeStyle('#dddddd'); // 使用更淡的颜色
      ctx.setLineWidth(1);
      ctx.moveTo(leftPadding, 10);
      ctx.lineTo(leftPadding, canvasHeight - bottomPadding);
      ctx.stroke();

      // 绘制X轴
      ctx.beginPath();
      ctx.moveTo(leftPadding, canvasHeight - bottomPadding);
      ctx.lineTo(canvasWidth, canvasHeight - bottomPadding);
      ctx.stroke();

      // 绘制Y轴刻度
      const yAxisSteps = 5; // Y轴分5个刻度
      const yAxisStepValue = Math.ceil(maxValue * 1.2 / yAxisSteps); // 每个刻度的值

      ctx.setFontSize(10);
      ctx.setFillStyle('#999999');
      ctx.setTextAlign('right');

      for (let i = 0; i <= yAxisSteps; i++) {
        const yValue = i * yAxisStepValue;
        const y = canvasHeight - bottomPadding - (yValue * heightRatio);

        // 绘制刻度线
        ctx.beginPath();
        ctx.moveTo(leftPadding - 5, y);
        ctx.lineTo(leftPadding, y);
        ctx.stroke();

        // 绘制刻度值
        ctx.fillText(yValue.toString(), leftPadding - 8, y + 3);

        // 绘制网格线（虚线）
        if (i > 0) {
          ctx.beginPath();
          ctx.setLineDash([2, 2]); // 设置虚线样式
          ctx.moveTo(leftPadding, y);
          ctx.lineTo(canvasWidth, y);
          ctx.setStrokeStyle('#eeeeee'); // 使用更淡的颜色
          ctx.stroke();
          ctx.setLineDash([]); // 恢复实线
          ctx.setStrokeStyle('#dddddd'); // 恢复轴线颜色
        }
      }

      // 绘制Y轴标题（垂直文字）
      ctx.save();
      ctx.translate(15, canvasHeight / 2);
      ctx.rotate(-Math.PI / 2);
      ctx.setTextAlign('center');
      ctx.setFillStyle('#666666');
      ctx.setFontSize(11);
      ctx.fillText('访客数量', 0, 0);
      ctx.restore();

      // 绘制柱状图
      // 使用更鲜明的蓝色系列
      const colors = ['#1e40af', '#3b82f6', '#60a5fa']; // 更鲜明的蓝色系列

      for (let i = 0; i < series[0].data.length; i++) {
        const value = series[0].data[i];
        const barHeight = value * heightRatio;
        const x = leftPadding + (i * (barWidth + barGap)) + barGap;
        const y = canvasHeight - bottomPadding - barHeight;

        // 绘制柱子
        ctx.beginPath();
        ctx.setFillStyle(colors[i % colors.length]);

        // 添加圆角效果
        const radius = 4; // 圆角半径
        ctx.moveTo(x, y + radius);
        ctx.arcTo(x, y, x + radius, y, radius);
        ctx.lineTo(x + barWidth - radius, y);
        ctx.arcTo(x + barWidth, y, x + barWidth, y + radius, radius);
        ctx.lineTo(x + barWidth, y + barHeight);
        ctx.lineTo(x, y + barHeight);
        ctx.closePath();
        ctx.fill();

        // 绘制类别标签 - 直接在柱子上方显示
        ctx.setFontSize(12);
        ctx.setFillStyle('#000000'); // 使用黑色以增加可见性
        ctx.setTextAlign('center');

        // 处理长文本
        let label = categories[i];
        if (label.length > 6) {
          label = label.substring(0, 6) + '..';
        }

        // 在柱子上方显示标签
        ctx.fillText(label, x + barWidth / 2, y - 20);

        // 绘制数值
        ctx.setFontSize(12);
        ctx.setFillStyle('#333333');
        ctx.setTextAlign('center');
        ctx.fillText(value.toString(), x + barWidth / 2, y - 5);

        // 绘制X轴刻度线
        ctx.beginPath();
        ctx.setStrokeStyle('#dddddd');
        ctx.moveTo(x + barWidth / 2, canvasHeight - bottomPadding);
        ctx.lineTo(x + barWidth / 2, canvasHeight - bottomPadding + 5);
        ctx.stroke();
      }

      // 绘制到画布
      ctx.draw();
    } catch (e) {
      console.error('初始化访客时段分布柱状图失败:', e);
    }
  },

  // 获取分类标题
  getCategoryTitle: function() {
    const titles = {
      resident: '居民统计',
      house: '房屋统计',
      parking: '车位统计',
      facility: '设施统计',
      workorder: '工单统计',
      visitor: '访客统计'
    };
    return titles[this.data.currentCategory] || '分类统计';
  },

  // 导航到工单页面
  navigateToWorkOrder: function() {
    wx.navigateTo({
      url: '/pages/property/workorder/workorder'
    });
  },

  // 导航到访客页面
  navigateToVisitor: function() {
    wx.navigateTo({
      url: '/pages/property/visitor-stats/index'
    });
  },

  // 导航到设施页面
  navigateToFacility: function() {
    wx.navigateTo({
      url: '/pages/property/facility/facility'
    });
  },

  // 导航到居民页面
  navigateToResident: function() {
    wx.navigateTo({
      url: '/pages/property/resident/resident'
    });
  }
});
