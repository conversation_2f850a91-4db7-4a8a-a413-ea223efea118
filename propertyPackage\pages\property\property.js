// pages/property/property.js
const util = require('../../../utils/util.js')

Page({
  data: {
    darkMode: false,
    services: [
      {
        id: 1,
        name: '工单管理',
        icon: 'workorder',
        color: '#333333',
        url: '/propertyPackage/pages/property/workorder/workorder'
      },
      {
        id: 2,
        name: '居民管理',
        icon: 'resident',
        color: '#ff8c00',
        url: '/propertyPackage/pages/property/resident/resident'
      },
      {
        id: 3,
        name: '设施管理',
        icon: 'facility',
        color: '#ff8c00',
        url: '/propertyPackage/pages/property/facility/facility'
      },
      {
        id: 4,
        name: '公告发布',
        icon: 'announcement',
        color: '#333333',
        url: '/propertyPackage/pages/property/announcement/announcement'
      },
      {
        id: 5,
        name: '数据统计',
        icon: 'statistics',
        color: '#333333',
        url: '/propertyPackage/pages/property/statistics/statistics'
      },
      {
        id: 6,
        name: '巡检记录',
        icon: 'inspection',
        color: '#333333',
        url: '/propertyPackage/pages/property/inspection/inspection'
      },
      {
        id: 7,
        name: '员工管理',
        icon: 'staff',
        color: '#ff8c00',
        url: '/propertyPackage/pages/property/staff/staff-list'
      },
      {
        id: 8,
        name: '访客核销',
        icon: 'visitor',
        color: '#ff8c00',
        url: '/propertyPackage/pages/property/visitor-verify/index'
      },
      {
        id: 9,
        name: '访客统计',
        icon: 'visitor-stats',
        color: '#ff8c00',
        url: '/propertyPackage/pages/property/visitor-stats/index'
      }
    ]
  },

  onLoad: function() {
    // 检查暗黑模式
    const app = getApp()
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      })
    }

    // 添加页面进入动画
    this.animatePageEnter();

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '物业管理工作台'
    });
  },

  // 页面进入动画
  animatePageEnter: function() {
    // 使用微信小程序的动画API
    const animation = wx.createAnimation({
      duration: 800,
      timingFunction: 'ease',
    });

    // 初始状态
    animation.opacity(0).translateY(30).step({ duration: 0 });
    this.setData({
      animationData: animation.export()
    });

    // 延迟一点执行入场动画
    setTimeout(() => {
      animation.opacity(1).translateY(0).step();
      this.setData({
        animationData: animation.export()
      });
    }, 100);
  },

  // 导航到服务页面
  navigateToService: function(e) {
    const url = e.currentTarget.dataset.url
    // 直接导航，不再检查认证，因为用户已经通过物业管理登录认证
    wx.navigateTo({
      url: url
    })
  },

  // 阻止事件冒泡
  stopPropagation: function() {
    // 阻止事件冒泡
  }
})
