// 批量邀请页面
const VisitorUtils = require('../../../../utils/visitor-utils');
const VisitorManager = require('../../../../utils/visitor-manager');
const NotificationManager = require('../../../../utils/notification-manager');
const dateUtil = require('@/utils/dateUtil');
const util = require('@/utils/util');

Page({
  data: {
    statusBarHeight: 20, // 状态栏高度，默认值
    visitorType: 'person', // 访客类型：person-人员访客，car-车辆访客
    formData: {
      visitDate: '',
      visitTime: '',
      duration: 2, // 默认滞留时长2小时
      purpose: '',
      remarks: ''
    },
    errors: {}, // 表单错误信息
    visitors: [], // 访客列表
    currentVisitor: { // 当前编辑的访客
      name: '',
      phone: '',
      carNumber: ''
    },
    currentVisitorErrors: {}, // 当前访客表单错误
    editingIndex: -1, // 当前编辑的访客索引，-1表示新增

    // 日期时间选择器相关
    showDateTimePicker: false,
    dateArray: [], // 日期数组
    hourArray: [], // 小时数组
    minuteArray: [], // 分钟数组
    dateTimePickerValue: [0, 0, 0], // 选择器当前值

    // 滞留时长选择器相关
    showDurationPicker: false,
    durationOptions: [1, 2, 3, 4, 8, 12, 24, 48], // 滞留时长选项

    // 来访目的选择器相关
    showPurposePicker: false,
    purposeOptions: ['探亲访友', '业务洽谈', '送货上门', '维修服务', '其他'],

    // 车牌号历史记录
    showCarNumberHistory: false,
    carNumberHistory: [],

    // 添加访客弹窗
    showAddVisitorModal: false,

    // 批量邀请结果
    showResultModal: false,
    batchResult: {
      success: false,
      successCount: 0,
      visitors: []
    }
  },

  onLoad: function() {
    // 获取状态栏高度
    this.getStatusBarHeight();

    // 初始化表单数据
    this.initFormData();

    // 初始化日期时间选择器
    this.initDateTimePicker();

    // 获取车牌号历史记录
    this.getCarNumberHistory();
  },

  // 获取状态栏高度
  getStatusBarHeight: function() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      this.setData({
        statusBarHeight: systemInfo.statusBarHeight
      });
    } catch (e) {
      console.error('获取状态栏高度失败', e);
    }
  },

  // 初始化表单数据
  initFormData: function() {
    // 设置默认来访时间为当前时间+30分钟
    const now = new Date();
    now.setMinutes(now.getMinutes() + 30);

    const visitDate = dateUtil.formatDate(now);
    const visitTime = dateUtil.formatTimeHM(now);

    this.setData({
      'formData.visitDate': visitDate,
      'formData.visitTime': visitTime
    });
  },

  // 初始化日期时间选择器
  initDateTimePicker: function() {
    // 获取未来7天的日期
    const dateArray = VisitorUtils.getFutureDates(7);

    // 获取小时数组
    const hourArray = VisitorUtils.getHours();

    // 获取分钟数组（每5分钟一个选项）
    const minuteArray = VisitorUtils.getMinutes(5);

    // 设置默认选中值
    const now = new Date();
    now.setMinutes(now.getMinutes() + 30);

    const defaultDateIndex = 0; // 默认选中今天
    const defaultHourIndex = now.getHours();
    const defaultMinuteIndex = Math.floor(now.getMinutes() / 5);

    this.setData({
      dateArray: dateArray,
      hourArray: hourArray,
      minuteArray: minuteArray,
      dateTimePickerValue: [defaultDateIndex, defaultHourIndex, defaultMinuteIndex]
    });
  },

  // 获取车牌号历史记录
  getCarNumberHistory: function() {
    const history = VisitorManager.getCarNumberHistory();
    this.setData({
      carNumberHistory: history
    });
  },

  // 设置访客类型
  setVisitorType: function(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      visitorType: type
    });
  },

  // 表单输入处理函数
  inputRemarks: function(e) {
    this.setData({
      'formData.remarks': e.detail.value
    });
  },

  // 当前访客表单输入处理
  inputCurrentVisitorName: function(e) {
    this.setData({
      'currentVisitor.name': e.detail.value,
      'currentVisitorErrors.name': ''
    });
  },

  inputCurrentVisitorPhone: function(e) {
    this.setData({
      'currentVisitor.phone': e.detail.value,
      'currentVisitorErrors.phone': ''
    });
  },

  inputCurrentVisitorCarNumber: function(e) {
    this.setData({
      'currentVisitor.carNumber': e.detail.value,
      'currentVisitorErrors.carNumber': ''
    });
  },

  // 粘贴手机号
  pastePhoneNumber: function() {
    const util = require('@/utils/util');
    util.pastePhoneNumber(
      (phoneNumber) => {
        this.setData({
          'currentVisitor.phone': phoneNumber,
          'currentVisitorErrors.phone': ''
        });
      },
      () => {
        // 错误处理已在util.pastePhoneNumber中处理
      }
    );
  },

  // 显示日期时间选择器
  showDateTimePicker: function() {
    this.setData({
      showDateTimePicker: true
    });
  },

  // 隐藏日期时间选择器
  hideDateTimePicker: function() {
    this.setData({
      showDateTimePicker: false
    });
  },

  // 日期时间选择器变化
  onDateTimePickerChange: function(e) {
    this.setData({
      dateTimePickerValue: e.detail.value
    });
  },

  // 确认日期时间
  confirmDateTime: function() {
    const { dateArray, hourArray, minuteArray, dateTimePickerValue } = this.data;

    const selectedDate = dateArray[dateTimePickerValue[0]].date;
    const selectedHour = hourArray[dateTimePickerValue[1]];
    const selectedMinute = minuteArray[dateTimePickerValue[2]];

    const selectedTime = `${selectedHour}:${selectedMinute}`;

    this.setData({
      'formData.visitDate': selectedDate,
      'formData.visitTime': selectedTime,
      'errors.visitDateTime': '',
      showDateTimePicker: false
    });
  },

  // 显示滞留时长选择器
  showDurationPicker: function() {
    this.setData({
      showDurationPicker: true
    });
  },

  // 隐藏滞留时长选择器
  hideDurationPicker: function() {
    this.setData({
      showDurationPicker: false
    });
  },

  // 选择滞留时长
  selectDuration: function(e) {
    const duration = parseInt(e.currentTarget.dataset.duration);
    this.setData({
      'formData.duration': duration,
      'errors.duration': ''
    });
  },

  // 确认滞留时长
  confirmDuration: function() {
    this.setData({
      showDurationPicker: false
    });
  },

  // 显示来访目的选择器
  showPurposePicker: function() {
    this.setData({
      showPurposePicker: true
    });
  },

  // 隐藏来访目的选择器
  hidePurposePicker: function() {
    this.setData({
      showPurposePicker: false
    });
  },

  // 选择来访目的
  selectPurpose: function(e) {
    const purpose = e.currentTarget.dataset.purpose;
    this.setData({
      'formData.purpose': purpose,
      'errors.purpose': '',
      showPurposePicker: false
    });
  },

  // 显示车牌号历史记录
  showCarNumberHistory: function() {
    this.setData({
      showCarNumberHistory: true
    });
  },

  // 隐藏车牌号历史记录
  hideCarNumberHistory: function() {
    this.setData({
      showCarNumberHistory: false
    });
  },

  // 选择车牌号
  selectCarNumber: function(e) {
    const carNumber = e.currentTarget.dataset.carNumber;
    this.setData({
      'currentVisitor.carNumber': carNumber,
      'currentVisitorErrors.carNumber': '',
      showCarNumberHistory: false
    });
  },

  // 显示添加访客弹窗
  showAddVisitorModal: function() {
    this.setData({
      currentVisitor: {
        name: '',
        phone: '',
        carNumber: ''
      },
      currentVisitorErrors: {},
      editingIndex: -1,
      showAddVisitorModal: true
    });
  },

  // 隐藏添加访客弹窗
  hideAddVisitorModal: function() {
    this.setData({
      showAddVisitorModal: false
    });
  },

  // 编辑访客
  editVisitor: function(e) {
    const index = e.currentTarget.dataset.index;
    const visitor = this.data.visitors[index];

    this.setData({
      currentVisitor: {
        name: visitor.name,
        phone: visitor.phone,
        carNumber: visitor.carNumber || ''
      },
      currentVisitorErrors: {},
      editingIndex: index,
      showAddVisitorModal: true
    });
  },

  // 移除访客
  removeVisitor: function(e) {
    const index = e.currentTarget.dataset.index;
    const visitors = this.data.visitors;

    visitors.splice(index, 1);

    this.setData({
      visitors: visitors
    });
  },

  // 确认添加/编辑访客
  confirmAddVisitor: function() {
    // 验证访客信息
    if (!this.validateCurrentVisitor()) {
      return;
    }

    const { currentVisitor, editingIndex, visitors } = this.data;
    const newVisitors = [...visitors];

    if (editingIndex === -1) {
      // 添加新访客
      newVisitors.push({
        name: currentVisitor.name,
        phone: currentVisitor.phone,
        carNumber: currentVisitor.carNumber
      });
    } else {
      // 更新现有访客
      newVisitors[editingIndex] = {
        name: currentVisitor.name,
        phone: currentVisitor.phone,
        carNumber: currentVisitor.carNumber
      };
    }

    this.setData({
      visitors: newVisitors,
      showAddVisitorModal: false
    });
  },

  // 验证当前访客信息
  validateCurrentVisitor: function() {
    const { currentVisitor, visitorType } = this.data;
    let currentVisitorErrors = {};
    let isValid = true;

    // 验证访客姓名
    if (!currentVisitor.name.trim()) {
      currentVisitorErrors.name = '请输入访客姓名';
      isValid = false;
    }

    // 验证手机号
    if (!currentVisitor.phone) {
      currentVisitorErrors.phone = '请输入手机号码';
      isValid = false;
    } else if (!util.validatePhone(currentVisitor.phone)) {
      currentVisitorErrors.phone = '请输入正确的手机号码';
      isValid = false;
    }

    // 验证车牌号（车辆访客必填）
    if (visitorType === 'car' && !currentVisitor.carNumber) {
      currentVisitorErrors.carNumber = '请输入车牌号码';
      isValid = false;
    } else if (currentVisitor.carNumber && !util.validateCarNumber(currentVisitor.carNumber)) {
      currentVisitorErrors.carNumber = '请输入正确的车牌号码';
      isValid = false;
    }

    this.setData({ currentVisitorErrors });
    return isValid;
  },

  // 验证公共信息
  validateCommonInfo: function() {
    const { formData, visitorType } = this.data;
    let errors = {};
    let isValid = true;

    // 验证来访时间
    const visitDateTime = dateUtil.parseDateTime(formData.visitDate, formData.visitTime);
    const now = new Date();
    if (visitDateTime < now) {
      errors.visitDateTime = '来访时间不能早于当前时间';
      isValid = false;
    }

    // 验证滞留时长
    if (!formData.duration || formData.duration <= 0) {
      errors.duration = '请选择有效的滞留时长';
      isValid = false;
    }

    // 验证来访目的
    if (!formData.purpose) {
      errors.purpose = '请选择来访目的';
      isValid = false;
    }

    this.setData({ errors });
    return isValid;
  },

  // 提交批量邀请
  submitBatchInvite: function() {
    // 验证是否有访客
    if (this.data.visitors.length === 0) {
      wx.showToast({
        title: '请添加至少一位访客',
        icon: 'none'
      });
      return;
    }

    // 验证公共信息
    if (!this.validateCommonInfo()) {
      wx.showToast({
        title: '请完善公共信息',
        icon: 'none'
      });
      return;
    }

    // 显示加载提示
    wx.showLoading({
      title: '处理中...',
      mask: true
    });

    // 构建批量访客数据
    const batchVisitorData = this.buildBatchVisitorData();

    // 批量保存访客数据
    VisitorManager.saveBatchVisitors(batchVisitorData)
      .then(result => {
        wx.hideLoading();

        // 显示结果弹窗
        this.setData({
          batchResult: result,
          showResultModal: true
        });

        // 请求订阅访客相关通知
        this.requestVisitorNotifications(result.visitors.filter(v => v.success));
      })
      .catch(error => {
        wx.hideLoading();
        console.error('批量保存访客失败', error);

        wx.showToast({
          title: '保存失败，请重试',
          icon: 'none'
        });
      });
  },

  // 构建批量访客数据
  buildBatchVisitorData: function() {
    const { formData, visitors, visitorType } = this.data;

    // 获取住户信息
    const userInfo = wx.getStorageSync('userInfo') || {};
    const certifiedHouse = wx.getStorageSync('certifiedHouse') || {};

    // 计算结束时间
    const startDateTime = dateUtil.parseDateTime(formData.visitDate, formData.visitTime);
    const endDateTime = new Date(startDateTime.getTime() + formData.duration * 60 * 60 * 1000);
    const endTime = dateUtil.formatTimeHM(endDateTime);

    // 构建访客数据数组
    return visitors.map(visitor => {
      // 生成唯一ID
      const id = VisitorUtils.generateVisitorId();

      return {
        id: id,
        name: visitor.name,
        phone: visitor.phone,
        date: formData.visitDate,
        startTime: formData.visitTime,
        endTime: endTime,
        duration: formData.duration,
        purpose: formData.purpose,
        access_type: visitorType,
        carNumber: visitor.carNumber || '',
        remarks: formData.remarks,
        status: 'pending', // pending, visited, expired, canceled
        max_uses: 1, // 最大使用次数
        used_count: 0, // 已使用次数
        hostInfo: {
          id: userInfo.id || '',
          name: userInfo.name || '',
          phone: userInfo.phone || '',
          address: certifiedHouse.fullAddress || ''
        }
      };
    });
  },

  // 请求访客相关通知订阅
  requestVisitorNotifications: function(visitors) {
    if (!visitors || visitors.length === 0) {
      return;
    }

    // 请求访客码创建成功通知
    NotificationManager.requestVisitorCreatedSubscription()
      .then(res => {
        console.log('访客码创建成功通知订阅结果:', res);

        // 如果用户接受了订阅，则发送通知
        if (res[NotificationManager.templates.visitorCreated] === 'accept') {
          // 发送访客码创建成功通知
          visitors.forEach(visitor => {
            if (visitor.success) {
              NotificationManager.sendVisitorCreatedNotification(visitor.data)
                .then(result => {
                  console.log('发送访客码创建成功通知结果:', result);
                })
                .catch(err => {
                  console.error('发送访客码创建成功通知失败:', err);
                });
            }
          });
        }
      })
      .catch(err => {
        console.error('请求访客通知订阅失败:', err);
      });
  },

  // 查看访客凭证
  viewVisitorCredential: function(e) {
    const id = e.currentTarget.dataset.id;

    // 隐藏结果弹窗
    this.setData({
      showResultModal: false
    });

    // 跳转到访客凭证页面
    wx.navigateTo({
      url: '/pages/visitor/credential/index?id=' + id
    });
  },

  // 隐藏结果弹窗
  hideResultModal: function() {
    this.setData({
      showResultModal: false
    });
  },

  // 导航到访客列表页面
  navigateToVisitorList: function() {
    // 隐藏结果弹窗
    this.setData({
      showResultModal: false
    });

    // 跳转到访客列表页面
    wx.navigateTo({
      url: '/pages/visitor/list/index'
    });
  },

  // 导航返回
  navigateBack: function() {
    wx.navigateBack();
  }
});