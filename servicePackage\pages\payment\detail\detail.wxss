/* pages/payment/detail/detail.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}



/* 加载中状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 140, 0, 0.2);
  border-top: 4rpx solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 状态卡片 */
.status-card {
  margin: 30rpx;
  padding: 40rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.status-card.paid {
  background: linear-gradient(135deg, #4caf50, #2e7d32);
}

.status-card.unpaid {
  background: linear-gradient(135deg, #2196f3, #1976d2);
}

.status-card.overdue {
  background: linear-gradient(135deg, #f44336, #d32f2f);
}

.status-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.status-icon.paid {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z' fill='white'/%3E%3C/svg%3E");
}

.status-icon.unpaid {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z' fill='white'/%3E%3C/svg%3E");
}

.status-icon.overdue {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z' fill='white'/%3E%3C/svg%3E");
}

.status-text {
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
}

.status-desc {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 详情卡片 */
.detail-card {
  margin: 0 30rpx 30rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  position: relative;
  padding-left: 20rpx;
}

.card-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background-color: #ff8c00;
  border-radius: 4rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  max-width: 60%;
  word-break: break-all;
}

.info-value.highlight {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff8c00;
}

.info-value.overdue {
  color: #f44336;
}

.transaction-id {
  font-size: 24rpx;
}

/* 卡片头部带操作 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.card-action {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
}

.action-text {
  margin-right: 8rpx;
}

.action-icon {
  width: 24rpx;
  height: 24rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.action-icon.down {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7 10l5 5 5-5H7z' fill='%23666'/%3E%3C/svg%3E");
}

.action-icon.up {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7 14l5-5 5 5H7z' fill='%23666'/%3E%3C/svg%3E");
}

/* 账单明细 */
.bill-details {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.bill-details.expanded {
  max-height: 2000rpx;
}

.bill-table {
  width: 100%;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
  font-size: 24rpx;
}

.bill-table-header {
  display: flex;
  background-color: #f5f5f5;
  font-weight: 500;
}

.bill-table-row {
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
}

.bill-table-row:last-child {
  border-bottom: none;
}

.bill-cell {
  flex: 1;
  padding: 16rpx 8rpx;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 房屋信息 */
.property-info {
  margin-bottom: 20rpx;
  padding: 16rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
}

.property-info-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.property-info-content {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 服务周期 */
.service-period {
  margin-top: 20rpx;
  padding: 16rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
}

.service-period-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.service-period-content {
  font-size: 24rpx;
  color: #666;
}

/* 底部操作按钮 */
.action-buttons {
  display: flex;
  justify-content: space-between;
  padding: 0 30rpx;
  margin-bottom: 40rpx;
  flex-wrap: nowrap;
  width: 100%;
}

.action-button {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 44rpx;
  font-size: 24rpx;
  margin: 0 8rpx;
  padding: 0 8rpx;
  border: none;
  white-space: nowrap;
  min-width: 0;
}

.action-button.invoice {
  background-color: #f2f2f7;
  color: #333;
}

.action-button.invoice.disabled {
  opacity: 0.5;
}

.action-button.share {
  background-color: #ff8c00;
  color: white;
}

.action-button.settings {
  background-color: #f2f2f7;
  color: #333;
}

.action-button.analysis {
  background-color: #f2f2f7;
  color: #333;
}

.button-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  flex-shrink: 0;
}

.invoice-icon {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V8l8 5 8-5v10zm-8-7L4 6h16l-8 5z' fill='%23333'/%3E%3C/svg%3E");
}

.share-icon {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z' fill='white'/%3E%3C/svg%3E");
}

.settings-icon {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z' stroke='%23333333' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.258 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.0113 9.77251C4.28059 9.5799 4.48572 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z' stroke='%23333333' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.analysis-icon {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M21 21H3V3' stroke='%23333333' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M21 9L13 17L9 13L3 19' stroke='%23333333' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

/* 分享选项弹窗 */
.share-options-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.share-options-modal.show {
  opacity: 1;
  visibility: visible;
}

.share-options-content {
  width: 100%;
  background-color: white;
  border-radius: 24rpx 24rpx 0 0;
  padding: 40rpx 30rpx;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.share-options-modal.show .share-options-content {
  transform: translateY(0);
}

.share-options-title {
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  margin-bottom: 40rpx;
}

.share-options-list {
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;
}

.share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.share-option-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-bottom: 16rpx;
  background-size: 60%;
  background-repeat: no-repeat;
  background-position: center;
}

.share-option-icon.wechat {
  background-color: #07c160;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9.5 8.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5-.67 1.5-1.5 1.5-1.5-.67-1.5-1.5zm6.5 1.5c.83 0 1.5-.67 1.5-1.5S16.83 7 16 7s-1.5.67-1.5 1.5.67 1.5 1.5 1.5zm1.5 3c0 .83-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5.67-1.5 1.5-1.5 1.5.67 1.5 1.5zm-9 0c0 .83-.67 1.5-1.5 1.5S5 13.83 5 13s.67-1.5 1.5-1.5 1.5.67 1.5 1.5z' fill='white'/%3E%3C/svg%3E");
}

.share-option-icon.screenshot {
  background-color: #ff8c00;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4 4h16v16H4V4zm4 4h8v8H8V8z' fill='white'/%3E%3C/svg%3E");
}

.share-option-name {
  font-size: 28rpx;
  color: #333;
}

.share-cancel-button {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  background-color: #f2f2f7;
  color: #333;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

/* 暗黑模式 */
.darkMode {
  background-color: #1c1c1e;
  color: #fff;
}

.darkMode .detail-card,
.darkMode .share-options-content {
  background-color: #2c2c2e;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.darkMode .card-title,
.darkMode .info-value,
.darkMode .share-options-title,
.darkMode .share-option-name {
  color: #fff;
}

.darkMode .info-label,
.darkMode .action-text,
.darkMode .property-info-content,
.darkMode .service-period-content {
  color: #8e8e93;
}

.darkMode .bill-table-header {
  background-color: #3a3a3c;
}

.darkMode .bill-table-row {
  border-bottom-color: #3a3a3c;
}

.darkMode .property-info,
.darkMode .service-period {
  background-color: #3a3a3c;
}

.darkMode .action-button.invoice {
  background-color: #3a3a3c;
  color: #fff;
}

.darkMode .invoice-icon {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V8l8 5 8-5v10zm-8-7L4 6h16l-8 5z' fill='white'/%3E%3C/svg%3E");
}

.darkMode .analysis-icon {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M21 21H3V3' stroke='white' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M21 9L13 17L9 13L3 19' stroke='white' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.darkMode .share-cancel-button {
  background-color: #3a3a3c;
  color: #fff;
}
