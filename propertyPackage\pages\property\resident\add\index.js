// pages/property/resident/add/index.js
const util = require('../../../../../utils/util.js')

Page({
  data: {
    formData: {
      name: '',
      phone: '',
      idNumber: '',
      gender: '男',
      birthDate: '',
      email: '',
      type: 'owner', // owner, tenant, family
      emergencyContact: '',
      emergencyPhone: '',
      house: '',
      houseId: ''
    },
    typeOptions: [
      { id: 'owner', name: '业主' },
      { id: 'tenant', name: '租户' },
      { id: 'family', name: '家属' }
    ],
    showHouseSelector: false, // 是否显示房屋选择器
    houses: [], // 可选房屋列表
    submitting: false, // 是否正在提交
    errors: {} // 表单错误信息
  },

  onLoad: function(options) {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '添加居民'
    });

    // 如果有传入的房屋ID，则预先填充
    if (options.houseId) {
      this.loadHouseInfo(options.houseId);
    }

    // 加载可选房屋列表
    this.loadHouses();
  },

  // 加载房屋信息
  loadHouseInfo: function(houseId) {
    // 模拟加载数据
    setTimeout(() => {
      const house = {
        id: houseId,
        address: '3栋2单元502室'
      };

      const formData = this.data.formData;
      formData.houseId = house.id;
      formData.house = house.address;

      this.setData({
        formData: formData
      });
    }, 500);
  },

  // 加载可选房屋列表
  loadHouses: function() {
    // 模拟加载数据
    setTimeout(() => {
      const houses = [
        { id: 1, address: '1栋1单元101室' },
        { id: 2, address: '1栋1单元102室' },
        { id: 3, address: '1栋1单元103室' },
        { id: 4, address: '1栋2单元201室' },
        { id: 5, address: '1栋2单元202室' },
        { id: 6, address: '2栋1单元101室' },
        { id: 7, address: '2栋1单元102室' },
        { id: 8, address: '2栋2单元201室' },
        { id: 9, address: '3栋1单元101室' },
        { id: 10, address: '3栋2单元201室' }
      ];

      this.setData({
        houses: houses
      });
    }, 500);
  },

  // 输入表单数据
  onInput: function(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    const formData = this.data.formData;

    // 更新对应字段
    formData[field] = value;

    // 清除该字段的错误信息
    const errors = this.data.errors;
    if (errors[field]) {
      delete errors[field];
    }

    this.setData({
      formData: formData,
      errors: errors
    });
  },

  // 选择性别
  onGenderChange: function(e) {
    const formData = this.data.formData;
    formData.gender = e.detail.value;

    this.setData({
      formData: formData
    });
  },

  // 选择出生日期
  onBirthDateChange: function(e) {
    const formData = this.data.formData;
    formData.birthDate = e.detail.value;

    // 清除该字段的错误信息
    const errors = this.data.errors;
    if (errors.birthDate) {
      delete errors.birthDate;
    }

    this.setData({
      formData: formData,
      errors: errors
    });
  },

  // 选择居民类型
  onTypeChange: function(e) {
    const formData = this.data.formData;
    const index = e.detail.value;
    const typeOptions = this.data.typeOptions;

    if (index >= 0 && index < typeOptions.length) {
      formData.type = typeOptions[index].id;

      this.setData({
        formData: formData
      });
    }
  },

  // 显示房屋选择器
  showHouseSelector: function() {
    this.setData({
      showHouseSelector: true
    });
  },

  // 隐藏房屋选择器
  hideHouseSelector: function() {
    this.setData({
      showHouseSelector: false
    });
  },

  // 选择房屋
  selectHouse: function(e) {
    const { id, address } = e.currentTarget.dataset;
    const formData = this.data.formData;

    formData.houseId = id;
    formData.house = address;

    // 清除该字段的错误信息
    const errors = this.data.errors;
    if (errors.house) {
      delete errors.house;
    }

    this.setData({
      formData: formData,
      errors: errors,
      showHouseSelector: false
    });
  },

  // 验证表单
  validateForm: function() {
    const formData = this.data.formData;
    const errors = {};

    // 验证姓名
    if (!formData.name.trim()) {
      errors.name = '请输入姓名';
    }

    // 验证手机号
    if (!formData.phone.trim()) {
      errors.phone = '请输入手机号';
    } else if (!/^1\d{10}$/.test(formData.phone)) {
      errors.phone = '手机号格式不正确';
    }

    // 验证身份证号
    if (!formData.idNumber.trim()) {
      errors.idNumber = '请输入身份证号';
    } else if (!/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(formData.idNumber)) {
      errors.idNumber = '身份证号格式不正确';
    }

    // 验证出生日期
    if (!formData.birthDate) {
      errors.birthDate = '请选择出生日期';
    }

    // 验证邮箱（可选）
    if (formData.email && !/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(formData.email)) {
      errors.email = '邮箱格式不正确';
    }

    // 验证紧急联系人电话（可选）
    if (formData.emergencyPhone && !/^1\d{10}$/.test(formData.emergencyPhone)) {
      errors.emergencyPhone = '紧急联系人电话格式不正确';
    }

    // 验证房屋
    if (!formData.house) {
      errors.house = '请选择关联房屋';
    }

    this.setData({
      errors: errors
    });

    return Object.keys(errors).length === 0;
  },

  // 提交表单
  submitForm: function() {
    // 验证表单
    if (!this.validateForm()) {
      wx.showToast({
        title: '请完善表单信息',
        icon: 'none'
      });
      return;
    }

    this.setData({
      submitting: true
    });

    // 模拟提交
    setTimeout(() => {
      this.setData({
        submitting: false
      });

      wx.showToast({
        title: '添加成功',
        icon: 'success'
      });

      // 延迟返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }, 1000);
  },

  // 取消添加
  cancelAdd: function() {
    wx.navigateBack();
  },

  // 阻止滑动穿透
  preventTouchMove: function() {
    return false;
  }
})