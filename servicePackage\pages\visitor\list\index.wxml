<view class="visitor-container">


  <!-- 搜索栏 -->
  <view class="visitor-search-bar">
    <view class="visitor-search-input-container">
      <image src="/images/icons/search.svg" class="visitor-search-icon" />
      <input type="text" class="visitor-search-input" placeholder="搜索访客姓名/手机号"
             value="{{searchKeyword}}" bindinput="onSearchInput" confirm-type="search" />
      <view class="visitor-clear-btn" bindtap="clearSearch" wx:if="{{searchKeyword}}">
        <image src="/images/icons/clear.svg" class="visitor-clear-icon" />
      </view>
    </view>
  </view>

  <!-- 筛选标签栏 -->
  <scroll-view scroll-x class="visitor-filter-tabs" enhanced show-scrollbar="{{false}}">
    <view class="visitor-filter-tab {{currentFilter === item.nameEn ? 'active' : ''}}"
          wx:for="{{filterTabs}}" wx:key="nameEn"
          bindtap="setFilter" data-filter="{{item.nameEn}}">{{item.nameCn}}</view>
  </scroll-view>

  <!-- 访客列表 -->
  <scroll-view scroll-y class="visitor-list-container" bindscrolltolower="loadMoreVisitors"
               scroll-into-view="{{scrollIntoView}}" scroll-with-animation>
    <!-- 空态页面 -->
    <view class="visitor-empty-state" wx:if="{{visitors.length === 0}}">
      <image src="/images/empty-visitor.svg" class="visitor-empty-image" />
      <text class="visitor-empty-text">{{searchKeyword ? '未找到相关访客' : '暂无访客记录'}}</text>
      <text class="visitor-empty-subtext" wx:if="{{!searchKeyword}}">点击右下角按钮添加访客</text>
    </view>

    <!-- 访客列表内容 -->
    <block wx:else>
      <!-- 今天 -->
      <view id="group-today" class="visitor-group" wx:if="{{visitorGroups.today.length > 0}}">
        <view class="visitor-group-header">
          <text class="visitor-group-title">今天</text>
          <text class="visitor-group-count">{{visitorGroups.today.length}}位访客</text>
        </view>
        <view class="visitor-items">
          <view class="visitor-item" wx:for="{{visitorGroups.today}}" wx:key="id" bindtap="viewVisitorDetail" data-id="{{item.id}}">
            <view class="visitor-avatar {{item.status}}">{{item.visitorName[0]}}</view>
            <view class="visitor-info">
              <view class="visitor-name-row">
                <text class="visitor-name">{{item.visitorName}}</text>
                <view class="visitor-tag {{item.status}}">{{statusTextMap[item.status]}}</view>
              </view>
              <view class="visitor-time">访问时间：{{item.visitTime}}</view>
              <view class="visitor-end-time">结束时间：{{item.endTime}}</view>
              <view class="visitor-note" wx:if="{{item.note}}">{{item.note}}</view>
              <view class="visitor-vehicle" wx:if="{{item.vehicleNumber}}">车牌：{{item.vehicleNumber}}</view>
              <view class="visitor-actions">
                <view class="visitor-action-btn" catchtap="viewCredential" data-id="{{item.id}}">
                  <image src="/images/icons/qrcode.svg" class="visitor-action-icon" />
                  <text>查看凭证</text>
                </view>
                <view class="visitor-action-btn" catchtap="shareVisitor" data-id="{{item.id}}">
                  <image src="/images/icons/share.svg" class="visitor-action-icon" />
                  <text>分享</text>
                </view>
                <view class="visitor-action-btn" catchtap="showExtendOptions" data-id="{{item.id}}" wx:if="{{item.status === 'wait_visit'}}">
                  <image src="/images/icons/time.svg" class="visitor-action-icon" />
                  <text>延期</text>
                </view>
                <view class="visitor-action-btn" catchtap="saveAsFrequent" data-id="{{item.id}}">
                  <image src="/images/icons/star.svg" class="visitor-action-icon" />
                  <text>设为常用</text>
                </view>
                <view class="visitor-action-btn" catchtap="deleteVisitor" data-id="{{item.id}}">
                  <image src="/images/icons/delete.svg" class="visitor-action-icon" />
                  <text>删除</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 明天 -->
      <view id="group-tomorrow" class="visitor-group" wx:if="{{visitorGroups.tomorrow.length > 0}}">
        <view class="visitor-group-header">
          <text class="visitor-group-title">明天</text>
          <text class="visitor-group-count">{{visitorGroups.tomorrow.length}}位访客</text>
        </view>
        <view class="visitor-items">
          <view class="visitor-item" wx:for="{{visitorGroups.tomorrow}}" wx:key="id" bindtap="viewVisitorDetail" data-id="{{item.id}}">
            <!-- 访客项内容与今天相同 -->
            <view class="visitor-avatar {{item.status}}">{{item.visitorName[0]}}</view>
            <view class="visitor-info">
              <view class="visitor-name-row">
                <text class="visitor-name">{{item.visitorName}}</text>
                <view class="visitor-tag {{item.status}}">{{statusTextMap[item.status]}}</view>
              </view>
              <view class="visitor-time">访问时间：{{item.visitTime}}</view>
              <view class="visitor-end-time">结束时间：{{item.endTime}}</view>
              <view class="visitor-note" wx:if="{{item.note}}">{{item.note}}</view>
              <view class="visitor-vehicle" wx:if="{{item.vehicleNumber}}">车牌：{{item.vehicleNumber}}</view>
              <view class="visitor-actions">
                <view class="visitor-action-btn" catchtap="viewCredential" data-id="{{item.id}}">
                  <image src="/images/icons/qrcode.svg" class="visitor-action-icon" />
                  <text>查看凭证</text>
                </view>
                <view class="visitor-action-btn" catchtap="shareVisitor" data-id="{{item.id}}">
                  <image src="/images/icons/share.svg" class="visitor-action-icon" />
                  <text>分享</text>
                </view>
                <view class="visitor-action-btn" catchtap="showExtendOptions" data-id="{{item.id}}" wx:if="{{item.status === 'wait_visit'}}">
                  <image src="/images/icons/time.svg" class="visitor-action-icon" />
                  <text>延期</text>
                </view>
                <view class="visitor-action-btn" catchtap="saveAsFrequent" data-id="{{item.id}}">
                  <image src="/images/icons/star.svg" class="visitor-action-icon" />
                  <text>设为常用</text>
                </view>
                <view class="visitor-action-btn" catchtap="deleteVisitor" data-id="{{item.id}}">
                  <image src="/images/icons/delete.svg" class="visitor-action-icon" />
                  <text>删除</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 最近7天 -->
      <view id="group-week" class="visitor-group" wx:if="{{visitorGroups.week.length > 0}}">
        <view class="visitor-group-header">
          <text class="visitor-group-title">最近7天</text>
          <text class="visitor-group-count">{{visitorGroups.week.length}}位访客</text>
        </view>
        <view class="visitor-items">
          <view class="visitor-item" wx:for="{{visitorGroups.week}}" wx:key="id" bindtap="viewVisitorDetail" data-id="{{item.id}}">
            <!-- 访客项内容与今天相同 -->
            <view class="visitor-avatar {{item.status}}">{{item.visitorName[0]}}</view>
            <view class="visitor-info">
              <view class="visitor-name-row">
                <text class="visitor-name">{{item.visitorName}}</text>
                <view class="visitor-tag {{item.status}}">{{statusTextMap[item.status]}}</view>
              </view>
              <view class="visitor-time">访问时间：{{item.visitTime}}</view>
              <view class="visitor-end-time">结束时间：{{item.endTime}}</view>
              <view class="visitor-note" wx:if="{{item.note}}">{{item.note}}</view>
              <view class="visitor-vehicle" wx:if="{{item.vehicleNumber}}">车牌：{{item.vehicleNumber}}</view>
              <view class="visitor-actions">
                <view class="visitor-action-btn" catchtap="viewCredential" data-id="{{item.id}}">
                  <image src="/images/icons/qrcode.svg" class="visitor-action-icon" />
                  <text>查看凭证</text>
                </view>
                <view class="visitor-action-btn" catchtap="shareVisitor" data-id="{{item.id}}">
                  <image src="/images/icons/share.svg" class="visitor-action-icon" />
                  <text>分享</text>
                </view>
                <view class="visitor-action-btn" catchtap="showExtendOptions" data-id="{{item.id}}" wx:if="{{item.status === 'wait_visit'}}">
                  <image src="/images/icons/time.svg" class="visitor-action-icon" />
                  <text>延期</text>
                </view>
                <view class="visitor-action-btn" catchtap="saveAsFrequent" data-id="{{item.id}}">
                  <image src="/images/icons/star.svg" class="visitor-action-icon" />
                  <text>设为常用</text>
                </view>
                <view class="visitor-action-btn" catchtap="deleteVisitor" data-id="{{item.id}}">
                  <image src="/images/icons/delete.svg" class="visitor-action-icon" />
                  <text>删除</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 更早 -->
      <view id="group-earlier" class="visitor-group" wx:if="{{visitorGroups.earlier.length > 0}}">
        <view class="visitor-group-header">
          <text class="visitor-group-title">更早</text>
          <text class="visitor-group-count">{{visitorGroups.earlier.length}}位访客</text>
        </view>
        <view class="visitor-items">
          <view class="visitor-item" wx:for="{{visitorGroups.earlier}}" wx:key="id" bindtap="viewVisitorDetail" data-id="{{item.id}}">
            <!-- 访客项内容与今天相同 -->
            <view class="visitor-avatar {{item.status}}">{{item.visitorName[0]}}</view>
            <view class="visitor-info">
              <view class="visitor-name-row">
                <text class="visitor-name">{{item.visitorName}}</text>
                <view class="visitor-tag {{item.status}}">{{statusTextMap[item.status]}}</view>
              </view>
              <view class="visitor-time">访问时间：{{item.visitTime}}</view>
              <view class="visitor-end-time">结束时间：{{item.endTime}}</view>
              <view class="visitor-note" wx:if="{{item.note}}">{{item.note}}</view>
              <view class="visitor-vehicle" wx:if="{{item.vehicleNumber}}">车牌：{{item.vehicleNumber}}</view>
              <view class="visitor-actions">
                <view class="visitor-action-btn" catchtap="viewCredential" data-id="{{item.id}}">
                  <image src="/images/icons/qrcode.svg" class="visitor-action-icon" />
                  <text>查看凭证</text>
                </view>
                <view class="visitor-action-btn" catchtap="shareVisitor" data-id="{{item.id}}">
                  <image src="/images/icons/share.svg" class="visitor-action-icon" />
                  <text>分享</text>
                </view>
                <view class="visitor-action-btn" catchtap="showExtendOptions" data-id="{{item.id}}" wx:if="{{item.status === 'wait_visit'}}">
                  <image src="/images/icons/time.svg" class="visitor-action-icon" />
                  <text>延期</text>
                </view>
                <view class="visitor-action-btn" catchtap="saveAsFrequent" data-id="{{item.id}}">
                  <image src="/images/icons/star.svg" class="visitor-action-icon" />
                  <text>设为常用</text>
                </view>
                <view class="visitor-action-btn" catchtap="deleteVisitor" data-id="{{item.id}}">
                  <image src="/images/icons/delete.svg" class="visitor-action-icon" />
                  <text>删除</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="visitor-load-more" wx:if="{{hasMoreVisitors}}">
        <view class="visitor-loading-indicator"></view>
        <text>加载更多...</text>
      </view>

      <!-- 已加载全部 -->
      <view class="visitor-load-all" wx:elif="{{visitors.length > 0}}">
        <text>已加载全部访客</text>
      </view>
    </block>
  </scroll-view>

  <!-- 悬浮添加按钮 -->
  <view class="visitor-add-btn" bindtap="navigateToRegistration">
    <image src="/images/icons/add.svg" class="visitor-add-icon" />
  </view>
</view>

<!-- 延期选项弹窗 -->
<view class="visitor-extend-modal {{showExtendModal ? 'show' : ''}}">
  <view class="visitor-modal-mask" bindtap="hideExtendModal"></view>
  <view class="visitor-modal-content">
    <view class="visitor-modal-header">
      <text class="visitor-modal-title">调整访客时间</text>
      <view class="visitor-modal-close" bindtap="hideExtendModal">
        <image src="/images/icons/close.svg" class="visitor-close-icon" />
      </view>
    </view>
    <view class="visitor-modal-body">
      <view class="visitor-extend-option" bindtap="extendVisitor" data-hours="1">
        <text>延长1小时</text>
      </view>
      <view class="visitor-extend-option" bindtap="extendVisitor" data-hours="2">
        <text>延长2小时</text>
      </view>
      <view class="visitor-extend-option" bindtap="extendVisitor" data-hours="4">
        <text>延长4小时</text>
      </view>

    </view>
  </view>
</view>
