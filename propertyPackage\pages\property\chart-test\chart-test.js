// 引入图表库
const wxCharts = require('../../../../utils/wxcharts/wxcharts.js');

// 页面实例
Page({
  data: {
    windowWidth: 320
  },

  // 页面加载时
  onLoad: function() {
    // 获取设备信息
    try {
      const res = wx.getSystemInfoSync();
      this.setData({
        windowWidth: res.windowWidth
      });
    } catch (e) {
      console.error('获取设备信息失败:', e);
    }
  },

  // 页面显示时
  onShow: function() {
    // 初始化图表
    this.initCharts();
  },

  // 初始化所有图表
  initCharts: function() {
    // 初始化饼图
    this.initPieChart();

    // 初始化柱状图
    this.initColumnChart();

    // 初始化折线图
    this.initLineChart();
  },

  // 初始化饼图
  initPieChart: function() {
    try {
      // 饼图数据
      const pieData = [
        { name: '业主', data: 60 },
        { name: '租户', data: 30 },
        { name: '家属', data: 10 }
      ];

      // 创建饼图
      const ctx = wx.createCanvasContext('pieCanvas');
      if (!ctx) {
        console.error('无法获取饼图Canvas上下文');
        return;
      }

      wxCharts.create({
        type: 'pie',
        canvas: ctx,
        series: pieData,
        width: this.data.windowWidth - 40,
        height: 200,
        padding: 10,
        colors: ['#7cb5ec', '#f7a35c', '#434348'],
        showLegend: true
      });

      // 绘制到画布
      ctx.draw();
    } catch (e) {
      console.error('初始化饼图失败:', e);
    }
  },

  // 初始化柱状图
  initColumnChart: function() {
    try {
      // 柱状图数据
      const columnData = [
        { name: '待处理', data: [15] },
        { name: '处理中', data: [12] },
        { name: '已完成', data: [98] }
      ];

      // 创建柱状图
      const ctx = wx.createCanvasContext('columnCanvas');
      if (!ctx) {
        console.error('无法获取柱状图Canvas上下文');
        return;
      }

      wxCharts.create({
        type: 'column',
        canvas: ctx,
        series: columnData,
        categories: ['工单状态'],
        width: this.data.windowWidth - 40,
        height: 200,
        padding: 10,
        colors: ['#f59e0b', '#3b82f6', '#10b981'],
        showLegend: true
      });

      // 绘制到画布
      ctx.draw();
    } catch (e) {
      console.error('初始化柱状图失败:', e);
    }
  },

  // 初始化折线图
  initLineChart: function() {
    try {
      // 折线图数据
      const lineData = [
        { name: '访客数量', data: [10, 15, 20, 25, 30, 25, 20] }
      ];

      // 创建折线图
      const ctx = wx.createCanvasContext('lineCanvas');
      if (!ctx) {
        console.error('无法获取折线图Canvas上下文');
        return;
      }

      wxCharts.create({
        type: 'line',
        canvas: ctx,
        series: lineData,
        categories: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        width: this.data.windowWidth - 40,
        height: 200,
        padding: 10,
        colors: ['#4f46e5'],
        showLegend: true
      });

      // 绘制到画布
      ctx.draw();
    } catch (e) {
      console.error('初始化折线图失败:', e);
    }
  }
});
