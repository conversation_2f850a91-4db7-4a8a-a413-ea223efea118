// pages/property/workorder/workorder.js
const util = require('@/utils/util.js');
const workOrderManager = require('@/utils/workorder-manager.js');

/**
 * 将日期字符串转换为iOS兼容格式
 * @param {string} dateStr 日期字符串，如 "2023-05-01 09:30"
 * @returns {Date} 日期对象
 */
function parseDate(dateStr) {
  if (!dateStr) return new Date();

  // 检查是否是 "yyyy-MM-dd HH:mm" 格式
  if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/.test(dateStr)) {
    // 转换为 "yyyy-MM-ddTHH:mm:00" 格式，iOS兼容
    return new Date(dateStr.replace(' ', 'T') + ':00');
  }

  return new Date(dateStr);
}

Page({
  data: {
    darkMode: false,
    statusCounts: {
      total: 0,
      pending: 0,
      processing: 0,
      completed: 0,
      cancelled: 0
    },
    recentOrders: [],
    loading: true
  },

  onLoad: function() {
    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '工单管理'
    });

    // 初始化模拟数据（仅用于开发测试）
    workOrderManager.initMockData();

    // 加载工单数据
    this.loadWorkOrderData();
  },

  onShow: function() {
    // 页面显示时刷新数据
    this.loadWorkOrderData();
  },

  // 加载工单数据
  loadWorkOrderData: function() {
    this.setData({ loading: true });

    // 获取工单数据
    workOrderManager.getWorkOrders()
      .then(orders => {
        // 计算各状态工单数量
        const statusCounts = this.calculateStatusCounts(orders);

        // 获取最近的5个工单
        const recentOrders = this.getRecentOrders(orders, 5);

        this.setData({
          statusCounts: statusCounts,
          recentOrders: recentOrders,
          loading: false
        });
      })
      .catch(error => {
        console.error('加载工单数据失败', error);
        this.setData({ loading: false });

        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },

  // 计算各状态工单数量
  calculateStatusCounts: function(orders) {
    const counts = {
      total: orders.length,
      pending: 0,
      processing: 0,
      completed: 0,
      cancelled: 0
    };

    orders.forEach(order => {
      if (counts[order.status] !== undefined) {
        counts[order.status]++;
      }
    });

    return counts;
  },

  // 获取最近的工单
  getRecentOrders: function(orders, count) {
    // 按创建时间排序，最新的在前
    const sortedOrders = [...orders].sort((a, b) => {
      return parseDate(b.createTime) - parseDate(a.createTime);
    });

    // 返回前count个
    return sortedOrders.slice(0, count);
  },

  // 导航到工单列表
  navigateToOrderList: function(e) {
    const status = e.currentTarget.dataset.status || '';
    wx.navigateTo({
      url: `/servicePackage/pages/workorder/list/index?status=${status}`
    });
  },

  // 导航到工单统计
  navigateToOrderStats: function() {
    wx.navigateTo({
      url: '/propertyPackage/pages/property/workorder/stats/index'
    });
  },

  // 导航到创建工单
  navigateToCreateOrder: function() {
    wx.navigateTo({
      url: '/servicePackage/pages/repair/repair?from=property'
    });
  },

  // 导航到工单详情
  navigateToOrderDetail: function(e) {
    const orderId = e.currentTarget.dataset.id;
  
    wx.navigateTo({
      url: `/servicePackage/pages/workorder/detail/index?id=${orderId}`
    });
  }
});
