/* profile.wxss */
page {
  background-color: #F2F2F7;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
  color: #333;
  line-height: 1.5;
  padding-bottom: 100rpx;
}

.container {
  padding: 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
}

/* 个人信息卡片 */
.profile-section {
  background: linear-gradient(135deg, #FF9500, #FF7800);
  color: white;
  border-radius: 32rpx;
  margin-bottom: 40rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 24rpx rgba(255, 149, 0, 0.15);
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 30rpx;
  margin-bottom: 30rpx;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  position: relative;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.8);
  background: #fff;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: #FF9500;
  position: relative;
}

.avatar image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

/* 头像右侧的认证标识 */
.auth-badge {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 22rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-weight: 500;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  display: inline-flex;
  align-items: center;
  gap: 6rpx;
  position: relative;
}

.auth-badge.verified {
  background-color: rgba(52, 199, 89, 0.2);
  border: 1rpx solid rgba(52, 199, 89, 0.3);
  padding-right: 40rpx; /* 为编辑按钮留出空间 */
}

.auth-badge-icon::before {
  content: '●';
  font-size: 16rpx;
}

/* 认证状态编辑按钮 */
.auth-edit-btn {
  position: absolute;
  right: 8rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 24rpx;
  height: 24rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7'/%3E%3Cpath d='m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  cursor: pointer;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.auth-edit-btn:hover {
  opacity: 1;
}

.complete-info-btn {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  padding: 8rpx 20rpx;
  font-size: 24rpx;
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;
}

.complete-info-btn:active {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.user-info {
  flex: 1;
}

.user-name-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  flex: 1;
}

/* 认证状态标签 */
.auth-status-tag {
  display: inline-flex;
  align-items: center;
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  font-weight: 500;
  margin-right: 16rpx;
  color: white;
  background-color: rgba(255, 255, 255, 0.2); /* 半透明白色（未认证） */
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.auth-status-tag.resident {
  background-color: rgba(52, 199, 89, 0.2); /* 半透明绿色（住户认证） */
  border: 1rpx solid rgba(52, 199, 89, 0.3);
}

.auth-status-tag.property {
  background-color: rgba(255, 255, 255, 0.25); /* 半透明白色（物业认证） */
  border: 1rpx solid rgba(255, 255, 255, 0.4);
}

.auth-status-tag.dual {
  background-color: rgba(255, 255, 255, 0.3); /* 半透明白色（双重认证） */
  border: 1rpx solid rgba(255, 255, 255, 0.5);
}

.user-points {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 30rpx;
  padding: 6rpx 24rpx;
  font-size: 26rpx;
  color: #fff;
  font-weight: 500;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.points-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-location {
  font-size: 28rpx;
  opacity: 0.9;
  display: flex;
  align-items: center;
}

/* 功能组 */
.group-title {
  font-size: 32rpx;
  font-weight: 600;
  margin: 40rpx 0 20rpx;
  color: #333;
  padding-left: 10rpx;
}

.function-group {
  background: white;
  border-radius: 32rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.03);
}

.function-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.function-item:last-child {
  border-bottom: none;
}

.function-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.function-title {
  flex: 1;
  font-size: 32rpx;
  color: #333;
}

.function-arrow {
  color: #ccc;
  font-size: 32rpx;
}

.function-badge {
  background: #ff8c00;
  color: white;
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
}

/* 不同认证状态的角标样式 */
.auth-badge {
  background-color: #FF9500;
  color: white;
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
  font-weight: 500;
  display: inline-block;
  min-width: 120rpx;
  text-align: center;
}

.auth-badge.verified {
  background-color: #34C759;
}

.auth-badge.property {
  background-color: #007AFF;
}

.auth-badge.dual {
  background-color: #5856D6;
}

/* 图标样式 */
/* 图标样式已移至 icons.wxss */

/* 认证提醒弹窗样式 */
.auth-reminder-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.auth-reminder-modal.show {
  opacity: 1;
  visibility: visible;
}

.auth-reminder-modal-content {
  background-color: white;
  border-radius: 32rpx;
  width: 600rpx;
  max-width: 85%;
  padding: 50rpx 40rpx;
  box-shadow: 0 20rpx 50rpx rgba(0, 0, 0, 0.1);
  transform: translateY(40rpx);
  transition: transform 0.3s ease, opacity 0.3s ease;
  opacity: 0.8;
}

.auth-reminder-modal.show .auth-reminder-modal-content {
  transform: translateY(0);
  opacity: 1;
}

.auth-reminder-modal-icon {
  text-align: center;
  margin-bottom: 30rpx;
}

.auth-reminder-modal-icon-circle {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  background-color: rgba(255, 193, 7, 0.1);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.auth-reminder-modal-header {
  text-align: center;
  margin-bottom: 40rpx;
  position: relative;
}

.auth-reminder-modal-header:after {
  content: '';
  display: block;
  width: 80rpx;
  height: 2rpx;
  background-color: rgba(0, 0, 0, 0.1);
  margin: 30rpx auto 0;
}

.auth-reminder-modal-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  color: #333;
}

.auth-reminder-modal-subtitle {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}

.auth-reminder-modal-footer {
  display: flex;
  gap: 20rpx;
}

.auth-reminder-modal-btn {
  flex: 1;
  border: none;
  border-radius: 24rpx;
  padding: 24rpx 0;
  font-size: 32rpx;
  font-weight: 500;
  transition: all 0.2s ease;
}

.auth-reminder-modal-btn.cancel {
  background-color: rgba(60, 60, 67, 0.1);
  color: #007AFF;
}

.auth-reminder-modal-btn.confirm {
  background-color: #007AFF;
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.3);
}

.logout-btn {
  width: 100%;
  height: 88rpx;
  background: white;
  color: #f44336;
  font-size: 32rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 60rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}
