// 我的车辆页面逻辑
const app = getApp()
const vehicleApi = require('@/api/vehicleApi.js')

Page({
  data: {
    vehicles: [],
  },

  onLoad: function (options) {
    // 获取URL参数
    this.fromPage = options.from || '';
  },

  onShow: function () {

    // 加载车辆列表
    this.loadVehicles();

    // 更新底部tabbar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 3
      });
    }
  },

  // 加载车辆列表
  loadVehicles: function () {
    wx.showLoading({
      title: '加载中...',
      mask: true
    });
    
    var params={
      pageNum: 1,
      pageSize: 50,
      communityId: wx.getStorageSync('selectedCommunity').id
    }

    // 从API获取车辆数据
    vehicleApi.getVehicleList(params)
      .then(res => {
        console.log('获取车辆列表成功', res);

        // 处理车辆数据
        const vehicles = (res.data.list || []).map(vehicle => {
           
          return vehicleApi.formatVehicleData(vehicle);
        });

        this.setData({
          vehicles: vehicles
        });

        wx.setStorageSync('my_vehicles', vehicles)
        wx.hideLoading();
      })
      .catch(err => {
        console.log('获取车辆列表失败', err);
        wx.hideLoading();

        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none',
          duration: 2000
        });
      });
  },

 



  // 添加车辆
  addVehicle: function () {
    let url = '/profilePackage/pages/profile/vehicle/add/add';

    // 如果有来源页面，则传递参数
    if (this.fromPage) {
      url += `?from=${this.fromPage}`;
    }

    wx.navigateTo({
      url: url
    });
  },

  // 编辑车辆
  editVehicle: function (e) {
    const id = e.currentTarget.dataset.id;

    // 跳转到编辑页面
    let url = `/profilePackage/pages/profile/vehicle/add/add?mode=edit&vehicleId=${id}`;

    // 如果有来源页面，则传递参数
    if (this.fromPage) {
      url += `&from=${this.fromPage}`;
    }

    wx.navigateTo({
      url: url
    });
  },


  // 删除车辆
  deleteVehicle: function (e) {
    const id = e.currentTarget.dataset.id;

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这辆车吗？',
      success: res => {
        if (res.confirm) {
          wx.showLoading({
            title: '删除中...',
            mask: true
          });

          // 调用API删除车辆
          vehicleApi.deleteVehicle(id)
            .then(res => {
              console.log('删除车辆成功', res);
              wx.hideLoading();

              // 刷新车辆列表
              this.loadVehicles();

              wx.showToast({
                title: '已删除',
                icon: 'success'
              });
            })
            .catch(err => {
              console.log('删除车辆失败', err);
              wx.hideLoading();

             
            });
        }
      }
    });
  },


})
