// messages.js
const announcementApi = require('@/utils/announcement-api');
const commApi = require('@/api/commApi.js');
const noticeApi = require('@/api/notice.js');
const TabbarManager = require('../../utils/tabbar-manager.js');

Page({
  data: {
    currentTab: '',
    // 动态通知类型列表
    noticeTypes: [],
    // 通知类型加载状态
    typesLoading: true,
    // 是否显示滚动提示
    showScrollHint: false,
    // 未读数量
    unreadCounts: {},
    // 消息数据
    messages: {},
    // 消息加载状态
    messagesLoading: false,
    showMessageModal: false,
    currentMessage: {},
    currentMessageIcon: 'icon-property'
  },

  onLoad: function() {
    // 先加载通知类型，再加载消息数据
    this.loadNoticeTypes();
  },

  onShow: function() {
    // 每次显示页面时刷新数据
    if (this.data.noticeTypes.length > 0) {
      this.loadUnreadCounts(); // 刷新未读数量
      this.loadMessagesForCurrentTab();
    }

    // 设置底部 tabBar 选中状态
    TabbarManager.setTabbarSelected(this, 3)
  },

  // 加载通知类型
  loadNoticeTypes: function() {
    this.setData({ typesLoading: true });

    commApi.getDictByNameEn('notice_type').then(res => {
      console.log('通知类型数据：', res);

      if (res.code === 0 && res.data && res.data.length > 0) {
        // 处理通知类型数据
        const noticeTypes = res.data.map(item => ({
          id: item.id,
          nameEn: item.nameEn,
          nameCn: item.nameCn,
          sort: item.sort || 0
        }));

        // 按排序字段排序
        noticeTypes.sort((a, b) => (a.sort || 0) - (b.sort || 0));

        // 初始化消息数据和未读数量
        const messages = {};
        const unreadCounts = {};
        noticeTypes.forEach(type => {
          messages[type.nameEn] = [];
          unreadCounts[type.nameEn] = 0;
        });

        this.setData({
          noticeTypes: noticeTypes,
          messages: messages,
          unreadCounts: unreadCounts,
          currentTab: noticeTypes[0]?.nameEn || '',
          typesLoading: false,
          showScrollHint: noticeTypes.length > 3 // 超过3个显示滚动提示
        });

        // 加载未读消息数量
        this.loadUnreadCounts();

        // 加载第一个标签的消息数据
        if (noticeTypes.length > 0) {
          this.loadMessagesForCurrentTab();
        }
      } else {
        console.error('获取通知类型失败：', res);
        this.setData({ typesLoading: false });
      }
    }).catch(err => {
      console.error('获取通知类型异常：', err);
      this.setData({ typesLoading: false });
    });
  },

  // 加载未读消息数量
  loadUnreadCounts: function() {
    const selectedCommunity = wx.getStorageSync('selectedCommunity');
    if (!selectedCommunity || !selectedCommunity.id) {
      console.error('未选择社区，无法获取未读消息数量');
      return;
    }

    // 获取所有通知类型的nameEn值
    const types = this.data.noticeTypes.map(type => type.nameEn);

    const params = {
      pageNum: 1,
      pageSize: 50,
      types: types,
      communityId: selectedCommunity.id
    };

    console.log('获取未读消息数量参数：', params);

    noticeApi.getUnReadCount(params).then(res => {
      console.log('未读消息数量响应：', res);

      if (res.code === 0 && res.data) {
        // 更新未读数量
        const unreadCounts = { ...this.data.unreadCounts };

        // 遍历返回的数据，更新对应类型的未读数量
        Object.keys(res.data).forEach(type => {
          if (type !== 'total' && unreadCounts.hasOwnProperty(type)) {
            unreadCounts[type] = res.data[type] || 0;
          }
        });

        this.setData({
          unreadCounts: unreadCounts
        });

        // 计算总未读数量并更新 TabbarManager
        const totalUnread = Object.values(unreadCounts).reduce((sum, count) => sum + count, 0);
        TabbarManager.updateUnreadCount(totalUnread);

        console.log('更新后的未读数量：', unreadCounts);
        console.log('总未读数量：', totalUnread);
      }
    }).catch(err => {
      console.error('获取未读消息数量失败：', err);
    });
  },

  // 加载当前标签的消息数据
  loadMessagesForCurrentTab: function() {
    if (!this.data.currentTab) return;

    this.setData({ messagesLoading: true });

    // 根据当前标签类型加载对应的消息数据
    const currentType = this.data.currentTab;




    // 使用图文接口获取对应类型的消息
    noticeApi.getNoticePage({
      pageNum: 1,
      pageSize: 10,
      type: currentType,
      communityId:wx.getStorageSync('selectedCommunity').id
    }).then(res => {
      console.log(`${currentType} 消息数据：`, res);

      if (res.code === 0 && res.data && res.data.list) {



        const messageList = res.data.list.map(item => ({
          id: item.id,
          title: item.title || '无标题',
          content: item.content || '',
          time: this.formatMessageTime(item.createTime),
          read: item.read !== undefined ? item.read : false, // 使用API返回的read状态，如果没有则默认未读
          type: item.type
        }));

        // 更新对应类型的消息数据
        const messages = { ...this.data.messages };
        const unreadCounts = { ...this.data.unreadCounts };

        messages[currentType] = messageList;
        unreadCounts[currentType] = messageList.filter(item => !item.read).length;

        this.setData({
          messages: messages,
          unreadCounts: unreadCounts,
          messagesLoading: false
        });
      } else {
        console.log(`${currentType} 消息数据为空`);
        this.setData({ messagesLoading: false });
      }
    }).catch(err => {
      console.error(`获取 ${currentType} 消息失败：`, err);
      this.setData({ messagesLoading: false });

      // 如果是客服消息类型，使用默认数据
      if (currentType === 'customer_message') {
        const messages = { ...this.data.messages };
        const unreadCounts = { ...this.data.unreadCounts };

        messages.customer_message = [
          { id: 4, title: '您的报修申请已受理', time: '今天 10:15', read: false },
          { id: 5, title: '物业费缴纳成功', time: '09-15 16:30', read: true }
        ];
        unreadCounts.customer_message = 1;

        this.setData({
          messages: messages,
          unreadCounts: unreadCounts
        });
      }
    });
  },

  // 格式化消息时间
  formatMessageTime: function(timeString) {
    if (!timeString) return '刚刚';

    try {
      const messageTime = new Date(timeString);
      const now = new Date();
      const diffDays = Math.floor((now - messageTime) / (24 * 60 * 60 * 1000));

      if (diffDays === 0) {
        return '今天 ' + messageTime.getHours().toString().padStart(2, '0') + ':' +
               messageTime.getMinutes().toString().padStart(2, '0');
      } else if (diffDays === 1) {
        return '昨天 ' + messageTime.getHours().toString().padStart(2, '0') + ':' +
               messageTime.getMinutes().toString().padStart(2, '0');
      } else {
        return (messageTime.getMonth() + 1).toString().padStart(2, '0') + '-' +
               messageTime.getDate().toString().padStart(2, '0') + ' ' +
               messageTime.getHours().toString().padStart(2, '0') + ':' +
               messageTime.getMinutes().toString().padStart(2, '0');
      }
    } catch (error) {
      console.error('时间格式化失败：', error);
      return '刚刚';
    }
  },

  // 标签滚动提示点击
  onScrollHintTap: function() {
    // 触发标签容器滚动
    const query = wx.createSelectorQuery();
    query.select('.message-tabs-container').scrollTo({
      left: 200, // 向右滚动200rpx
      animated: true
    });
    query.exec();
  },

  // 加载公告数据（保留原有方法，用于兼容）
  loadAnnouncementData: function() {
    wx.showLoading({
      title: '加载中...'
    });

    // 调用API获取公告列表
    announcementApi.getAnnouncementList()
      .then(res => {
        const announcements = res.data.list || [];

        // 按类型分类公告
        const propertyNotices = [];
        const activityNotices = [];
        const emergencyNotices = [];

        // 处理公告数据
        announcements.forEach(item => {
          if (item.status !== 'published') return;

          // 格式化时间
          let timeText = '';
          if (typeof item.publishTime === 'string') {
            const publishTime = new Date(item.publishTime);
            const now = new Date();
            const diffDays = Math.floor((now - publishTime) / (24 * 60 * 60 * 1000));

            if (diffDays === 0) {
              timeText = '今天 ' + publishTime.getHours().toString().padStart(2, '0') + ':' +
                        publishTime.getMinutes().toString().padStart(2, '0');
            } else if (diffDays === 1) {
              timeText = '昨天 ' + publishTime.getHours().toString().padStart(2, '0') + ':' +
                        publishTime.getMinutes().toString().padStart(2, '0');
            } else {
              timeText = (publishTime.getMonth() + 1).toString().padStart(2, '0') + '-' +
                        publishTime.getDate().toString().padStart(2, '0') + ' ' +
                        publishTime.getHours().toString().padStart(2, '0') + ':' +
                        publishTime.getMinutes().toString().padStart(2, '0');
            }
          } else {
            timeText = '刚刚';
          }

          // 创建通知对象
          const notice = {
            id: item.id,
            title: item.title,
            time: timeText,
            read: false, // 默认未读
            content: item.content
          };

          // 根据类型分类
          switch (item.type) {
            case 'property_notice':
              propertyNotices.push(notice);
              break;
            case 'activity_notice':
              activityNotices.push(notice);
              break;
            case 'emergency_notice':
              emergencyNotices.push(notice);
              break;
          }
        });

        // 更新数据
        this.setData({
          'messages.property': propertyNotices,
          'messages.community': activityNotices,
          'messages.emergency': emergencyNotices,
          'unreadCounts.property': propertyNotices.filter(item => !item.read).length,
          'unreadCounts.community': activityNotices.filter(item => !item.read).length,
          'unreadCounts.emergency': emergencyNotices.filter(item => !item.read).length
        });

        wx.hideLoading();
      })
      .catch(err => {
        console.error('加载公告数据失败', err);
        wx.hideLoading();

        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },

  switchTab: function (e) {
    const tab = e.currentTarget.dataset.tab
    if (tab === this.data.currentTab) return; // 如果是当前标签，不需要切换

    this.setData({
      currentTab: tab
    })

    // 加载新标签的消息数据
    this.loadMessagesForCurrentTab();
  },

  showMessageDetail: function (e) {
    const message = e.currentTarget.dataset.message

    // 标记为已读
    if (!message.read) {
      this.markMessageAsRead(message)
    }

    // 根据消息类型设置图标
    let icon = 'icon-property'
    if (this.data.currentTab === 'service') {
      icon = 'icon-service'
    } else if (this.data.currentTab === 'community') {
      icon = 'icon-community'
    } else if (this.data.currentTab === 'emergency') {
      icon = 'icon-emergency'
    }

    // 生成消息内容
    let content = this.generateMessageContent(message)

    this.setData({
      showMessageModal: true,
      currentMessage: {
        ...message,
        content
      },
      currentMessageIcon: icon
    })
  },

  closeMessageModal: function () {
    this.setData({
      showMessageModal: false
    })

    noticeApi

  },

  stopPropagation: function (e) {
    // 阻止事件冒泡
  },

  markMessageAsRead: function (message) {
    // 更新消息已读状态
    const type = this.data.currentTab
    const messages = this.data.messages[type]
    const unreadCounts = this.data.unreadCounts

    for (let i = 0; i < messages.length; i++) {
      if (messages[i].id === message.id) {
        messages[i].read = true

        // 调用API设置已读状态
        noticeApi.setNoticeRead(messages[i].id).then(res => {
          console.log('设置已读成功');

          // API调用成功后，立即更新未读数量和角标
          const newUnreadCounts = { ...this.data.unreadCounts };
          newUnreadCounts[type] = Math.max(0, newUnreadCounts[type] - 1);

          this.setData({
            unreadCounts: newUnreadCounts
          });

          // 计算总未读数量并立即更新 TabbarManager
          const totalUnread = Object.values(newUnreadCounts).reduce((sum, count) => sum + count, 0);
          TabbarManager.updateUnreadCount(totalUnread);

          console.log('立即更新未读数量：', newUnreadCounts);
          console.log('立即更新总未读数量：', totalUnread);
        })
        .catch(err => {
          console.error('设置已读失败', err);
          // 如果API调用失败，恢复消息的未读状态
          messages[i].read = false;
          this.setData({
            [`messages.${type}`]: messages
          });
        });

        break
      }
    }

    // 立即更新本地UI状态
    const newUnreadCounts = { ...unreadCounts };
    newUnreadCounts[type] = Math.max(0, newUnreadCounts[type] - 1);

    this.setData({
      [`messages.${type}`]: messages,
      unreadCounts: newUnreadCounts
    });

    // 立即更新角标
    const totalUnread = Object.values(newUnreadCounts).reduce((sum, count) => sum + count, 0);
    TabbarManager.updateUnreadCount(totalUnread);



  },

  generateMessageContent: function (message) {
    // 如果消息有内容，直接使用
    if (message.content) {
      // 如果内容是HTML格式，去除HTML标签
      if (message.content.includes('<')) {
        return message.content.replace(/<[^>]+>/g, '');
      }
      return message.content;
    }


  },


  onPullDownRefresh: function() {
    // 下拉刷新
    this.loadMessagesForCurrentTab()
  },


})
