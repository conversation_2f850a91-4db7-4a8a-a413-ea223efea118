<!--员工编辑页面-->
<view class="container {{darkMode ? 'darkMode' : ''}}">
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
  
  <!-- 表单内容 -->
  <block wx:if="{{!isLoading}}">
    <view class="form-container">
      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-title">
          <view class="section-icon-basic"></view>
          <text>基本信息</text>
        </view>
        
        <view class="form-group {{nameError ? 'error' : ''}}">
          <view class="form-label required">姓名</view>
          <input class="form-input" type="text" value="{{name}}" placeholder="请输入姓名" bindinput="inputName" />
          <view class="error-message" wx:if="{{nameError}}">请输入有效的姓名（至少2个字符）</view>
        </view>
        
        <view class="form-group">
          <view class="form-label required">性别</view>
          <picker bindchange="bindGenderChange" value="{{genders.indexOf(gender)}}" range="{{genders}}">
            <view class="form-picker {{gender ? '' : 'placeholder'}}">
              {{gender || '请选择性别'}}
              <view class="picker-arrow"></view>
            </view>
          </picker>
        </view>
        
        <view class="form-group">
          <view class="form-label">年龄</view>
          <input class="form-input" type="number" value="{{age}}" placeholder="请输入年龄" bindinput="inputAge" />
        </view>
        
        <view class="form-group {{phoneError ? 'error' : ''}}">
          <view class="form-label required">手机号码</view>
          <input class="form-input" type="number" value="{{phone}}" placeholder="请输入手机号码" bindinput="inputPhone" />
          <view class="error-message" wx:if="{{phoneError}}">请输入有效的手机号码</view>
        </view>
        
        <view class="form-group {{idCardError ? 'error' : ''}}">
          <view class="form-label required">身份证号</view>
          <input class="form-input" type="idcard" value="{{idCard}}" placeholder="请输入身份证号" bindinput="inputIdCard" />
          <view class="error-message" wx:if="{{idCardError}}">请输入有效的身份证号</view>
        </view>
        
        <view class="form-group">
          <view class="form-label">邮箱</view>
          <input class="form-input" type="text" value="{{email}}" placeholder="请输入邮箱" bindinput="inputEmail" />
        </view>
        
        <view class="form-group">
          <view class="form-label">住址</view>
          <input class="form-input" type="text" value="{{address}}" placeholder="请输入住址" bindinput="inputAddress" />
        </view>
        
        <view class="form-group">
          <view class="form-label">紧急联系人</view>
          <input class="form-input" type="text" value="{{emergencyContact}}" placeholder="请输入紧急联系人" bindinput="inputEmergencyContact" />
        </view>
        
        <view class="form-group">
          <view class="form-label">紧急联系电话</view>
          <input class="form-input" type="number" value="{{emergencyPhone}}" placeholder="请输入紧急联系电话" bindinput="inputEmergencyPhone" />
        </view>
      </view>
      
      <!-- 工作信息 -->
      <view class="form-section">
        <view class="section-title">
          <view class="section-icon-work"></view>
          <text>工作信息</text>
        </view>
        
        <view class="form-group {{employeeIdError ? 'error' : ''}}">
          <view class="form-label required">员工编号</view>
          <input class="form-input" type="text" value="{{employeeId}}" placeholder="请输入员工编号" bindinput="inputEmployeeId" />
          <view class="error-message" wx:if="{{employeeIdError}}">请输入有效的员工编号（至少2个字符）</view>
        </view>
        
        <view class="form-group {{departmentError ? 'error' : ''}}">
          <view class="form-label required">部门</view>
          <picker bindchange="bindDepartmentChange" value="{{departmentIndex}}" range="{{departments}}">
            <view class="form-picker {{department ? '' : 'placeholder'}}">
              {{department || '请选择部门'}}
              <view class="picker-arrow"></view>
            </view>
          </picker>
          <view class="error-message" wx:if="{{departmentError}}">请选择部门</view>
        </view>
        
        <view class="form-group {{positionError ? 'error' : ''}}">
          <view class="form-label required">职位</view>
          <picker bindchange="bindPositionChange" value="{{positionIndex}}" range="{{positions}}">
            <view class="form-picker {{position ? '' : 'placeholder'}}">
              {{position || '请选择职位'}}
              <view class="picker-arrow"></view>
            </view>
          </picker>
          <view class="error-message" wx:if="{{positionError}}">请选择职位</view>
        </view>
        
        <view class="form-group">
          <view class="form-label required">入职日期</view>
          <picker mode="date" bindchange="bindEntryDateChange" value="{{entryDate}}">
            <view class="form-picker">
              {{entryDate}}
              <view class="picker-arrow"></view>
            </view>
          </picker>
        </view>
        
        <view class="form-group">
          <view class="form-label">状态</view>
          <picker bindchange="bindStatusChange" value="{{statusIndex}}" range="{{statuses}}">
            <view class="form-picker">
              {{status}}
              <view class="picker-arrow"></view>
            </view>
          </picker>
        </view>
        
        <view class="form-group">
          <view class="form-label">薪资</view>
          <input class="form-input" type="digit" value="{{salary}}" placeholder="请输入薪资" bindinput="inputSalary" />
        </view>
        
        <view class="form-group">
          <view class="form-label">绩效</view>
          <picker bindchange="bindPerformanceChange" value="{{performanceIndex}}" range="{{performances}}">
            <view class="form-picker {{performance ? '' : 'placeholder'}}">
              {{performance || '请选择绩效'}}
              <view class="picker-arrow"></view>
            </view>
          </picker>
        </view>
      </view>
      
      <!-- 教育背景 -->
      <view class="form-section">
        <view class="section-title">
          <view class="section-icon-education"></view>
          <text>教育背景</text>
        </view>
        
        <view class="form-group">
          <view class="form-label">学历</view>
          <picker bindchange="bindEducationChange" value="{{educationIndex}}" range="{{educations}}">
            <view class="form-picker {{education ? '' : 'placeholder'}}">
              {{education || '请选择学历'}}
              <view class="picker-arrow"></view>
            </view>
          </picker>
        </view>
        
        <view class="form-group">
          <view class="form-label">专业</view>
          <input class="form-input" type="text" value="{{major}}" placeholder="请输入专业" bindinput="inputMajor" />
        </view>
      </view>
      
      <!-- 技能与证书 -->
      <view class="form-section">
        <view class="section-title">
          <view class="section-icon-skill"></view>
          <text>技能与证书</text>
        </view>
        
        <view class="form-group">
          <view class="form-label">技能</view>
          <input class="form-input" type="text" value="{{skills}}" placeholder="请输入技能，多个技能用逗号分隔" bindinput="inputSkills" />
        </view>
        
        <view class="form-group">
          <view class="form-label">证书</view>
          <input class="form-input" type="text" value="{{certificates}}" placeholder="请输入证书，多个证书用逗号分隔" bindinput="inputCertificates" />
        </view>
      </view>
      
      <!-- 备注 -->
      <view class="form-section">
        <view class="section-title">
          <view class="section-icon-note"></view>
          <text>备注</text>
        </view>
        
        <view class="form-group">
          <textarea class="form-textarea" value="{{notes}}" placeholder="请输入备注信息" bindinput="inputNotes"></textarea>
        </view>
      </view>
      
      <!-- 证件照片 -->
      <view class="form-section">
        <view class="section-title">
          <view class="section-icon-photo"></view>
          <text>证件照片</text>
        </view>
        
        <view class="form-group">
          <view class="form-label required">工作证照片</view>
          <view class="upload-container" bindtap="uploadEmployeeCard">
            <image class="upload-image" src="{{employeeCardPhotoPath}}" mode="aspectFill" wx:if="{{employeeCardPhotoPath}}"></image>
            <view class="upload-placeholder" wx:else>
              <view class="upload-icon"></view>
              <text>点击上传</text>
            </view>
          </view>
        </view>
        
        <view class="form-group">
          <view class="form-label required">人脸照片</view>
          <view class="upload-container" bindtap="uploadFacePhoto">
            <image class="upload-image" src="{{facePhotoPath}}" mode="aspectFill" wx:if="{{facePhotoPath}}"></image>
            <view class="upload-placeholder" wx:else>
              <view class="upload-icon"></view>
              <text>点击上传</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 错误提示 -->
      <view class="error-toast" wx:if="{{errorMsg}}">{{errorMsg}}</view>
      
      <!-- 底部按钮 -->
      <view class="form-actions">
        <button class="cancel-btn" bindtap="cancelEdit" disabled="{{isSubmitting}}">取消</button>
        <button class="submit-btn" bindtap="submitForm" disabled="{{isSubmitting}}">
          <view class="loading-icon" wx:if="{{isSubmitting}}"></view>
          <text>{{isSubmitting ? '提交中...' : (mode === 'add' ? '添加' : '保存')}}</text>
        </button>
      </view>
    </view>
  </block>
</view>
