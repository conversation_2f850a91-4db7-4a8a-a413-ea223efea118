// pages/property/staff/staff-detail.js
const util = require('../../../../utils/util.js')

Page({
  data: {
    darkMode: false,
    staffId: '',
    staffInfo: null,
    isLoading: true,
    showActionSheet: false,
    actions: [
      { name: '编辑信息', color: '#007aff', icon: 'edit' },
      { name: '删除员工', color: '#ff3b30', icon: 'delete' }
    ]
  },

  onLoad: function(options) {
    // 检查暗黑模式
    const app = getApp()
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      })
    }

    // 获取员工ID
    if (options.id) {
      this.setData({
        staffId: options.id
      });

      // 加载员工详情
      this.loadStaffDetail();
    } else {
      wx.showToast({
        title: '员工ID不存在',
        icon: 'none',
        duration: 2000,
        complete: () => {
          setTimeout(() => {
            wx.navigateBack();
          }, 2000);
        }
      });
    }
  },

  // 加载员工详情
  loadStaffDetail: function() {
    this.setData({
      isLoading: true
    });

    // 模拟从服务器获取员工详情
    setTimeout(() => {
      // 模拟数据
      const staffList = [
        {
          id: '001',
          name: '张三',
          gender: '男',
          age: 35,
          phone: '13800138001',
          idCard: '110101198505079876',
          employeeId: 'ZH001',
          department: '物业管理部',
          position: '经理',
          entryDate: '2020-01-15',
          employeeCardPhoto: '/images/default-avatar.svg',
          facePhoto: '/images/default-avatar.svg',
          status: '在职',
          email: '<EMAIL>',
          address: '北京市朝阳区建国路88号',
          emergencyContact: '李四',
          emergencyPhone: '13900139000',
          education: '本科',
          major: '物业管理',
          skills: ['物业管理', '客户服务', '团队管理'],
          certificates: ['物业管理师证书'],
          performance: '优秀',
          salary: '8000',
          notes: '工作认真负责，善于团队协作'
        },
        {
          id: '002',
          name: '李四',
          gender: '女',
          age: 28,
          phone: '13900139002',
          idCard: '110101199201234567',
          employeeId: 'ZH002',
          department: '客服部',
          position: '客服专员',
          entryDate: '2021-03-20',
          employeeCardPhoto: '/images/default-avatar.svg',
          facePhoto: '/images/default-avatar.svg',
          status: '在职',
          email: '<EMAIL>',
          address: '北京市海淀区中关村大街1号',
          emergencyContact: '王五',
          emergencyPhone: '13700137000',
          education: '大专',
          major: '市场营销',
          skills: ['客户服务', '沟通协调', '问题解决'],
          certificates: ['客服专员证书'],
          performance: '良好',
          salary: '6000',
          notes: '服务态度好，沟通能力强'
        },
        {
          id: '003',
          name: '王五',
          gender: '男',
          age: 42,
          phone: '13700137003',
          idCard: '110101197901234567',
          employeeId: 'ZH003',
          department: '工程部',
          position: '工程师',
          entryDate: '2019-05-10',
          employeeCardPhoto: '/images/default-avatar.svg',
          facePhoto: '/images/default-avatar.svg',
          status: '在职',
          email: '<EMAIL>',
          address: '北京市丰台区丰台路20号',
          emergencyContact: '赵六',
          emergencyPhone: '13600136000',
          education: '大专',
          major: '电气工程',
          skills: ['电气维修', '设备维护', '故障排除'],
          certificates: ['电工证', '安全生产证'],
          performance: '优秀',
          salary: '7500',
          notes: '技术过硬，经验丰富'
        },
        {
          id: '004',
          name: '赵六',
          gender: '女',
          age: 31,
          phone: '13600136004',
          idCard: '110101199001234567',
          employeeId: 'ZH004',
          department: '财务部',
          position: '财务专员',
          entryDate: '2022-01-05',
          employeeCardPhoto: '/images/default-avatar.svg',
          facePhoto: '/images/default-avatar.svg',
          status: '在职',
          email: '<EMAIL>',
          address: '北京市西城区西长安街10号',
          emergencyContact: '张三',
          emergencyPhone: '13800138001',
          education: '本科',
          major: '会计学',
          skills: ['财务管理', '会计核算', '预算编制'],
          certificates: ['会计师证书'],
          performance: '良好',
          salary: '7000',
          notes: '工作细致，责任心强'
        },
        {
          id: '005',
          name: '钱七',
          gender: '男',
          age: 45,
          phone: '13500135005',
          idCard: '110101197601234567',
          employeeId: 'ZH005',
          department: '保安部',
          position: '主管',
          entryDate: '2018-08-18',
          employeeCardPhoto: '/images/default-avatar.svg',
          facePhoto: '/images/default-avatar.svg',
          status: '在职',
          email: '<EMAIL>',
          address: '北京市东城区东长安街15号',
          emergencyContact: '孙八',
          emergencyPhone: '13400134000',
          education: '高中',
          major: '安保管理',
          skills: ['安全管理', '团队协调', '应急处理'],
          certificates: ['保安证'],
          performance: '优秀',
          salary: '6500',
          notes: '工作认真负责，经验丰富'
        }
      ];

      // 查找对应ID的员工
      const staffInfo = staffList.find(staff => staff.id === this.data.staffId);

      if (staffInfo) {
        // 添加位置信息和健康信息
        staffInfo.currentLocation = '北京市朝阳区建国路88号附近';
        staffInfo.locationUpdateTime = '2023-10-25 14:30:45';
        staffInfo.locationLatitude = 39.9042;
        staffInfo.locationLongitude = 116.4074;

        // 添加健康信息
        staffInfo.heartRate = 75;
        staffInfo.bloodPressure = '120/80';
        staffInfo.temperature = 36.5;
        staffInfo.steps = 6542;
        staffInfo.healthUpdateTime = '2023-10-25 14:30:45';

        this.setData({
          staffInfo: staffInfo,
          isLoading: false
        });

        // 启动模拟健康数据更新
        this.startHealthDataSimulation();
      } else {
        wx.showToast({
          title: '未找到员工信息',
          icon: 'none',
          duration: 2000,
          complete: () => {
            setTimeout(() => {
              wx.navigateBack();
            }, 2000);
          }
        });
      }
    }, 1000);
  },

  // 显示操作菜单
  showActions: function() {
    this.setData({
      showActionSheet: true
    });
  },

  // 隐藏操作菜单
  hideActions: function() {
    this.setData({
      showActionSheet: false
    });
  },

  // 处理操作菜单选择
  handleActionClick: function(e) {
    const index = e.currentTarget.dataset.index;
    this.hideActions();

    switch(index) {
      case 0: // 编辑信息
        this.editStaff();
        break;
      case 1: // 删除员工
        this.confirmDeleteStaff();
        break;
    }
  },

  // 编辑员工信息
  editStaff: function() {
    wx.navigateTo({
      url: `./staff-edit?id=${this.data.staffId}&mode=edit`
    });
  },

  // 确认删除员工
  confirmDeleteStaff: function() {
    wx.showModal({
      title: '确认删除',
      content: `确定要删除员工"${this.data.staffInfo.name}"吗？此操作不可恢复。`,
      confirmText: '删除',
      confirmColor: '#ff3b30',
      success: (res) => {
        if (res.confirm) {
          this.deleteStaff();
        }
      }
    });
  },

  // 删除员工
  deleteStaff: function() {
    wx.showLoading({
      title: '删除中...',
    });

    // 模拟删除操作
    setTimeout(() => {
      wx.hideLoading();

      wx.showToast({
        title: '删除成功',
        icon: 'success',
        duration: 2000,
        complete: () => {
          // 设置需要刷新员工列表的标志
          wx.setStorageSync('staffDataNeedRefresh', true);

          // 返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 2000);
        }
      });
    }, 1500);
  },

  // 拨打电话
  callPhone: function() {
    if (this.data.staffInfo && this.data.staffInfo.phone) {
      wx.makePhoneCall({
        phoneNumber: this.data.staffInfo.phone
      });
    }
  },

  // 发送短信
  sendMessage: function() {
    if (this.data.staffInfo && this.data.staffInfo.phone) {
      wx.showToast({
        title: '暂不支持发送短信',
        icon: 'none'
      });
    }
  },

  // 查看大图
  viewImage: function(e) {
    const url = e.currentTarget.dataset.url;
    if (url) {
      wx.previewImage({
        urls: [url],
        current: url
      });
    }
  },

  // 页面显示时刷新数据
  onShow: function() {
    // 检查是否需要刷新数据
    const needRefresh = wx.getStorageSync('staffDetailNeedRefresh');
    if (needRefresh) {
      this.loadStaffDetail();
      wx.removeStorageSync('staffDetailNeedRefresh');
    }
  },

  // 查看大地图
  viewLargeMap: function() {
    const { staffInfo } = this.data;
    if (staffInfo && staffInfo.locationLatitude && staffInfo.locationLongitude) {
      wx.openLocation({
        latitude: staffInfo.locationLatitude,
        longitude: staffInfo.locationLongitude,
        name: staffInfo.name + '的位置',
        address: staffInfo.currentLocation,
        scale: 18
      });
    }
  },

  // 启动健康数据模拟
  startHealthDataSimulation: function() {
    // 清除之前的定时器
    if (this.healthDataTimer) {
      clearInterval(this.healthDataTimer);
    }

    // 每30秒更新一次健康数据
    this.healthDataTimer = setInterval(() => {
      if (!this.data.staffInfo) return;

      // 模拟心率变化 (正常范围60-100)
      const heartRateChange = Math.floor(Math.random() * 7) - 3; // -3到3的随机变化
      let heartRate = this.data.staffInfo.heartRate + heartRateChange;
      heartRate = Math.max(58, Math.min(102, heartRate)); // 限制在58-102范围内

      // 模拟体温变化 (正常范围36-37.2)
      const tempChange = (Math.random() * 0.4 - 0.2).toFixed(1); // -0.2到0.2的随机变化
      let temperature = parseFloat((parseFloat(this.data.staffInfo.temperature) + parseFloat(tempChange)).toFixed(1));
      temperature = Math.max(36.0, Math.min(37.5, temperature)); // 限制在36-37.5范围内

      // 模拟步数增加
      const stepsIncrease = Math.floor(Math.random() * 50); // 0到50的随机增加
      const steps = this.data.staffInfo.steps + stepsIncrease;

      // 更新健康数据
      const now = new Date();
      const healthUpdateTime = now.getFullYear() + '-' +
                              String(now.getMonth() + 1).padStart(2, '0') + '-' +
                              String(now.getDate()).padStart(2, '0') + ' ' +
                              String(now.getHours()).padStart(2, '0') + ':' +
                              String(now.getMinutes()).padStart(2, '0') + ':' +
                              String(now.getSeconds()).padStart(2, '0');

      this.setData({
        'staffInfo.heartRate': heartRate,
        'staffInfo.temperature': temperature,
        'staffInfo.steps': steps,
        'staffInfo.healthUpdateTime': healthUpdateTime
      });
    }, 30000); // 30秒更新一次
  },

  // 页面卸载时清除定时器
  onUnload: function() {
    if (this.healthDataTimer) {
      clearInterval(this.healthDataTimer);
      this.healthDataTimer = null;
    }
  }
})
