const REQUEST = require('../utils/request.js')

/**
 * 访客管理API模块
 */
const visitorsApi = {

  /**
   * 分页查询访客列表
   * @param {Object} params - 查询参数
   * @param {number} params.pageNum - 页码，默认1
   * @param {number} params.pageSize - 每页大小，默认10
   * @param {string} params.visitorName - 访客姓名（可选）
   * @param {string} params.phone - 手机号（可选）
   * @param {string} params.status - 访客状态（可选）
   * @returns {Promise} 返回访客列表
   */
  getVisitorList: function(params = {}) {
    const defaultParams = {
      pageNum: 1,
      pageSize: 10
    }

    return REQUEST.request('/users-api/v1/community/visitor/page', 'GET', {
      ...defaultParams,
      ...params
    }, true)
  },

  /**
   * 新增访客
   * @param {Object} visitorData - 访客信息
   * @param {string} visitorData.visitorName - 访客姓名
   * @param {string} visitorData.phone - 手机号码
   * @param {string} visitorData.vehicleNumber - 车牌号（可选）
   * @param {string} visitorData.note - 备注（可选）
   * @param {number} visitorData.stayDuration - 停留时长
   * @param {string} visitorData.timeUnit - 时间单位（hour/day）
   * @param {string} visitorData.visitTime - 访问时间（ISO格式）
   * @param {number} visitorData.communityId - 社区ID
   * @returns {Promise} 返回新增结果
   */
  addVisitor: function(visitorData) {
    const params = {
      visitorName: visitorData.visitorName,
      phone: visitorData.phone,
      vehicleNumber: visitorData.vehicleNumber || '',
      note: visitorData.note || '',
      stayDuration: visitorData.stayDuration,
      timeUnit: visitorData.timeUnit || 'hour',
      visitTime: visitorData.visitTime,
      communityId: visitorData.communityId
    }

    return REQUEST.request('/users-api/v1/community/visitor', 'POST', params, true)
  },

  /**
   * 编辑访客
   * @param {Object} visitorData - 访客信息
   * @param {number} visitorData.id - 访客ID
   * @param {string} visitorData.visitorName - 访客姓名
   * @param {string} visitorData.phone - 手机号码
   * @param {string} visitorData.vehicleNumber - 车牌号（可选）
   * @param {string} visitorData.note - 备注（可选）
   * @param {number} visitorData.stayDuration - 停留时长
   * @param {string} visitorData.timeUnit - 时间单位（hour/day）
   * @param {string} visitorData.visitTime - 访问时间（ISO格式）
   * @param {number} visitorData.communityId - 社区ID
   * @returns {Promise} 返回编辑结果
   */
  updateVisitor: function(visitorData) {
    const params = {
      id: visitorData.id,
      visitorName: visitorData.visitorName,
      phone: visitorData.phone,
      vehicleNumber: visitorData.vehicleNumber || '',
      note: visitorData.note || '',
      stayDuration: visitorData.stayDuration,
      timeUnit: visitorData.timeUnit || 'hour',
      visitTime: visitorData.visitTime,
      communityId: visitorData.communityId
    }

    return REQUEST.request('/users-api/v1/community/visitor', 'PUT', params, true)
  },

  /**
   * 删除访客
   * @param {number} id - 访客ID
   * @returns {Promise} 返回删除结果
   */
  deleteVisitor: function(id) {
    return REQUEST.request(`/users-api/v1/community/visitor?id=${id}`, 'DELETE', {}, true)
  },

  /**
   * 获取访客详情
   * @param {number} id - 访客ID
   * @returns {Promise} 返回访客详情
   */
  getVisitorDetail: function(id) {
    return REQUEST.request(`/users-api/v1/community/visitor?id=`+id, 'GET', {}, true)
  },

  /**
   * 更新访客状态
   * @param {number} id - 访客ID
   * @param {string} status - 状态
   * @returns {Promise} 返回更新结果
   */
  updateVisitorStatus: function(id, status) {
    return REQUEST.request('/users-api/v1/community/visitor/status', 'PUT', {
      id: id,
      status: status
    }, true)
  }
}

module.exports = visitorsApi