<!--pages/payment/detail/detail.wxml-->
<view class="container {{darkMode ? 'darkMode' : ''}}">
  <!-- 加载中状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 缴费详情内容 -->
  <block wx:if="{{!isLoading && paymentDetail}}">
    <!-- 内容容器 -->
    <view class="content-container">
      <!-- 状态卡片 -->
      <view class="status-card {{paymentDetail.status}}">
        <view class="status-icon {{paymentDetail.status}}"></view>
        <view class="status-text">
          {{paymentDetail.status === 'paid' ? '已支付' :
            paymentDetail.status === 'unpaid' ? '待支付' : '已逾期'}}
        </view>
        <view class="status-desc" wx:if="{{paymentDetail.status === 'paid'}}">
          感谢您按时缴纳费用
        </view>
        <view class="status-desc" wx:elif="{{paymentDetail.status === 'unpaid'}}">
          请在到期前完成支付
        </view>
        <view class="status-desc" wx:else>
          请尽快完成支付
        </view>
      </view>

      <!-- 缴费基本信息 -->
      <view class="detail-card">
        <view class="card-title">缴费信息</view>
        <view class="info-row">
          <view class="info-label">缴费项目</view>
          <view class="info-value">{{paymentDetail.title}}</view>
        </view>
        <view class="info-row">
          <view class="info-label">缴费金额</view>
          <view class="info-value highlight">¥{{paymentDetail.amount}}</view>
        </view>
        <view class="info-row">
          <view class="info-label">缴费周期</view>
          <view class="info-value">{{paymentDetail.period}}</view>
        </view>
        <view class="info-row" wx:if="{{paymentDetail.status === 'paid'}}">
          <view class="info-label">支付时间</view>
          <view class="info-value">{{paymentDetail.paymentDate}}</view>
        </view>
        <view class="info-row" wx:else>
          <view class="info-label">截止日期</view>
          <view class="info-value {{paymentDetail.status === 'overdue' ? 'overdue' : ''}}">
            {{paymentDetail.dueDate}}
          </view>
        </view>
        <view class="info-row" wx:if="{{paymentDetail.status === 'paid'}}">
          <view class="info-label">支付方式</view>
          <view class="info-value">{{paymentDetail.paymentMethod}}</view>
        </view>
        <view class="info-row" wx:if="{{paymentDetail.status === 'paid' && paymentDetail.paymentAccount}}">
          <view class="info-label">支付账户</view>
          <view class="info-value">{{paymentDetail.paymentAccount}}</view>
        </view>
        <view class="info-row" wx:if="{{paymentDetail.status === 'paid' && paymentDetail.transactionId}}">
          <view class="info-label">交易单号</view>
          <view class="info-value transaction-id">{{paymentDetail.transactionId}}</view>
        </view>
        <view class="info-row" wx:if="{{paymentDetail.status === 'paid' && paymentDetail.invoiceNo}}">
          <view class="info-label">发票号码</view>
          <view class="info-value">{{paymentDetail.invoiceNo}}</view>
        </view>
      </view>

      <!-- 账单明细 -->
      <view class="detail-card">
        <view class="card-header" bindtap="toggleBillDetails">
          <view class="card-title">账单明细</view>
          <view class="card-action">
            <view class="action-text">{{billDetailsExpanded ? '收起' : '展开'}}</view>
            <view class="action-icon {{billDetailsExpanded ? 'up' : 'down'}}"></view>
          </view>
        </view>

        <view class="bill-details {{billDetailsExpanded ? 'expanded' : ''}}">
          <!-- 物业费明细 -->
          <block wx:if="{{paymentDetail.type === 'property'}}">
            <view class="bill-table">
              <view class="bill-table-header">
                <view class="bill-cell">项目</view>
                <view class="bill-cell">单价</view>
                <view class="bill-cell">面积</view>
                <view class="bill-cell">月数</view>
                <view class="bill-cell">金额</view>
              </view>
              <view class="bill-table-row" wx:for="{{paymentDetail.billDetails}}" wx:key="name">
                <view class="bill-cell">{{item.name}}</view>
                <view class="bill-cell">{{item.unitPrice}}{{item.unit}}</view>
                <view class="bill-cell">{{item.area}}㎡</view>
                <view class="bill-cell">{{item.months}}</view>
                <view class="bill-cell">¥{{item.amount}}</view>
              </view>
            </view>
            <view class="property-info">
              <view class="property-info-title">房屋信息</view>
              <view class="property-info-content">
                {{paymentDetail.propertyInfo.community}} {{paymentDetail.propertyInfo.building}}
                {{paymentDetail.propertyInfo.unit}} {{paymentDetail.propertyInfo.room}}
                ({{paymentDetail.propertyInfo.area}})
              </view>
            </view>
          </block>

          <!-- 停车费明细 -->
          <block wx:elif="{{paymentDetail.type === 'parking'}}">
            <view class="bill-table">
              <view class="bill-table-header">
                <view class="bill-cell">项目</view>
                <view class="bill-cell">单价</view>
                <view class="bill-cell">数量</view>
                <view class="bill-cell">月数</view>
                <view class="bill-cell">金额</view>
              </view>
              <view class="bill-table-row" wx:for="{{paymentDetail.billDetails}}" wx:key="name">
                <view class="bill-cell">{{item.name}}</view>
                <view class="bill-cell">{{item.unitPrice}}{{item.unit}}</view>
                <view class="bill-cell">{{item.quantity}}</view>
                <view class="bill-cell">{{item.months}}</view>
                <view class="bill-cell">¥{{item.amount}}</view>
              </view>
            </view>
            <view class="property-info">
              <view class="property-info-title">车位信息</view>
              <view class="property-info-content">
                {{paymentDetail.parkingInfo.community}} {{paymentDetail.parkingInfo.parkingNo}}
                ({{paymentDetail.parkingInfo.type}})
                <view wx:if="{{paymentDetail.parkingInfo.carNumber}}">
                  车牌号: {{paymentDetail.parkingInfo.carNumber}}
                </view>
              </view>
            </view>
          </block>

          <!-- 水电费明细 -->
          <block wx:elif="{{paymentDetail.type === 'utility'}}">
            <view class="bill-table">
              <view class="bill-table-header">
                <view class="bill-cell">项目</view>
                <view class="bill-cell">单价</view>
                <view class="bill-cell">上期读数</view>
                <view class="bill-cell">本期读数</view>
                <view class="bill-cell">用量</view>
                <view class="bill-cell">金额</view>
              </view>
              <view class="bill-table-row" wx:for="{{paymentDetail.billDetails}}" wx:key="name">
                <view class="bill-cell">{{item.name}}</view>
                <view class="bill-cell">{{item.unitPrice}}{{item.unit}}</view>
                <view class="bill-cell">{{item.previousReading}}</view>
                <view class="bill-cell">{{item.currentReading}}</view>
                <view class="bill-cell">{{item.usage}}</view>
                <view class="bill-cell">¥{{item.amount}}</view>
              </view>
            </view>
            <view class="property-info">
              <view class="property-info-title">抄表日期</view>
              <view class="property-info-content">{{paymentDetail.meterReadingDate}}</view>
            </view>
            <view class="property-info">
              <view class="property-info-title">房屋信息</view>
              <view class="property-info-content">
                {{paymentDetail.propertyInfo.community}} {{paymentDetail.propertyInfo.building}}
                {{paymentDetail.propertyInfo.unit}} {{paymentDetail.propertyInfo.room}}
              </view>
            </view>
          </block>

          <!-- 服务周期 -->
          <view class="service-period">
            <view class="service-period-title">服务周期</view>
            <view class="service-period-content">
              {{paymentDetail.servicePeriod.start}} 至 {{paymentDetail.servicePeriod.end}}
            </view>
          </view>
        </view>
      </view>

      <!-- 联系信息 -->
      <view class="detail-card">
        <view class="card-title">联系信息</view>
        <view class="info-row">
          <view class="info-label">业主姓名</view>
          <view class="info-value">{{paymentDetail.contact.name}}</view>
        </view>
        <view class="info-row">
          <view class="info-label">联系电话</view>
          <view class="info-value">{{paymentDetail.contact.phone}}</view>
        </view>
      </view>

      <!-- 底部操作按钮 -->
      <view class="action-buttons">
        <button class="action-button invoice {{!invoiceAvailable ? 'disabled' : ''}}" bindtap="viewInvoice">
          <view class="button-icon invoice-icon"></view>
          <text>发票</text>
        </button>
        <button class="action-button share" bindtap="showShareOptions">
          <view class="button-icon share-icon"></view>
          <text>分享</text>
        </button>
        <button class="action-button settings" bindtap="navigateToSettings">
          <view class="button-icon settings-icon"></view>
          <text>设置</text>
        </button>
      </view>
    </view>
  </block>

  <!-- 分享选项弹窗 -->
  <view class="share-options-modal {{showShareOptions ? 'show' : ''}}" bindtap="hideShareOptions">
    <view class="share-options-content" catchtap="stopPropagation">
      <view class="share-options-title">分享方式</view>
      <view class="share-options-list">
        <view class="share-option" bindtap="shareToWechat">
          <view class="share-option-icon wechat"></view>
          <view class="share-option-name">微信好友</view>
        </view>
        <view class="share-option" bindtap="saveScreenshot">
          <view class="share-option-icon screenshot"></view>
          <view class="share-option-name">保存截图</view>
        </view>
      </view>
      <button class="share-cancel-button" bindtap="hideShareOptions">取消</button>
    </view>
  </view>
</view>
