<!--审核详情页-->
<view class="container">
  <!-- 审核状态栏 -->
  <view class="status-bar {{reviewData.status}}">
    <view class="status-icon"></view>
    <view class="status-text">{{statusText}}</view>
  </view>
  
  <!-- 申请信息卡片 -->
  <view class="detail-card">
    <view class="card-title">申请信息</view>
    <view class="info-list">
      <view class="info-item">
        <view class="info-label">申请人</view>
        <view class="info-value">{{reviewData.applicant}}</view>
      </view>
      <view class="info-item">
        <view class="info-label">手机号</view>
        <view class="info-value">{{reviewData.phone}}</view>
      </view>
      <view class="info-item">
        <view class="info-label">申请时间</view>
        <view class="info-value">{{reviewData.submitTime}}</view>
      </view>
      
      <!-- 根据类型显示不同信息 -->
      <block wx:if="{{reviewData.type === 'identity'}}">
        <view class="info-item">
          <view class="info-label">身份证号</view>
          <view class="info-value">{{reviewData.idNumber}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">性别</view>
          <view class="info-value">{{reviewData.gender}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">出生日期</view>
          <view class="info-value">{{reviewData.birthDate}}</view>
        </view>
      </block>
      
      <block wx:elif="{{reviewData.type === 'house'}}">
        <view class="info-item">
          <view class="info-label">房屋地址</view>
          <view class="info-value">{{reviewData.address}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">房屋类型</view>
          <view class="info-value">{{reviewData.houseType}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">产权证号</view>
          <view class="info-value">{{reviewData.propertyNumber}}</view>
        </view>
      </block>
      
      <block wx:elif="{{reviewData.type === 'vehicle'}}">
        <view class="info-item">
          <view class="info-label">车牌号</view>
          <view class="info-value">{{reviewData.plateNumber}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">车辆类型</view>
          <view class="info-value">{{reviewData.vehicleType}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">品牌型号</view>
          <view class="info-value">{{reviewData.brand}} {{reviewData.model}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">车辆颜色</view>
          <view class="info-value">{{reviewData.color}}</view>
        </view>
      </block>
    </view>
  </view>
  
  <!-- 证件照片区域 -->
  <block wx:if="{{reviewData.type === 'identity'}}">
    <view class="detail-card">
      <view class="card-title">证件照片</view>
      <view class="photo-container">
        <view class="photo-item" bindtap="previewImage" data-url="{{reviewData.idCardFront}}">
          <image src="{{reviewData.idCardFront}}" mode="aspectFit" class="id-photo"></image>
          <view class="photo-label">身份证正面</view>
        </view>
        <view class="photo-item" bindtap="previewImage" data-url="{{reviewData.idCardBack}}">
          <image src="{{reviewData.idCardBack}}" mode="aspectFit" class="id-photo"></image>
          <view class="photo-label">身份证背面</view>
        </view>
      </view>
    </view>
  </block>
  
  <!-- 车辆照片区域 -->
  <block wx:if="{{reviewData.type === 'vehicle'}}">
    <view class="detail-card">
      <view class="card-title">车辆照片</view>
      <view class="photo-container">
        <view class="photo-item" bindtap="previewImage" data-url="{{reviewData.vehiclePhoto}}">
          <image src="{{reviewData.vehiclePhoto}}" mode="aspectFit" class="vehicle-photo"></image>
          <view class="photo-label">车辆照片</view>
        </view>
        <view class="photo-item" bindtap="previewImage" data-url="{{reviewData.licensePlatePhoto}}">
          <image src="{{reviewData.licensePlatePhoto}}" mode="aspectFit" class="license-photo"></image>
          <view class="photo-label">车牌照片</view>
        </view>
      </view>
    </view>
  </block>
  
  <!-- 房屋照片区域 -->
  <block wx:if="{{reviewData.type === 'house'}}">
    <view class="detail-card">
      <view class="card-title">房屋证明</view>
      <view class="photo-container">
        <view class="photo-item" bindtap="previewImage" data-url="{{reviewData.propertyPhoto}}">
          <image src="{{reviewData.propertyPhoto}}" mode="aspectFit" class="property-photo"></image>
          <view class="photo-label">产权证明</view>
        </view>
      </view>
    </view>
  </block>
  
  <!-- 关联信息区域 -->
  <view class="detail-card">
    <view class="card-title">关联信息</view>
    <block wx:if="{{reviewData.type === 'identity'}}">
      <view class="related-house">
        <view class="related-title">关联房屋</view>
        <view class="related-content">{{reviewData.relatedHouse || '无关联房屋'}}</view>
      </view>
    </block>
    
    <block wx:elif="{{reviewData.type === 'house' || reviewData.type === 'vehicle'}}">
      <view class="related-resident">
        <view class="related-title">关联居民</view>
        <view class="related-content">{{reviewData.relatedResident || '无关联居民'}}</view>
      </view>
    </block>
  </view>
  
  <!-- 审核意见区域 -->
  <block wx:if="{{reviewData.status === 'pending'}}">
    <view class="detail-card">
      <view class="card-title">审核意见</view>
      <textarea class="review-comment" placeholder="请输入审核意见（选填）" value="{{reviewComment}}" bindinput="inputReviewComment"></textarea>
    </view>
  </block>
  
  <!-- 已审核信息 -->
  <block wx:if="{{reviewData.status !== 'pending'}}">
    <view class="detail-card">
      <view class="card-title">审核结果</view>
      <view class="review-result">
        <view class="result-item">
          <view class="result-label">审核人</view>
          <view class="result-value">{{reviewData.reviewer}}</view>
        </view>
        <view class="result-item">
          <view class="result-label">审核时间</view>
          <view class="result-value">{{reviewData.reviewTime}}</view>
        </view>
        <view class="result-item">
          <view class="result-label">审核结果</view>
          <view class="result-value {{reviewData.status}}">{{statusText}}</view>
        </view>
        <block wx:if="{{reviewData.status === 'rejected'}}">
          <view class="result-item">
            <view class="result-label">拒绝原因</view>
            <view class="result-value reject-reason">{{reviewData.rejectReason}}</view>
          </view>
        </block>
        <block wx:if="{{reviewData.reviewComment}}">
          <view class="result-item">
            <view class="result-label">审核意见</view>
            <view class="result-value">{{reviewData.reviewComment}}</view>
          </view>
        </block>
      </view>
    </view>
  </block>
  
  <!-- 底部操作区 -->
  <block wx:if="{{reviewData.status === 'pending'}}">
    <view class="bottom-actions">
      <button class="btn-reject" bindtap="showRejectModal" disabled="{{submitting}}">拒绝</button>
      <button class="btn-approve" bindtap="showApproveModal" disabled="{{submitting}}">通过审核</button>
    </view>
  </block>
  
  <!-- 拒绝原因弹窗 -->
  <view class="modal {{showRejectModal ? 'show' : ''}}" catchtouchmove="preventTouchMove">
    <view class="modal-mask" bindtap="hideModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">驳回原因</text>
      </view>
      <view class="modal-body">
        <textarea class="reject-reason" placeholder="请输入驳回原因..." value="{{rejectReason}}" bindinput="inputRejectReason"></textarea>
      </view>
      <view class="modal-footer">
        <button class="btn-cancel" bindtap="hideModal">取消</button>
        <button class="btn-confirm" bindtap="confirmReject" disabled="{{submitting}}">确认</button>
      </view>
    </view>
  </view>
  
  <!-- 通过确认弹窗 -->
  <view class="modal {{showApproveModal ? 'show' : ''}}" catchtouchmove="preventTouchMove">
    <view class="modal-mask" bindtap="hideModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">确认通过</text>
      </view>
      <view class="modal-body">
        <text class="confirm-text">确定通过此审核申请吗？</text>
      </view>
      <view class="modal-footer">
        <button class="btn-cancel" bindtap="hideModal">取消</button>
        <button class="btn-confirm" bindtap="confirmApprove" disabled="{{submitting}}">确认</button>
      </view>
    </view>
  </view>
</view>
