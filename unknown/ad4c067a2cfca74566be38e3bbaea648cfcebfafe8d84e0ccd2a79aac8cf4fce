/* 添加居民页面样式 */
.container {
  min-height: 100vh;
  background-color: #F2F2F7;
  padding-bottom: 120rpx;
}

/* 表单容器 */
.form-container {
  padding: 24rpx 0;
}

/* 表单分区 */
.form-section {
  background: #FFFFFF;
  border-radius: 28rpx;
  margin: 0 32rpx 24rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
  margin-bottom: 24rpx;
}

/* 表单项 */
.form-item {
  margin-bottom: 24rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 28rpx;
  color: #8E8E93;
  margin-bottom: 12rpx;
}

.form-input {
  height: 88rpx;
  background: #F2F2F7;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #000000;
}

/* 单选框组 */
.radio-group {
  display: flex;
  height: 88rpx;
  align-items: center;
}

.radio {
  margin-right: 48rpx;
  font-size: 28rpx;
  color: #000000;
  display: flex;
  align-items: center;
}

/* 日期选择器 */
.picker-value {
  height: 88rpx;
  background: #F2F2F7;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #000000;
  display: flex;
  align-items: center;
}

/* 房屋选择器 */
.house-selector {
  height: 88rpx;
  background: #F2F2F7;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #000000;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.house-value {
  flex: 1;
}

.selector-arrow {
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

/* 错误状态 */
.form-item.error .form-input,
.form-item.error .picker-value,
.form-item.error .house-selector {
  border: 1rpx solid #FF3B30;
}

.error-message {
  font-size: 24rpx;
  color: #FF3B30;
  margin-top: 8rpx;
}

/* 底部操作区 */
.bottom-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  padding: 24rpx 32rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.btn-cancel, .btn-submit {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.btn-cancel {
  background: #FFFFFF;
  color: #8E8E93;
  border: 1rpx solid rgba(142, 142, 147, 0.2);
  margin-right: 24rpx;
}

.btn-submit {
  background: #FF8C00;
  color: #FFFFFF;
}

.btn-submit[disabled] {
  opacity: 0.5;
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(4px);
}

.modal-content {
  position: relative;
  width: 600rpx;
  background: #FFFFFF;
  border-radius: 28rpx;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s;
}

.modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  padding: 32rpx;
  text-align: center;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
}

.modal-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #000000;
}

.modal-body {
  padding: 32rpx;
  max-height: 600rpx;
}

/* 房屋列表 */
.house-list {
  max-height: 500rpx;
}

.house-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
}

.house-item:last-child {
  border-bottom: none;
}

.house-address {
  font-size: 28rpx;
  color: #000000;
}

.item-selected {
  width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background: #FF8C00;
  position: relative;
}

.item-selected::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16rpx;
  height: 8rpx;
  border-left: 2rpx solid #FFFFFF;
  border-bottom: 2rpx solid #FFFFFF;
  transform: translate(-50%, -60%) rotate(-45deg);
}

.modal-footer {
  padding: 0;
  border-top: 1rpx solid rgba(60, 60, 67, 0.1);
}

.modal-footer button {
  height: 100rpx;
  border-radius: 0;
  font-size: 32rpx;
  background: transparent;
  color: #FF8C00;
  font-weight: 500;
}

.modal-footer button::after {
  border: none;
}