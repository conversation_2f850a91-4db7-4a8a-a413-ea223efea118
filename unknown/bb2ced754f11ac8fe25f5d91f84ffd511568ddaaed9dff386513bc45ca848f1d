// pages/property/vehicle/detail/index.js
const dateUtil = require('../../../../utils/dateUtil.js')

Page({
  data: {
    id: null, // 车辆ID
    vehicleData: {}, // 车辆数据
    owner: null, // 车主信息
    isEditing: false, // 是否处于编辑模式
    editData: {}, // 编辑数据
    showOwnerModal: false, // 是否显示更换车主弹窗
    searchText: '', // 搜索文本
    searchResults: [], // 搜索结果
    selectedOwner: null, // 选中的车主
    submitting: false // 是否正在提交
  },

  onLoad: function(options) {
    const { id } = options;

    this.setData({
      id: id
    });

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '车辆详情'
    });

    // 加载车辆详情
    this.loadVehicleDetail();
  },

  // 加载车辆详情
  loadVehicleDetail: function() {
    // 模拟加载数据
    setTimeout(() => {
      // 模拟车辆数据
      const vehicleData = {
        id: this.data.id,
        plateNumber: '京A·12345',
        brand: '丰田',
        model: '卡罗拉',
        color: '白色',
        type: '小型汽车',
        engineNumber: 'ENG123456',
        vin: 'VIN123456789012345',
        registerDate: '2020-05-15',
        parkingSpace: 'A-123',
        lastUpdateTime: '2023-10-15'
      };

      // 模拟车主信息
      const owner = {
        id: 1,
        name: '张三',
        phone: '138****1234',
        idNumber: '410******1234',
        startDate: '2020-05-15'
      };

      this.setData({
        vehicleData: vehicleData,
        owner: owner,
        editData: JSON.parse(JSON.stringify(vehicleData)) // 深拷贝用于编辑
      });
    }, 500);
  },

  // 进入编辑模式
  enterEditMode: function() {
    this.setData({
      isEditing: true
    });
  },

  // 取消编辑
  cancelEdit: function() {
    this.setData({
      isEditing: false,
      editData: JSON.parse(JSON.stringify(this.data.vehicleData)) // 重置编辑数据
    });
  },

  // 保存编辑
  saveEdit: function() {
    this.setData({
      submitting: true
    });

    // 模拟保存操作
    setTimeout(() => {
      // 更新车辆数据
      this.setData({
        vehicleData: JSON.parse(JSON.stringify(this.data.editData)),
        isEditing: false,
        submitting: false
      });

      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });
    }, 1000);
  },

  // 编辑数据输入
  onEditInput: function(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    const editData = this.data.editData;

    // 更新对应字段
    editData[field] = value;

    this.setData({
      editData: editData
    });
  },

  // 显示更换车主弹窗
  showOwnerModal: function() {
    this.setData({
      showOwnerModal: true,
      searchText: '',
      searchResults: [],
      selectedOwner: null
    });
  },

  // 隐藏更换车主弹窗
  hideOwnerModal: function() {
    this.setData({
      showOwnerModal: false
    });
  },

  // 搜索居民
  searchResident: function(e) {
    const searchText = e.detail.value;

    this.setData({
      searchText: searchText
    });

    if (searchText.trim() === '') {
      this.setData({
        searchResults: []
      });
      return;
    }

    // 模拟搜索结果
    setTimeout(() => {
      const searchResults = [
        {
          id: 2,
          name: '李四',
          phone: '139****5678',
          idNumber: '320******5678'
        },
        {
          id: 3,
          name: '王五',
          phone: '137****9012',
          idNumber: '110******9012'
        },
        {
          id: 4,
          name: '赵六',
          phone: '136****3456',
          idNumber: '440******3456'
        }
      ];

      this.setData({
        searchResults: searchResults
      });
    }, 300);
  },

  // 选择车主
  selectOwner: function(e) {
    const { id } = e.currentTarget.dataset;
    const owner = this.data.searchResults.find(item => item.id === id);

    this.setData({
      selectedOwner: owner
    });
  },

  // 更换车主
  changeOwner: function() {
    if (!this.data.selectedOwner) {
      wx.showToast({
        title: '请选择车主',
        icon: 'none'
      });
      return;
    }

    this.setData({
      submitting: true
    });

    // 模拟更换操作
    setTimeout(() => {
      const newOwner = this.data.selectedOwner;

      // 更新车主信息
      this.setData({
        owner: {
          id: newOwner.id,
          name: newOwner.name,
          phone: newOwner.phone,
          idNumber: newOwner.idNumber,
          startDate: dateUtil.formatTime(new Date()).split(' ')[0] // 只取日期部分
        },
        showOwnerModal: false,
        submitting: false
      });

      wx.showToast({
        title: '更换成功',
        icon: 'success'
      });
    }, 1000);
  },

  // 查看车主详情
  viewOwnerDetail: function() {
    if (this.data.owner) {
      wx.navigateTo({
        url: `/pages/property/resident/detail/index?id=${this.data.owner.id}`
      });
    }
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    // 重新加载车辆详情
    this.loadVehicleDetail();

    // 停止下拉刷新
    wx.stopPullDownRefresh();
  },

  // 阻止滑动穿透
  preventTouchMove: function() {
    return false;
  }
})
