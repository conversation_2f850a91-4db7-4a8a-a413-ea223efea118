<!-- 公告发布/编辑页 -->
<view class="container {{darkMode ? 'dark-mode' : ''}}">
  <!-- 表单区域 -->
  <view class="form-container">
    <!-- 标题区 -->
    <view class="form-item">
      <view class="form-label">公告标题</view>
      <input class="form-input title-input" type="text" placeholder="请输入公告标题（最多50字）" maxlength="50" value="{{formData.title}}" bindinput="inputTitle" />
      <view class="char-count">{{formData.title.length}}/50</view>
    </view>

    <!-- 公告类型 -->
    <view class="form-item">
      <view class="form-label">公告类型</view>
      <picker class="form-picker" bindchange="selectType" value="{{typeIndex}}" range="{{typeOptions}}" range-key="name">
        <view class="picker-content">
          <view class="type-icon {{typeOptions[typeIndex].id}}"></view>
          <text>{{typeOptions[typeIndex].name}}</text>
          <view class="arrow-icon"></view>
        </view>
      </picker>
    </view>

    <!-- 发布对象 -->
    <view class="form-item">
      <view class="form-label">发布对象</view>
      <picker class="form-picker" bindchange="selectTarget" value="{{targetIndex}}" range="{{targetOptions}}" range-key="name">
        <view class="picker-content">
          <text>{{targetOptions[targetIndex].name}}</text>
          <view class="arrow-icon"></view>
        </view>
      </picker>
    </view>

    <!-- 楼栋选择 -->
    <view class="form-item building-select" wx:if="{{formData.targetScope === 'specific_buildings'}}">
      <view class="building-select-btn" bindtap="selectBuildings">
        <text>选择发布对象</text>
        <view class="arrow-icon"></view>
      </view>
      <view class="selected-buildings" wx:if="{{selectedBuildings.length > 0}}">
        <text>已选择：</text>
        <text class="building-names">{{selectedBuildings.length}}个对象</text>
      </view>
      <view class="selected-buildings" wx:else>
        <text>请选择要发布公告的对象</text>
      </view>
    </view>

    <!-- 内容编辑区 -->
    <view class="form-item content-edit-area">
      <view class="form-label">公告内容</view>

      <!-- 格式工具栏 -->
      <view class="format-toolbar">
        <view class="toolbar-btn" bindtap="formatBold">
          <view class="toolbar-icon bold-icon"></view>
        </view>
        <view class="toolbar-btn" bindtap="formatItalic">
          <view class="toolbar-icon italic-icon"></view>
        </view>
        <view class="toolbar-btn" bindtap="formatUnderline">
          <view class="toolbar-icon underline-icon"></view>
        </view>
        <view class="toolbar-divider"></view>
        <view class="toolbar-btn" bindtap="formatList">
          <view class="toolbar-icon list-icon"></view>
        </view>
        <view class="toolbar-btn" bindtap="formatIndent">
          <view class="toolbar-icon indent-icon"></view>
        </view>
        <view class="toolbar-divider"></view>
        <view class="toolbar-btn" bindtap="clearFormat">
          <view class="toolbar-icon clear-icon"></view>
        </view>
      </view>

      <!-- 内容输入区 -->
      <textarea class="content-textarea" placeholder="请输入公告内容" value="{{formData.content}}" bindinput="inputContent" maxlength="1000"></textarea>
      <view class="char-count">{{formData.content.length || 0}}/1000</view>

      <!-- 图片上传区域 -->
      <view class="image-section">
        <view class="section-header">
          <text class="section-title">图片附件</text>
          <text class="section-subtitle">已上传 {{formData.images.length}}/9 张</text>
        </view>

        <view class="image-upload-area">
          <!-- 已上传图片 -->
          <view class="image-item" wx:for="{{formData.images}}" wx:key="index">
            <image class="uploaded-image" src="{{item}}" mode="aspectFill" bindtap="previewImage" data-index="{{index}}"></image>
            <view class="delete-icon" catchtap="deleteImage" data-index="{{index}}"></view>
          </view>

          <!-- 正在上传的图片 -->
          <view class="image-item" wx:for="{{uploadingImages}}" wx:key="index">
            <image class="uploading-image" src="{{item.path}}" mode="aspectFill"></image>
            <view class="upload-progress {{item.error ? 'error' : ''}}">
              <view class="progress-bar" style="width: {{item.progress}}%"></view>
              <text class="progress-text">{{item.error ? '上传失败' : item.progress + '%'}}</text>
            </view>
          </view>

          <!-- 添加图片按钮 -->
          <view class="image-item add-image" bindtap="chooseImage" wx:if="{{formData.images.length + uploadingImages.length < 9}}">
            <view class="add-icon">+</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 附加选项 -->
    <view class="form-item options-area">
      <view class="section-header">
        <text class="section-title">附加选项</text>
      </view>

      <view class="option-item">
        <view class="switch-label">
          <view class="option-icon pin-icon"></view>
          <text>置顶公告</text>
        </view>
        <switch class="custom-switch" checked="{{formData.isPinned}}" bindchange="togglePin" color="#ff8c00" />
      </view>

      <view class="option-item">
        <view class="switch-label">
          <view class="option-icon time-icon"></view>
          <text>定时发布</text>
        </view>
        <switch class="custom-switch" checked="{{formData.isScheduled}}" bindchange="toggleScheduled" color="#ff8c00" />
      </view>

      <!-- 定时发布时间选择 -->
      <view class="scheduled-time" wx:if="{{formData.isScheduled}}">
        <picker mode="multiSelector" bindchange="onDatePickerChange" bindcolumnchange="onDatePickerColumnChange" value="{{datePickerValue}}" range="{{[years, months, days, hours, minutes]}}">
          <view class="time-picker">
            <text class="time-value">{{formData.scheduledAt || '请选择发布时间'}}</text>
            <view class="arrow-icon"></view>
          </view>
        </picker>
      </view>
    </view>

    <!-- 底部操作区 -->
    <view class="bottom-actions">
      <view class="action-row">
        <view class="action-btn preview" bindtap="showPreview">预览</view>
        <view class="action-btn save-draft" bindtap="onSaveDraftTap">保存草稿</view>
      </view>
      <view class="action-btn publish" bindtap="onPublishTap">发布</view>
    </view>
  </view>
</view>
