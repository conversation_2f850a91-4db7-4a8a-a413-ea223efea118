<!--pages/payment/history/history.wxml-->
<view class="container {{darkMode ? 'darkMode' : ''}}">
  <!-- 内容容器 -->
  <view class="content-container">
    <!-- 统计卡片 -->
    <view class="stats-card">
      <view class="stats-item">
        <view class="stats-value">¥{{totalPaid}}</view>
        <view class="stats-label">已缴总额</view>
      </view>
      <view class="stats-divider"></view>
      <view class="stats-item">
        <view class="stats-value">¥{{totalUnpaid}}</view>
        <view class="stats-label">待缴总额</view>
      </view>
      <view class="stats-divider"></view>
      <view class="stats-item {{totalOverdue > 0 ? 'overdue' : ''}}">
        <view class="stats-value">¥{{totalOverdue}}</view>
        <view class="stats-label">逾期总额</view>
      </view>
    </view>

    <!-- 筛选区域 -->
    <view class="filter-section">
      <!-- 时间筛选 -->
      <view class="filter-group">
        <view class="filter-label">时间</view>
        <view class="filter-options">
          <view class="filter-option {{timeFilter === 'all' ? 'active' : ''}}" bindtap="changeTimeFilter" data-filter="all">全部</view>
          <view class="filter-option {{timeFilter === 'month3' ? 'active' : ''}}" bindtap="changeTimeFilter" data-filter="month3">近三个月</view>
          <view class="filter-option {{timeFilter === 'month6' ? 'active' : ''}}" bindtap="changeTimeFilter" data-filter="month6">近半年</view>
          <view class="filter-option {{timeFilter === 'year1' ? 'active' : ''}}" bindtap="changeTimeFilter" data-filter="year1">近一年</view>
        </view>
      </view>

      <!-- 类型筛选 -->
      <view class="filter-group">
        <view class="filter-label">类型</view>
        <view class="filter-options">
          <view class="filter-option {{typeFilter === 'all' ? 'active' : ''}}" bindtap="changeTypeFilter" data-filter="all">全部</view>
          <view class="filter-option {{typeFilter === 'property' ? 'active' : ''}}" bindtap="changeTypeFilter" data-filter="property">物业费</view>
          <view class="filter-option {{typeFilter === 'parking' ? 'active' : ''}}" bindtap="changeTypeFilter" data-filter="parking">停车费</view>
          <view class="filter-option {{typeFilter === 'utility' ? 'active' : ''}}" bindtap="changeTypeFilter" data-filter="utility">水电费</view>
        </view>
      </view>

      <!-- 状态筛选 -->
      <view class="filter-group">
        <view class="filter-label">状态</view>
        <view class="filter-options">
          <view class="filter-option {{statusFilter === 'all' ? 'active' : ''}}" bindtap="changeStatusFilter" data-filter="all">全部</view>
          <view class="filter-option {{statusFilter === 'paid' ? 'active' : ''}}" bindtap="changeStatusFilter" data-filter="paid">已缴费</view>
          <view class="filter-option {{statusFilter === 'unpaid' ? 'active' : ''}}" bindtap="changeStatusFilter" data-filter="unpaid">待缴费</view>
          <view class="filter-option {{statusFilter === 'overdue' ? 'active' : ''}}" bindtap="changeStatusFilter" data-filter="overdue">已逾期</view>
        </view>
      </view>

      <!-- 重置筛选按钮 -->
      <view class="reset-filter" bindtap="resetFilters">
        <view class="reset-icon"></view>
        <text>重置筛选</text>
      </view>
    </view>

    <!-- 缴费记录列表 -->
    <view class="payment-records">
      <!-- 加载中状态 -->
      <view class="loading-container" wx:if="{{isLoading}}">
        <view class="loading-spinner"></view>
        <view class="loading-text">加载中...</view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{showEmpty && !isLoading}}">
        <view class="empty-icon"></view>
        <view class="empty-text">{{emptyText}}</view>
        <view class="empty-action" bindtap="handleEmptyAction">{{emptyActionText}}</view>
      </view>

      <!-- 记录列表 -->
      <block wx:if="{{!isLoading && filteredRecords.length > 0}}">
        <view class="payment-card" wx:for="{{filteredRecords}}" wx:key="id" bindtap="viewPaymentDetail" data-id="{{item.id}}">
          <view class="payment-card-header">
            <view class="payment-title">{{item.title}}</view>
            <view class="payment-status {{item.status}}">
              {{item.status === 'paid' ? '已缴费' : (item.status === 'unpaid' ? '待缴费' : '已逾期')}}
            </view>
          </view>
          <view class="payment-card-body">
            <view class="payment-info">
              <view class="payment-period">{{item.period}}</view>
              <view class="payment-date" wx:if="{{item.paymentDate}}">
                支付时间：{{item.paymentDate}}
              </view>
              <view class="payment-date" wx:else>
                截止日期：{{item.dueDate || '暂无'}}
              </view>
              <view class="payment-method" wx:if="{{item.paymentMethod}}">
                支付方式：{{item.paymentMethod}}
              </view>
            </view>
            <view class="payment-amount">¥{{item.amount}}</view>
          </view>
          <view class="payment-card-footer" wx:if="{{item.status === 'paid'}}">
            <view class="payment-action invoice" wx:if="{{item.invoiceAvailable}}" catchtap="downloadInvoice" data-id="{{item.id}}">
              <view class="action-icon invoice-icon"></view>
              <text>查看发票</text>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view class="load-more" wx:if="{{hasMore}}">
          <view class="loading-spinner" wx:if="{{loadingMore}}"></view>
          <text wx:if="{{loadingMore}}">加载更多...</text>
          <text wx:else>上拉加载更多</text>
        </view>
        <view class="load-more" wx:else>
          <text>没有更多记录了</text>
        </view>
      </block>
    </view>
  </view>
</view>
