<!--扫码核销页面-->
<view class="container {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}">
  <!-- 扫码中 -->
  <view class="scanning-container" wx:if="{{!scanResult && !showOrderConfirm}}">
    <view class="scanning-icon"></view>
    <view class="scanning-text">正在扫描...</view>
    <view class="scanning-tip">请将订单二维码放入框内</view>
  </view>

  <!-- 订单确认 -->
  <view class="confirm-container" wx:elif="{{showOrderConfirm}}">
    <view class="confirm-icon"></view>
    <view class="confirm-title">确认完成订单</view>

    <view class="order-details">
      <view class="detail-item">
        <view class="detail-label">订单号</view>
        <view class="detail-value">{{orderInfo.orderNo}}</view>
      </view>
      <view class="detail-item">
        <view class="detail-label">商品</view>
        <view class="detail-value">{{orderInfo.stuffDescribe}}</view>
      </view>
      <view class="detail-item">
        <view class="detail-label">数量</view>
        <view class="detail-value">{{orderInfo.quantity}}</view>
      </view>
      <view class="detail-item">
        <view class="detail-label">金额</view>
        <view class="detail-value">¥{{orderInfo.totalAmount}}</view>
      </view>
      <view class="detail-item">
        <view class="detail-label">下单时间</view>
        <view class="detail-value">{{orderInfo.createTime}}</view>
      </view>
    </view>

    <view class="action-buttons">
      <view class="action-btn secondary" bindtap="cancelConfirm">取消</view>
      <view class="action-btn primary" bindtap="confirmCompleteOrder">确认完成</view>
    </view>
  </view>
  
  <!-- 验证成功 -->
  <view class="result-container success" wx:elif="{{verificationSuccess}}">
    <view class="result-icon success"></view>
    <view class="result-title">核销成功</view>
    <view class="result-time">{{orderInfo.verifyTime}}</view>
    
    <view class="order-card">
      <view class="order-header">
        <view class="order-id">订单号: {{orderInfo.orderSn}}</view>
        <view class="order-time">{{orderInfo.createTime}}</view>
      </view>
      <view class="order-content">
        <image class="buyer-avatar" src="{{orderInfo.buyerAvatar}}" mode="aspectFill"></image>
        <view class="order-info">
          <view class="buyer-name">{{orderInfo.buyerName}}</view>
          <view class="goods-title">{{orderInfo.goodsTitle}}</view>
          <view class="order-detail">
            <view class="order-quantity">数量: {{orderInfo.quantity}}</view>
            <view class="order-amount">金额: ¥{{orderInfo.totalAmount}}</view>
          </view>
        </view>
      </view>
    </view>
    
    <view class="action-buttons">
      <view class="action-btn secondary" bindtap="rescan">继续扫码</view>
      <view class="action-btn primary" bindtap="viewOrderDetail">查看详情</view>
    </view>
  </view>
  
  <!-- 验证失败 -->
  <view class="result-container error" wx:elif="{{verificationFailed}}">
    <view class="result-icon error"></view>
    <view class="result-title">核销失败</view>
    <view class="error-message">{{errorMessage}}</view>
    
    <view class="action-buttons">
      <view class="action-btn secondary" bindtap="backToList">返回列表</view>
      <view class="action-btn primary" bindtap="rescan">重新扫码</view>
    </view>
  </view>
</view>
