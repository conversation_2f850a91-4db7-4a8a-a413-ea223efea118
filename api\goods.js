
const REQUEST = require('../utils/request.js')

/**
 * 好物模块API接口
 * 基于接口文档：/users-api/v1/member/good-stuff 和 /users-api/v1/good-stuff
 */

// ==================== 我的好物管理 ====================

/**
 * 分页查询我的好物列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页码，默认1
 * @param {number} params.pageSize - 每页大小，默认10
 * @param {string} params.categoryCode - 分类代码（可选）
 * @param {string} params.type - 类型（可选）
 * @param {string} params.title - 标题（可选）
 * @returns {Promise} 我的好物列表
 */
function getMyGoodsList(params = {}) {
  const queryParams = {
    pageNum: params.pageNum || 1,
    pageSize: params.pageSize || 10
  }

  // 添加可选参数
  if (params.categoryCode) {
    queryParams.categoryCode = params.categoryCode
  }
  if (params.type) {
    queryParams.type = params.type
  }
  if (params.title) {
    queryParams.title = params.title
  }

  return REQUEST.request('/users-api/v1/member/good-stuff/page', 'GET', { search: queryParams }, true)
}


/**
 * 添加我的好物
 * @param {Object} data - 好物数据
 * @param {string} data.stuffDescribe - 商品描述
 * @param {string} data.categoryCode - 分类代码
 * @param {string} data.type - 类型
 * @param {number} data.amount - 价格
 * @param {number} data.stock - 库存
 * @param {number} data.points - 积分
 * @param {string} data.media - 图片，多个用逗号分隔
 * @param {number} data.lng - 经度
 * @param {number} data.lat - 纬度
 * @param {string} data.address - 地址
 * @returns {Promise} 添加结果
 */
function addMyGoods(data) {
  const requestData = {
    stuffDescribe: data.stuffDescribe,
    categoryCode: data.categoryCode,
    type: data.type,
    amount: data.amount || 0,
    stock: data.stock || 1,
    points: data.points || 0,
    media: data.media || '',
    lng: data.lng || 0.1,
    lat: data.lat || 0.1,
    address: data.address || ''
  }

  return REQUEST.request('/users-api/v1/member/good-stuff', 'POST', requestData, true)
}

/**
 * 修改我的好物
 * @param {Object} data - 好物数据（包含id）
 * @returns {Promise} 修改结果
 */
function updateMyGoods(data) {
  const requestData = {
    id: data.id,
    stuffDescribe: data.stuffDescribe,
    categoryCode: data.categoryCode,
    type: data.type,
    amount: data.amount || 0,
    stock: data.stock || 1,
    points: data.points || 0,
    media: data.media || '',
    lng: data.lng || 0.1,
    lat: data.lat || 0.1,
    address: data.address || ''
  }

  return REQUEST.request('/users-api/v1/member/good-stuff', 'PUT', requestData, true)
}

/**
 * 删除我的好物
 * @param {number} id - 好物ID
 * @returns {Promise} 删除结果
 */
function deleteMyGoods(id) {
  if (!id) {
    return Promise.reject(new Error('好物ID不能为空'))
  }
  return REQUEST.request('/users-api/v1/member/good-stuff?id=' + id, 'DELETE', {}, true)
}

// ==================== 平台好物浏览 ====================

/**
 * 分页查询平台好物列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页码，默认1
 * @param {number} params.pageSize - 每页大小，默认10
 * @param {string} params.categoryCode - 分类代码（可选）
 * @param {string} params.type - 类型（可选）
 * @param {string} params.keyword - 关键词（可选）
 * @param {number} params.lng - 经度（可选）
 * @param {number} params.lat - 纬度（可选）
 * @param {number} params.radius - 搜索半径（可选）
 * @returns {Promise} 平台好物列表
 */
function getPlatformGoodsList(params = {}) {
  const queryParams = {
    pageNum: params.pageNum || 1,
    pageSize: params.pageSize || 10
  }

  // 添加可选参数
  if (params.categoryCode) {
    queryParams.categoryCode = params.categoryCode
  }
  if (params.type) {
    queryParams.type = params.type
  }
  if (params.keyword) {
    queryParams.keyword = params.keyword
  }
  if (params.lng) {
    queryParams.lng = params.lng
  }
  if (params.lat) {
    queryParams.lat = params.lat
  }
  if (params.radius) {
    queryParams.radius = params.radius
  }

  return REQUEST.request('/users-api/v1/good-stuff/page', 'GET', { search: queryParams }, true)
}

/**
 * 通过ID查询平台好物详情
 * @param {number} id - 好物ID
 * @returns {Promise} 好物详情
 */
function getPlatformGoodsDetail(id) {
  if (!id) {
    return Promise.reject(new Error('好物ID不能为空'))
  }
  return REQUEST.request('/users-api/v1/good-stuff?id=' + id, 'GET', {}, true)
}

// ==================== 好物订单管理 ====================

/**
 * 分页查询我的好物订单
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页码，默认1
 * @param {number} params.pageSize - 每页大小，默认10
 * @returns {Promise} 订单列表
 */
function getMyGoodsOrderList(params = {}) {

  return REQUEST.request('/users-api/v1/member/good-stuff/order/page', 'GET', params, true)
}

/**
 * 通过ID查询我的好物订单详情
 * @param {number} id - 订单ID
 * @returns {Promise} 订单详情
 */
function getMyGoodsOrderDetail(id) {
  if (!id) {
    return Promise.reject(new Error('订单ID不能为空'))
  }
  return REQUEST.request('/users-api/v1/member/good-stuff/order?id=' + id, 'GET', {}, true)
}

/**
 * 新增好物订单
 * @param {Object} orderData - 订单数据
 * @returns {Promise} 创建结果
 */
function createGoodsOrder(orderData) {
  return REQUEST.request('/users-api/v1/member/good-stuff/order', 'POST', orderData, true)
}

/**
 * 修改好物订单
 * @param {Object} orderData - 订单数据（包含id）
 * @returns {Promise} 修改结果
 */
function updateGoodsOrder(orderData) {
  return REQUEST.request('/users-api/v1/member/good-stuff/order', 'PUT', orderData, true)
}

/**
 * 删除好物订单
 * @param {number} id - 订单ID
 * @returns {Promise} 删除结果
 */
function deleteGoodsOrder(id) {
  if (!id) {
    return Promise.reject(new Error('订单ID不能为空'))
  }
  return REQUEST.request('/users-api/v1/member/good-stuff/order?id=' + id, 'DELETE', {}, true)
}

// ==================== 好物收藏管理 ====================

/**
 * 分页查询我收藏的好物
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页码，默认1
 * @param {number} params.pageSize - 每页大小，默认10
 * @returns {Promise} 收藏列表
 */
function getMyCollectedGoodsList(params = {}) {
  const queryParams = {
    pageNum: params.pageNum || 1,
    pageSize: params.pageSize || 10
  }

  return REQUEST.request('/users-api/v1/member/good-stuff/collect/page', 'GET', { search: queryParams }, true)
}

/**
 * 收藏好物
 * @param {number} goodStuffId - 好物ID
 * @returns {Promise} 收藏结果
 */
function collectGoods(goodStuffId) {
  if (!goodStuffId) {
    return Promise.reject(new Error('好物ID不能为空'))
  }
  return REQUEST.request('/users-api/v1/member/good-stuff/collect' , 'POST', {goodStuffId}, true)
}

/**
 * 取消收藏好物
 * @param {number} id - 收藏记录ID
 * @returns {Promise} 取消收藏结果
 */
function uncollectGoods(id) {
  if (!id) {
    return Promise.reject(new Error('收藏记录ID不能为空'))
  }

  return REQUEST.request('/users-api/v1/member/good-stuff/collect?goodStuffId=' + id, 'DELETE', {}, true)
}

/**
 * 取消我的好物订单
 * @param {Object} orderData - 订单数据，包含id和note
 * @returns {Promise} 取消订单结果
 */
function cancelMyGoodsOrder(orderData) {
  if (!orderData || !orderData.id) {
    return Promise.reject(new Error('订单ID不能为空'))
  }
  return REQUEST.request('/users-api/v1/member/good-stuff/order/cancel', 'PUT', orderData, true)
}

/**
 * 完成我的好物订单
 * @param {Object} orderData - 订单数据，包含id和note
 * @returns {Promise} 完成订单结果
 */
function completeMyGoodsOrder(orderData) {
  if (!orderData || !orderData.id) {
    return Promise.reject(new Error('订单ID不能为空'))
  }
  return REQUEST.request('/users-api/v1/member/good-stuff/order/complete', 'PUT', orderData, true)
}

module.exports = {
  // 我的好物管理
  getMyGoodsList,
  addMyGoods,
  updateMyGoods,
  deleteMyGoods,

  // 平台好物浏览
  getPlatformGoodsList,
  getPlatformGoodsDetail,

  // 好物订单管理
  getMyGoodsOrderList,
  getMyGoodsOrderDetail,
  createGoodsOrder,
  updateGoodsOrder,
  deleteGoodsOrder,
  cancelMyGoodsOrder,
  completeMyGoodsOrder,

  // 好物收藏管理
  getMyCollectedGoodsList,
  collectGoods,
  uncollectGoods,

  
}


