// pages/payment/detail/detail.js
const util = require('../../../utils/util.js')

Page({
  data: {
    darkMode: false,
    isLoading: true,

    // 缴费详情数据
    paymentId: '',
    paymentDetail: null,

    // 账单明细是否展开
    billDetailsExpanded: false,

    // 发票状态
    invoiceAvailable: false,

    // 分享选项是否显示
    showShareOptions: false
  },

  onLoad: function (options) {
    // 获取缴费ID
    if (options.id) {
      this.setData({
        paymentId: options.id
      })
      this.loadPaymentDetail(options.id)
    } else {
      wx.showToast({
        title: '缴费ID无效',
        icon: 'error'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },



  // 加载缴费详情
  loadPaymentDetail: function (id) {
    this.setData({
      isLoading: true
    })

    // 模拟加载数据
    setTimeout(() => {
      // 根据ID获取不同的详情数据
      let detail = this.getMockPaymentDetail(id)

      this.setData({
        paymentDetail: detail,
        invoiceAvailable: detail.status === 'paid',
        isLoading: false
      })
    }, 1000)
  },

  // 获取模拟的缴费详情数据
  getMockPaymentDetail: function (id) {
    // 基础数据
    const details = {
      // 物业费详情
      '1001': {
        id: '1001',
        title: '物业管理费',
        type: 'property',
        amount: '720.00',
        period: '2023年10月-12月',
        status: 'paid',
        paymentDate: '2023-12-26 18:30',
        paymentMethod: '微信支付',
        dueDate: '2023-12-31',
        invoiceNo: '***************',
        paymentAccount: '微信支付 (尾号 8765)',
        transactionId: '4200001234202312260123456789',
        billDetails: [
          {
            name: '基础物业服务费',
            unit: '元/平方米/月',
            unitPrice: '2.00',
            area: '120',
            months: '3',
            amount: '720.00'
          }
        ],
        propertyInfo: {
          community: '星河湾',
          building: '2栋',
          unit: '3单元',
          room: '1801',
          area: '120平方米'
        },
        contact: {
          name: '张先生',
          phone: '138****6789'
        },
        servicePeriod: {
          start: '2023-10-01',
          end: '2023-12-31'
        }
      },

      // 停车费详情
      '1002': {
        id: '1002',
        title: '地下停车费',
        type: 'parking',
        amount: '450.00',
        period: '2023年第四季度',
        status: 'paid',
        paymentDate: '2023-12-26 18:30',
        paymentMethod: '微信支付',
        dueDate: '2023-12-31',
        invoiceNo: '***************',
        paymentAccount: '微信支付 (尾号 8765)',
        transactionId: '4200001234202312260123456790',
        billDetails: [
          {
            name: '地下停车位租赁费',
            unit: '元/月',
            unitPrice: '150.00',
            quantity: '1',
            months: '3',
            amount: '450.00'
          }
        ],
        parkingInfo: {
          community: '星河湾',
          parkingNo: 'B2-168',
          type: '固定车位',
          carNumber: '粤B12345'
        },
        contact: {
          name: '张先生',
          phone: '138****6789'
        },
        servicePeriod: {
          start: '2023-10-01',
          end: '2023-12-31'
        }
      },

      // 水电费详情
      '1003': {
        id: '1003',
        title: '水电费',
        type: 'utility',
        amount: '88.50',
        period: '2023年11月',
        status: 'paid',
        paymentDate: '2023-12-26 18:30',
        paymentMethod: '微信支付',
        dueDate: '2023-12-31',
        invoiceNo: '***************',
        paymentAccount: '微信支付 (尾号 8765)',
        transactionId: '4200001234202312260123456791',
        billDetails: [
          {
            name: '水费',
            unit: '元/吨',
            unitPrice: '3.50',
            previousReading: '1024',
            currentReading: '1032',
            usage: '8',
            amount: '28.00'
          },
          {
            name: '电费',
            unit: '元/度',
            unitPrice: '0.55',
            previousReading: '2450',
            currentReading: '2560',
            usage: '110',
            amount: '60.50'
          }
        ],
        propertyInfo: {
          community: '星河湾',
          building: '2栋',
          unit: '3单元',
          room: '1801'
        },
        contact: {
          name: '张先生',
          phone: '138****6789'
        },
        meterReadingDate: '2023-11-30',
        servicePeriod: {
          start: '2023-11-01',
          end: '2023-11-30'
        }
      }
    }

    // 返回对应ID的详情，如果没有则返回默认数据
    return details[id] || details['1001']
  },

  // 切换账单明细展开/收起
  toggleBillDetails: function () {
    this.setData({
      billDetailsExpanded: !this.data.billDetailsExpanded
    })
  },

  // 查看发票
  viewInvoice: function () {
    if (!this.data.invoiceAvailable) {
      wx.showToast({
        title: '发票暂不可用',
        icon: 'none'
      })
      return
    }

    const id = this.data.paymentDetail.id
    wx.navigateTo({
      url: `/pages/payment/invoice/invoice?id=${id}`
    })
  },

  // 显示分享选项
  showShareOptions: function () {
    this.setData({
      showShareOptions: true
    })
  },

  // 隐藏分享选项
  hideShareOptions: function () {
    this.setData({
      showShareOptions: false
    })
  },

  // 分享到微信
  shareToWechat: function () {
    this.hideShareOptions()
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  // 保存截图
  saveScreenshot: function () {
    this.hideShareOptions()
    wx.showLoading({
      title: '生成截图中...',
      mask: true
    })

    // 模拟截图生成过程
    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      })
    }, 1500)
  },

  // 返回上一页
  navigateBack: function () {
    wx.navigateBack()
  },

  // 跳转到缴费设置页面
  navigateToSettings: function () {
    wx.navigateTo({
      url: '/pages/payment/settings/settings'
    })
  },

  // 跳转到缴费分析页面
  navigateToAnalysis: function () {
    wx.navigateTo({
      url: '/pages/payment/analysis/analysis'
    })
  },

  // 分享给好友
  onShareAppMessage: function () {
    const detail = this.data.paymentDetail
    return {
      title: `${detail.title}缴费凭证`,
      path: `/pages/payment/detail/detail?id=${detail.id}`,
      imageUrl: '/images/share-payment.png' // 默认分享图片
    }
  }
})
